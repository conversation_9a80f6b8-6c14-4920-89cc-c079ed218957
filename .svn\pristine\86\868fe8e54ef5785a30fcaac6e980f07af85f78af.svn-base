<?php
//文件地址
namespace app\home\route;
//使用Route对象
use think\facade\Route;

Route::get('/', 'Index/index');
//首页提交表单
Route::post('submit-feedback', 'Index/submitFeedback');

// 发送验证码
Route::post('send_captcha', 'Captcha/send');
// 验证验证码
Route::post('verify_captcha', 'Captcha/verify');

//注册
Route::rule('login/register', 'Login/register');
//登录
Route::rule('login', 'Login/index');
//退出登录
Route::rule('logout', 'Login/logout');

Route::rule('clear-message', 'User/clearMessage');

//产品详情页
Route::get('product/<url>/detail', 'Product/detail')
    ->pattern([
        'url' => '[-\w\x{4e00}-\x{9fa5}]+', // 允许中文、字母、数字、下划线和连字符
    ]);
//产品+服务页面
Route::get('product/<url>', 'Product/service')
    ->pattern([
        'url' => '[-\w\x{4e00}-\x{9fa5}]+', // 允许中文、字母、数字、下划线和连字符
    ]);
//产品列表页
Route::get('product', 'Product/index');


//服务详情页
Route::get('service/<url>', 'Service/index')
    ->pattern([
        'url' => '[-\w\x{4e00}-\x{9fa5}]+', // 允许中文、字母、数字、下划线和连字符
    ]);

//quote咨询
Route::rule('quote/logged', 'Quote/logged');
Route::rule('quote', 'Quote/index');

Route::get('getServices/:product_id', 'Quote/getServices');

//FAQ搜索页
Route::rule('faq/result', 'Faq/results');
//FAQ详情页
Route::get('faq/<url>', 'Faq/details')
    ->pattern([
        'url' => '[-\w\x{4e00}-\x{9fa5}]+', // 允许中文、字母、数字、下划线和连字符
    ]);

//FAQ总页
Route::get('faq', 'Faq/index');
Route::post('ask-question', 'Faq/askQuestion');

//订单详情页
Route::get('user/order-detail-<id>', 'User/order_detail')
    ->pattern([
        'id' => '\d+', // 允许数字
    ]);

Route::get('user/private-message', 'User/private_message');

//上传用户资料
Route::post('upload/userfile', 'User/upload_userfile');

//新闻页面
Route::get('news/year/:year', 'News/index');
Route::get('news/<url>', 'News/details')
    ->pattern([
        'url' => '[-\w\x{4e00}-\x{9fa5}]+', // 允许中文、字母、数字、下划线和连字符
    ]);
Route::get('news', 'News/index');

//关于我们
Route::get('about', 'About/index');
//联系我们
Route::get('contact', 'Contact/index');
//隐私条款
Route::get('privacy/terms', 'Privacy/terms');
Route::get('privacy', 'Privacy/index');

//资源页面
Route::get('resources', 'Resources/index');

//论坛
Route::get('iCommunity/news/:id', 'Forum/post_detail')
    ->name('news')
    ->pattern([
        'id' => '\d+', // 允许数字
    ]);
Route::get('iCommunity/post/:id', 'Forum/post_detail')
    ->name('post')
    ->pattern([
        'id' => '\d+', // 允许数字
    ]);
Route::post('iCommunity/reply', 'Forum/reply');
Route::rule('iCommunity/post', 'Forum/post');
Route::get('iCommunity/tag', 'Forum/tag');
Route::get('iCommunity/message', 'User/private');
Route::get('iCommunity/user/<id>', 'Forum/user')
    ->pattern([
        'id' => '\d+', // 允许数字
    ]);
Route::get('iCommunity/topic/<name>', 'Forum/topic')
    ->pattern([
        'name' => '[-\w\x{4e00}-\x{9fa5}]+', // 允许中文、字母、数字、下划线和连字符
    ]);
Route::get('iCommunity', 'Forum/index');

//保存私信
Route::post('message/save', 'User/saveMessage');
Route::post('message/upload', 'User/uploadMessageFile');
Route::post('message/delete', 'User/deleteMessage');

//下载日志
Route::post('download/log', 'User/downloadLog');
?>
