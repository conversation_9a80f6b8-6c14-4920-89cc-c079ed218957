<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>Chat Application</title>

    <link rel="stylesheet" href="__CSS__/private.css">
</head>
<body>
    <div class="sidebar">
        <div class="sidebar-header">
            <div class="desktop-header">
                <a rel="nofollow" href="javascript:history.go(-1)" class="action-btn">
                    <svg t="1752138927647" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4498" width="20" height="20"><path d="M719.4 959.8c-12.8 0-25.6-4.7-35.5-14.2L269.2 547c-9.6-9.2-15-21.8-15-35 0-13.1 5.4-25.7 15-35L683.9 78.3c19.8-19.1 51.8-18.9 71.4 0.4 19.6 19.3 19.4 50.4-0.4 69.5L376.5 512l378.4 363.7c19.8 19.1 20 50.2 0.4 69.5-9.9 9.8-22.9 14.6-35.9 14.6z" p-id="4499" fill="#707070"></path></svg>
                </a>
            </div>
            <div class="mobile-header">
                <button class="mobile-close-btn">
                    <svg t="1752200314767" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="18451" width="20" height="20"><path d="M585.412525 512.594747L973.601616 124.418586c19.600808-19.600808 19.600808-51.898182 0-71.49899l-2.120404-2.120404c-19.600808-19.600808-51.898182-19.600808-71.49899 0L511.793131 439.518384 123.61697 50.799192c-19.600808-19.600808-51.898182-19.600808-71.49899 0l-2.120404 2.120404c-20.11798 19.600808-20.11798 51.898182 0 71.49899l388.189091 388.189091L49.997576 900.783838c-19.587879 19.600808-19.587879 51.898182 0 71.49899l2.120404 2.120404c19.600808 19.600808 51.898182 19.600808 71.49899 0L511.793131 586.214141l388.189091 388.176162c19.600808 19.600808 51.898182 19.600808 71.49899 0l2.120404-2.120404c19.600808-19.600808 19.600808-51.898182 0-71.49899L585.412525 512.594747z m0 0" p-id="18452"></path></svg>
                </button>
                <span class="mobile-header-title">Chat list</span>
            </div>
        </div>

        <!-- 左侧会话列表 -->
        <div class="chat-list">
            {volist name="conversations" id="vo"}
            <div class="chat-item active" data-user="{$vo.other_user.first_name}_{$vo.other_user.last_name}">
                <div class="chat-item-header">
                    <div class="chat-avatar">
                        <img src="{$vo.other_user.avatar ?? '__IMG__/user-1.jpg'}">
                    </div>
                    <span class="chat-name">{$vo.other_user.first_name} {$vo.other_user.last_name}</span>
                    <span class="chat-time">
                        {if $vo.last_message}
                        {:date('M d, Y, H:i', strtotime($vo.last_message.create_time))}
                        {/if}
                    </span>
                </div>
                {if $vo.last_message}
                <div class="chat-preview wrap-anywhere line-clamp-2">{$vo.last_message.content}</div>
                {/if}
            </div>
            {/volist}
        </div>
    </div>

    <div class="mobile-backdrop"></div>

    <!-- 搜索历史记录模态框 -->
    <div class="search-modal" id="searchModal">
        <div class="search-modal-content">
            <div class="search-header">
                <h3 id="searchTitle">Search History</h3>
                <button class="search-close-btn" id="searchCloseBtn">
                    <svg t="1752200314767" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="18451" width="20" height="20"><path d="M585.412525 512.594747L973.601616 124.418586c19.600808-19.600808 19.600808-51.898182 0-71.49899l-2.120404-2.120404c-19.600808-19.600808-51.898182-19.600808-71.49899 0L511.793131 439.518384 123.61697 50.799192c-19.600808-19.600808-51.898182-19.600808-71.49899 0l-2.120404 2.120404c-20.11798 19.600808-20.11798 51.898182 0 71.49899l388.189091 388.189091L49.997576 900.783838c-19.587879 19.600808-19.587879 51.898182 0 71.49899l2.120404 2.120404c19.600808 19.600808 51.898182 19.600808 71.49899 0L511.793131 586.214141l388.189091 388.176162c19.600808 19.600808 51.898182 19.600808 71.49899 0l2.120404-2.120404c19.600808-19.600808 19.600808-51.898182 0-71.49899L585.412525 512.594747z m0 0" p-id="18452"></path></svg>
                </button>
            </div>

            <div class="search-input-container">
                <input type="text" id="searchInput" class="search-input" placeholder="Search message content...">
                <button class="search-btn" id="searchBtn">
                    <svg t="1752140705320" class="icon" viewBox="0 0 1025 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4000" width="20" height="20"><path d="M104.385155 468.298759a364.034819 364.034819 0 1 1 728.069637-9.640482 364.034819 364.034819 0 0 1-728.069637 9.640482z m905.77869 468.459149L832.62542 759.13417A467.008811 467.008811 0 0 0 759.59664 102.472346a467.264753 467.264753 0 0 0-656.661823 73.114094 467.094125 467.094125 0 0 0 657.429649 656.064626l177.45311 177.45311a51.188397 51.188397 0 0 0 74.223177-70.46936l-1.876908-1.876908z" fill="#ffffff" p-id="4001"></path></svg>
                </button>
                <button class="clear-messages-btn" id="clearMessagesBtn">
                    <svg t="1752139972311" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2641" width="20" height="20"><path d="M202.666667 256h-42.666667a32 32 0 0 1 0-64h704a32 32 0 0 1 0 64H266.666667v565.333333a53.333333 53.333333 0 0 0 53.333333 53.333334h384a53.333333 53.333333 0 0 0 53.333333-53.333334V352a32 32 0 0 1 64 0v469.333333c0 64.8-52.533333 117.333333-117.333333 117.333334H320c-64.8 0-117.333333-52.533333-117.333333-117.333334V256z m224-106.666667a32 32 0 0 1 0-64h170.666666a32 32 0 0 1 0 64H426.666667z m-32 288a32 32 0 0 1 64 0v256a32 32 0 0 1-64 0V437.333333z m170.666666 0a32 32 0 0 1 64 0v256a32 32 0 0 1-64 0V437.333333z" fill="#ffffff" p-id="2642"></path></svg>
                </button>
            </div>

            <div class="search-results" id="searchResults">
                <div class="search-placeholder">
                    <div class="search-placeholder-icon">
                        <svg t="1752200487910" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="21369" width="20" height="20"><path d="M778.24 61.44a122.88 122.88 0 0 1 122.88 122.88v655.36a122.88 122.88 0 0 1-122.88 122.88H245.76a122.88 122.88 0 0 1-122.88-122.88V184.32a122.88 122.88 0 0 1 122.88-122.88h532.48z m0 61.44H245.76a61.44 61.44 0 0 0-61.3376 57.83552L184.32 184.32v655.36a61.44 61.44 0 0 0 57.83552 61.3376L245.76 901.12h532.48a61.44 61.44 0 0 0 61.3376-57.83552L839.68 839.68V184.32a61.44 61.44 0 0 0-57.83552-61.3376L778.24 122.88zM563.2 532.48a30.72 30.72 0 0 1 0 61.44h-266.24a30.72 30.72 0 0 1 0-61.44h266.24z m163.84-225.28a30.72 30.72 0 0 1 0 61.44h-430.08a30.72 30.72 0 0 1 0-61.44h430.08z" fill="#131415" p-id="21370"></path></svg>
                    </div>
                    <div class="search-placeholder-text">Loading history...</div>
                </div>
            </div>
        </div>
    </div>

    <!-- 图片预览模态框 -->
    <div class="image-modal" id="imageModal">
        <div class="image-modal-info" id="imageModalInfo">Image preview</div>
        <button class="image-modal-close" id="imageModalClose">
            <svg t="1752200314767" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="18451" width="20" height="20"><path d="M585.412525 512.594747L973.601616 124.418586c19.600808-19.600808 19.600808-51.898182 0-71.49899l-2.120404-2.120404c-19.600808-19.600808-51.898182-19.600808-71.49899 0L511.793131 439.518384 123.61697 50.799192c-19.600808-19.600808-51.898182-19.600808-71.49899 0l-2.120404 2.120404c-20.11798 19.600808-20.11798 51.898182 0 71.49899l388.189091 388.189091L49.997576 900.783838c-19.587879 19.600808-19.587879 51.898182 0 71.49899l2.120404 2.120404c19.600808 19.600808 51.898182 19.600808 71.49899 0L511.793131 586.214141l388.189091 388.176162c19.600808 19.600808 51.898182 19.600808 71.49899 0l2.120404-2.120404c19.600808-19.600808 19.600808-51.898182 0-71.49899L585.412525 512.594747z m0 0" p-id="18452"></path></svg>
        </button>

        <div class="image-modal-content">
            <img id="modalImage" src="" alt="Preview image">
        </div>

        <div class="image-modal-controls">
            <button class="image-control-btn" id="zoomOutBtn" title="Zoom out">
                <svg t="1752200172098" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="14593" id="mx_n_1752200172101" width="20" height="20"><path d="M919.264 905.984l-138.912-138.912C851.808 692.32 896 591.328 896 480c0-229.376-186.624-416-416-416S64 250.624 64 480s186.624 416 416 416c95.008 0 182.432-32.384 252.544-86.208l141.44 141.44a31.904 31.904 0 0 0 45.248 0 32 32 0 0 0 0.032-45.248zM128 480C128 285.92 285.92 128 480 128s352 157.92 352 352-157.92 352-352 352S128 674.08 128 480z" p-id="14594" fill="#ffffff"></path><path d="M625.792 448H336a32 32 0 0 0 0 64h289.792a32 32 0 1 0 0-64z" p-id="14595" fill="#ffffff"></path></svg>
            </button>
            <button class="image-control-btn" id="zoomInBtn" title="Zoom in">
                <svg t="1752200138485" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="13266" width="20" height="20"><path d="M975.37592889 960.26624L817.32494222 802.21525333C898.62599111 717.16408889 948.90666667 602.25763555 948.90666667 475.59111111c0-260.97891555-212.33664-473.31555555-473.31555556-473.31555556S2.27555555 214.61219555 2.27555555 475.59111111s212.33664 473.31555555 473.31555556 473.31555556c108.09799111 0 207.56707555-36.84579555 287.33895111-98.08554667l160.92728889 160.92728889a36.29966222 36.29966222 0 0 0 51.48216889 0 36.40888889 36.40888889 0 0 0 0.03640889-51.48216889zM75.09333333 475.59111111c0-220.81991111 179.67786667-400.49777778 400.49777778-400.49777778s400.49777778 179.67786667 400.49777778 400.49777778-179.67786667 400.49777778-400.49777778 400.49777778-400.49777778-179.67786667-400.49777778-400.49777778z" fill="#ffffff" p-id="13267"></path><path d="M641.47000889 439.18222222H512V311.75111111a36.40888889 36.40888889 0 0 0-72.81777778 0v127.43111111H311.75111111a36.40888889 36.40888889 0 0 0 0 72.81777778h127.43111111v127.43111111a36.40888889 36.40888889 0 1 0 72.81777778 0V512h129.47000889a36.40888889 36.40888889 0 1 0 0-72.81777778z" fill="#ffffff" p-id="13268"></path></svg>
            </button>
            <button class="image-control-btn" id="resetZoomBtn" title="Reset zoom">
                <svg t="1752200207393" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="15911" id="mx_n_1752200207393" width="20" height="20"><path d="M192 631.3c-6.9 0-13.8-2.9-18.8-8.5l-67.7-76.7c-9.1-10.4-8.1-26.2 2.2-35.3 10.4-9.1 26.2-8.1 35.3 2.2l67.7 76.7c9.1 10.4 8.1 26.2-2.2 35.3-4.7 4.3-10.6 6.3-16.5 6.3z m7.3 5.3c-7 0-14-2.9-18.9-8.7-9-10.5-7.9-26.2 2.6-35.3l81.7-70.5c10.5-9 26.2-7.9 35.3 2.6s7.9 26.2-2.6 35.3l-81.7 70.5c-4.8 4.1-10.6 6.1-16.4 6.1z m333.1 273.2c-16.6 0-30-13.4-30-30s13.4-30 30-30c164.9 0 299.1-134.2 299.1-299.1S697.3 251.6 532.4 251.6c-164.9 0-299.1 134.2-299.1 299.1 0 16.6-13.4 30-30 30s-30-13.4-30-30c0-48.5 9.5-95.5 28.2-139.8 18.1-42.8 44-81.2 76.9-114.1 33-33 71.4-58.9 114.1-76.9 44.3-18.7 91.3-28.2 139.8-28.2s95.5 9.5 139.8 28.2c42.8 18.1 81.2 44 114.1 76.9 33 33 58.9 71.4 76.9 114.1 18.7 44.3 28.2 91.3 28.2 139.8 0 48.5-9.5 95.5-28.2 139.8-18.1 42.8-44 81.2-76.9 114.1-33 33-71.4 58.9-114.1 76.9-44.2 18.8-91.2 28.3-139.7 28.3z" fill="#ffffff" p-id="15912"></path></svg>
            </button>
            <button class="image-control-btn" id="downloadImageBtn" title="Download">
                <svg t="1752200226939" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="16993" width="20" height="20"><path d="M853.333333 853.333333a42.666667 42.666667 0 0 1 0 85.333334H170.666667a42.666667 42.666667 0 0 1 0-85.333334h682.666666zM512 85.504a42.666667 42.666667 0 0 1 42.666667 42.666667v515.370666l204.373333-204.373333a42.666667 42.666667 0 0 1 63.914667 56.277333l-3.584 4.010667-277.376 277.546667a42.666667 42.666667 0 0 1-56.32 3.584l-4.010667-3.541334-277.12-276.650666a42.666667 42.666667 0 0 1 56.234667-63.957334l4.010666 3.541334L469.333333 644.096V128.170667a42.666667 42.666667 0 0 1 42.666667-42.666667z" fill="#ffffff" p-id="16994"></path></svg>
            </button>
        </div>
    </div>

    <div class="main-chat">
        <div class="chat-header">
            <button class="mobile-menu-btn">
                <svg t="1752200630868" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="22635" width="20" height="20"><path d="M192.037 287.953h640.124c17.673 0 32-14.327 32-32s-14.327-32-32-32H192.037c-17.673 0-32 14.327-32 32s14.327 32 32 32zM192.028 543.17h638.608c17.673 0 32-14.327 32-32s-14.327-32-32-32H192.028c-17.673 0-32 14.327-32 32s14.327 32 32 32zM832.161 735.802H192.037c-17.673 0-32 14.327-32 32s14.327 32 32 32h640.124c17.673 0 32-14.327 32-32s-14.327-32-32-32z" fill="" p-id="22636"></path></svg>
            </button>
            <div class="chat-avatar">
                <img src="{$conversation_user.avatar ?? '__IMG__/user-1.jpg'}">
            </div>
            <div class="chat-header-info">
                <h2>{$conversation_user.first_name} {$conversation_user.last_name}</h2>
            </div>
            <div class="chat-header-actions">
                <button class="action-btn" data-btn="history">
                    <svg t="1752139942435" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1536" width="20" height="20"><path d="M822.496 473.152l52.053333 29.290667C869.461333 306.56 709.098667 149.333333 512 149.333333c-200.298667 0-362.666667 162.368-362.666667 362.666667s162.368 362.666667 362.666667 362.666667c122.538667 0 234.645333-61.194667 301.578667-161.152a32 32 0 1 1 53.173333 35.616C788.064 866.634667 656.117333 938.666667 512 938.666667 276.362667 938.666667 85.333333 747.637333 85.333333 512S276.362667 85.333333 512 85.333333s426.666667 191.029333 426.666667 426.666667c0 10.954667-0.853333 26.357333-2.517334 46.528-1.930667 23.242667-27.274667 36.682667-47.594666 25.248l-97.450667-54.848a32 32 0 1 1 31.392-55.786667z m-493.12 176.213333L480 498.762667V320a32 32 0 0 1 64 0v192a32 32 0 0 1-9.376 22.624l-160 160a32 32 0 1 1-45.248-45.248z" fill="#000000" p-id="1537"></path></svg>
                </button>
                <button class="action-btn" data-btn="delete">
                    <svg t="1752139972311" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2641" width="20" height="20"><path d="M202.666667 256h-42.666667a32 32 0 0 1 0-64h704a32 32 0 0 1 0 64H266.666667v565.333333a53.333333 53.333333 0 0 0 53.333333 53.333334h384a53.333333 53.333333 0 0 0 53.333333-53.333334V352a32 32 0 0 1 64 0v469.333333c0 64.8-52.533333 117.333333-117.333333 117.333334H320c-64.8 0-117.333333-52.533333-117.333333-117.333334V256z m224-106.666667a32 32 0 0 1 0-64h170.666666a32 32 0 0 1 0 64H426.666667z m-32 288a32 32 0 0 1 64 0v256a32 32 0 0 1-64 0V437.333333z m170.666666 0a32 32 0 0 1 64 0v256a32 32 0 0 1-64 0V437.333333z" fill="#000000" p-id="2642"></path></svg>
                </button>
                <a rel="nofollow" href="javascript:history.go(-1)" class="action-btn" data-btn="back">
                    <svg t="1752138927647" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4498" width="20" height="20"><path d="M719.4 959.8c-12.8 0-25.6-4.7-35.5-14.2L269.2 547c-9.6-9.2-15-21.8-15-35 0-13.1 5.4-25.7 15-35L683.9 78.3c19.8-19.1 51.8-18.9 71.4 0.4 19.6 19.3 19.4 50.4-0.4 69.5L376.5 512l378.4 363.7c19.8 19.1 20 50.2 0.4 69.5-9.9 9.8-22.9 14.6-35.9 14.6z" p-id="4499" fill="#707070"></path></svg>
                </a>
            </div>
        </div>

        <!-- 右侧消息列表 -->
        <div class="messages-container"></div>

        <div class="refresh-notice">
            <p><svg t="1752198709050" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="7883" id="mx_n_1752198709051" width="20" height="20"  style="vertical-align: middle;"><path d="M1001.661867 796.544c48.896 84.906667 7.68 157.013333-87.552 157.013333H110.781867c-97.834667 0-139.050667-69.504-90.112-157.013333l401.664-666.88c48.896-87.552 128.725333-87.552 177.664 0l401.664 666.88zM479.165867 296.533333v341.333334a32 32 0 1 0 64 0v-341.333334a32 32 0 1 0-64 0z m0 469.333334v42.666666a32 32 0 1 0 64 0v-42.666666a32 32 0 1 0-64 0z" fill="#FAAD14" p-id="7884"></path></svg> You need to refresh the page to view new messages each time.</p>
            <button class="refresh-btn">
                <svg t="1752198529924" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="6541" width="20" height="20" style="vertical-align: middle;"><path d="M929.072167 349.757173L801.605308 287.849318 737.6181 412.031371l51.121205 26.318359 20.561251-39.886349c17.585482 39.633593 27.302778 82.497758 27.302778 125.896087 0 175.557037-147.578878 318.387784-328.978983 318.387784-54.70277 0-108.914354-13.256905-156.776336-38.426094l-26.740984 50.899148c56.064788 29.491626 119.516809 45.081664 183.51732 45.081665 213.140952 0 386.506072-168.67327 386.506072-375.942503 0-48.594668-9.998705-96.767734-28.706751-141.569014l38.536611 18.707024 25.111884-51.740305zM214.684095 625.506943c-17.598785-39.63257-27.317104-82.495711-27.317105-125.89404 0-175.556014 147.579902-318.418483 328.967726-318.418483 54.716073 0 108.927657 13.286581 156.791686 38.426094l26.739961-50.868449c-56.064788-29.522325-119.490203-45.055059-183.531647-45.055059-213.10002 0-386.492769 168.619035-386.492769 375.914874 0 48.592621 10.014054 96.766711 28.720055 141.56799l-38.522285-18.704977-25.111884 51.766911 127.465835 61.851573 63.987209-124.1534-51.121206-26.348036-20.575576 39.915002z" fill="#ffffff" p-id="6542"></path></svg> Refresh</button>
        </div>

        <div class="upload-preview" id="uploadPreview"></div>

        <div class="input-area">
            <div class="input-actions">
                <button class="input-btn" id="imageUploadBtn">
                    <svg t="1752140916159" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1613" width="20" height="20"><path d="M938.666667 553.92V768c0 64.8-52.533333 117.333333-117.333334 117.333333H202.666667c-64.8 0-117.333333-52.533333-117.333334-117.333333V256c0-64.8 52.533333-117.333333 117.333334-117.333333h618.666666c64.8 0 117.333333 52.533333 117.333334 117.333333v297.92z m-64-74.624V256a53.333333 53.333333 0 0 0-53.333334-53.333333H202.666667a53.333333 53.333333 0 0 0-53.333334 53.333333v344.48A290.090667 290.090667 0 0 1 192 597.333333a286.88 286.88 0 0 1 183.296 65.845334C427.029333 528.384 556.906667 437.333333 704 437.333333c65.706667 0 126.997333 16.778667 170.666667 41.962667z m0 82.24c-5.333333-8.32-21.130667-21.653333-43.648-32.917333C796.768 511.488 753.045333 501.333333 704 501.333333c-121.770667 0-229.130667 76.266667-270.432 188.693334-2.730667 7.445333-7.402667 20.32-13.994667 38.581333-7.68 21.301333-34.453333 28.106667-51.370666 13.056-16.437333-14.634667-28.554667-25.066667-36.138667-31.146667A222.890667 222.890667 0 0 0 192 661.333333c-14.464 0-28.725333 1.365333-42.666667 4.053334V768a53.333333 53.333333 0 0 0 53.333334 53.333333h618.666666a53.333333 53.333333 0 0 0 53.333334-53.333333V561.525333zM320 480a96 96 0 1 1 0-192 96 96 0 0 1 0 192z m0-64a32 32 0 1 0 0-64 32 32 0 0 0 0 64z" fill="#000000" p-id="1614"></path></svg>
                </button>
                <button class="input-btn" id="fileUploadBtn">
                    <svg t="1752140948318" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2697" width="20" height="20"><path d="M191.4 765.1c0 22.6 18.3 41 41 41h158.1c22.6 0 41-18.3 41-41 0-22.6-18.3-41-41-41H232.3c-22.6 0-40.9 18.3-40.9 41zM889.3 855.1h-40.9 16.2c13.8-0.8 24.7-12.2 24.7-26.2v26.2zM141.3 855.2h40.9H166c-13.8-0.8-24.7-12.2-24.7-26.2v26.2zM889.3 549.6h-40.9 16.2c13.8 0.8 24.7 12.2 24.7 26.2v-26.2zM809.6 346.9h-40.9 16.2c13.8 0.8 24.7 12.2 24.7 26.2v-26.2zM141.6 244h40.9-16.2c-13.8 0.8-24.7 12.2-24.7 26.2V244z" fill="#242424" p-id="2698"></path><path d="M906.1 468.1H232.3c-22.6 0-41 18.3-41 41 0 22.6 18.3 41 41 41h657v305.1h-748V244.1h287.3l52.7 81.4a40.98 40.98 0 0 0 36.1 21.5h291.9v63.4c0 22.6 18.3 41 41 41s41-18.3 41-41v-83.2c-3.2-34.7-32.3-61.8-67.8-61.8H769v-0.2H539.9L486 181.9l-0.6-0.9c-7.3-11.3-20-18.9-34.5-18.9H182.3v0.1h-54.5c-37.6 0-68.1 30.5-68.1 68.1v0.5h-0.1v643.6c2.4 32.8 28 59.1 60.5 62.6h787c33.1-1.5 60-26.6 64.3-58.9V529.7c-3.3-33.7-31.1-60.3-65.3-61.6z" fill="#242424" p-id="2699"></path></svg>
                </button>
                <input type="file" id="imageInput" class="file-input" accept="image/*" multiple>
                <input type="file" id="fileInput" class="file-input" multiple>
            </div>
            <textarea class="message-input" placeholder="Type in the question you want to ask..."></textarea>
            <button class="send-btn" disabled>send</button>
        </div>
    </div>

    <script src="__JS__/vendors/jquery-3.6.0.min.js"></script>
    <script src="__STATIC__/layer/layer.js"></script>

    <script>
        $(document).ready(function() {
            const chatData = {$chatData|raw};

            // 添加点击事件处理
            $('.chat-item').on('click', function() {
                var chatKey = $(this).data('user');
                renderChatMessages(chatData[chatKey]);
            });

            // 渲染聊天消息的函数示例
            function renderChatMessages(chat) {
                $('#chat-header').html(`
                    <h5>${chat.name}</h5>
                    <small class="text-muted">Last active: ${chat.lastTime}</small>
                `);

                $('.messages-container').empty();
                $.each(chat.messages, function(index, message) {
                    var messageClass = message.type === 'sent' ? 'sent' : 'received';
                    var messageHtml = '';

                    if (message.type === 'image') {
                        messageHtml = `
                            <div class="message ${messageClass}">
                                <div class="avatar">${message.avatar}</div>
                                <div class="content">
                                    <img src="${message.content}" class="img-fluid" alt="Image">
                                    <div class="time">${message.time}</div>
                                </div>
                            </div>
                        `;
                    } else if (message.type === 'file') {
                        messageHtml = `
                            <div class="message ${messageClass}">
                                <div class="avatar">${message.avatar}</div>
                                <div class="content">
                                    <div class="file-message">
                                        <i class="far fa-file-alt"></i>
                                        <div>
                                            <a href="${message.content.url}" target="_blank">${message.content.name}</a>
                                            <small>${formatFileSize(message.content.size)}</small>
                                        </div>
                                    </div>
                                    <div class="time">${message.time}</div>
                                </div>
                            </div>
                        `;
                    } else {
                        messageHtml = `
                            <div class="message ${messageClass}">
                                <div class="avatar">${message.avatar}</div>
                                <div class="content">
                                    <div class="text">${message.content}</div>
                                    <div class="time">${message.time}</div>
                                </div>
                            </div>
                        `;
                    }

                    $('.messages-container').append(messageHtml);
                });

                // 滚动到底部
                $('.messages-container').scrollTop($('.messages-container')[0].scrollHeight);
            }

            // 辅助函数：格式化文件大小
            function formatFileSize(bytes) {
                if (bytes === 0) return '0 Bytes';
                const k = 1024;
                const sizes = ['Bytes', 'KB', 'MB', 'GB'];
                const i = Math.floor(Math.log(bytes) / Math.log(k));
                return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
            }

            // 当前活跃的聊天用户 - 优先从URL参数获取
            let currentChatUser = "{$conversation_user.first_name}_{$conversation_user.last_name}";

            // 渲染消息列表
            function renderMessages(userId) {
                const user = chatData[userId];
                if (!user) return;

                const messagesContainer = $('.messages-container');
                messagesContainer.empty();

                user.messages.forEach(message => {
                    let messageHtml = '';
                    const messageClass = message.type === 'sent' || message.sender === 'sent' ? 'sent' : '';

                    // 生成头像HTML
                    const avatarUrl = message.avatarUrl || '__IMG__/user-1.jpg';
                    const avatarHtml = `<img src="${avatarUrl}">`;
                    if (message.type === 'image') {
                        messageHtml = `
                            <div class="message ${messageClass}">
                                <div class="message-avatar chat-avatar">${avatarHtml}</div>
                                <div class="message-content">
                                    <div class="image-message" onclick="openImageModal('${message.content}')">
                                        <img src="${message.content}" alt="聊天图片">
                                    </div>
                                    <div class="message-time">${message.time}</div>
                                </div>
                            </div>
                        `;
                    } else if (message.type === 'file') {
                        const isReceived = message.type === 'received' || message.sender === 'received' || messageClass === '';
                        const downloadButton = isReceived ? `<button class="file-download-btn" onclick="downloadFile('${message.content.name}', '${message.content.url || ''}', ${message.content.size})">download</button>` : '';

                        messageHtml = `
                            <div class="message ${messageClass}">
                                <div class="message-avatar chat-avatar">${avatarHtml}</div>
                                <div class="message-content">
                                    <div class="file-message-custom">
                                        <div class="file-icon-custom">
                                            <svg t="1752200995890" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="23768" width="20" height="20"><path d="M901.9392 497.408c-14.1312 0-25.6 11.4688-25.6 25.6v13.056c0 14.1312 11.4688 25.6 25.6 25.6s25.6-11.4688 25.6-25.6v-13.056c0-14.1824-11.4688-25.6-25.6-25.6z" fill="#ffffff" p-id="23769"></path><path d="M748.3392 202.9568h-202.9056c-11.5712 0-22.5792-4.3008-31.0272-11.776A46.21824 46.21824 0 0 1 506.88 182.6304l-15.2064-22.272-15.2064-22.272a97.49504 97.49504 0 0 0-20.2752-21.7088s-0.0512 0-0.0512-0.0512c-0.8704-0.7168-1.792-1.3312-2.7136-1.9968-0.6656-0.512-1.3312-1.024-2.048-1.536-0.4608-0.3584-0.9728-0.6144-1.4848-0.9728-1.1264-0.768-2.304-1.536-3.4816-2.2528-0.6656-0.4096-1.3312-0.7168-1.9456-1.1264-1.0752-0.6144-2.0992-1.2288-3.1744-1.792a97.90976 97.90976 0 0 0-45.6704-11.3152H263.9872c-98.816 0-179.2 80.384-179.2 179.2v480.3072c0 98.816 80.384 179.2 179.2 179.2h484.352c98.816 0 179.2-80.384 179.2-179.2v-143.8208c0-14.1312-11.4688-25.6-25.6-25.6s-25.6 11.4688-25.6 25.6v143.8208c0 70.6048-57.3952 128-128 128h-484.352c-70.6048 0-128-57.3952-128-128v-333.824h740.352V450.56c0 14.1312 11.4688 25.6 25.6 25.6s25.6-11.4688 25.6-25.6V382.1568c0-98.816-80.384-179.2-179.2-179.2z m-612.352 71.68c0-70.6048 57.3952-128 128-128h131.6352c15.4112 0 29.8496 7.6288 38.5536 20.3264l30.4128 44.544c18.2784 26.7264 48.4864 42.6496 80.8448 42.6496h202.9056c2.1504 0 4.2496 0.0512 6.4 0.1536l2.0992 0.1536 4.1984 0.3072 2.4576 0.3072 3.7376 0.4608c0.8704 0.1536 1.7408 0.3072 2.6624 0.4608l3.4816 0.6144 2.7136 0.6144 3.328 0.768c0.9216 0.2048 1.792 0.4608 2.7136 0.7168l3.2256 0.9216c0.9216 0.256 1.792 0.5632 2.7136 0.8704 1.024 0.3584 2.048 0.6656 3.072 1.024 0.9216 0.3072 1.792 0.6656 2.6624 0.9728 1.024 0.3584 1.9968 0.768 2.9696 1.1776 0.8704 0.3584 1.792 0.7168 2.6624 1.1264 0.9728 0.4096 1.9456 0.8704 2.9184 1.3312l2.6112 1.2288c0.9216 0.4608 1.8944 0.9728 2.816 1.4336 0.8704 0.4608 1.6896 0.8704 2.5088 1.3824 0.9216 0.512 1.8432 1.024 2.7136 1.5872 0.8192 0.4608 1.6384 0.9728 2.4576 1.4848 0.8704 0.5632 1.792 1.1264 2.6624 1.6896a110.86848 110.86848 0 0 1 4.9152 3.4304c0.768 0.5632 1.536 1.0752 2.304 1.6896 0.8704 0.6656 1.6896 1.3312 2.5088 1.9968 0.7168 0.5632 1.4336 1.1264 2.1504 1.7408 0.8704 0.7168 1.6896 1.4848 2.56 2.2016 0.6656 0.5632 1.3312 1.1264 1.9456 1.7408 0.9728 0.8704 1.8944 1.792 2.8672 2.7136l2.304 2.304c1.0752 1.0752 2.1504 2.2016 3.1744 3.328 0.5632 0.6144 1.1264 1.28 1.6384 1.8944 0.768 0.8704 1.536 1.7408 2.2528 2.6112l1.6896 2.1504c0.6656 0.8704 1.3824 1.6896 1.9968 2.56 0.5632 0.768 1.1264 1.536 1.6384 2.2528 0.6144 0.8704 1.2288 1.6896 1.8432 2.56 0.512 0.768 1.0752 1.5872 1.5872 2.4064 0.5632 0.8704 1.1264 1.7408 1.6896 2.6624 0.512 0.8192 0.9728 1.6384 1.4848 2.4576 0.512 0.9216 1.0752 1.792 1.5872 2.7136 0.4608 0.8192 0.9216 1.6896 1.3824 2.56 0.4608 0.9216 0.9728 1.8432 1.4336 2.7648l1.2288 2.6112c0.4608 0.9728 0.8704 1.8944 1.28 2.8672 0.4096 0.8704 0.768 1.792 1.1264 2.6624 0.4096 0.9728 0.768 1.9456 1.1776 2.9696 0.3584 0.9216 0.6656 1.792 1.024 2.7136 0.3584 1.024 0.7168 2.048 1.024 3.072 0.3072 0.9216 0.6144 1.8432 0.8704 2.7648 0.3072 1.024 0.6144 2.0992 0.8704 3.1232 0.256 0.9216 0.512 1.8432 0.7168 2.7648l0.768 3.2256 0.6144 2.816c0.2048 1.0752 0.4096 2.2016 0.6144 3.328 0.1536 0.9216 0.3072 1.8432 0.4608 2.816l0.4608 3.4816c0.1024 0.9216 0.256 1.8432 0.3072 2.7648 0 0.1536 0 0.256 0.0512 0.4096H135.9872V274.6368z" fill="#ffffff" p-id="23770"></path></svg></div>
                                        <div class="file-info-custom">
                                            <div class="file-name-custom" title="${message.content.name}">${message.content.name}</div>
                                            <div class="file-size-custom">${formatFileSize(message.content.size)}</div>
                                        </div>
                                        ${downloadButton}
                                    </div>
                                    <div class="message-time">${message.time}</div>
                                </div>
                            </div>
                        `;
                    } else {
                        messageHtml = `
                            <div class="message ${messageClass}">
                                <div class="message-avatar chat-avatar">${avatarHtml}</div>
                                <div class="message-content">
                                    <div class="message-text">${message.content}</div>
                                    <div class="message-time">${message.time}</div>
                                </div>
                            </div>
                        `;
                    }

                    messagesContainer.append(messageHtml);
                });

                // 滚动到底部
                messagesContainer.scrollTop(messagesContainer[0].scrollHeight);
            }

            // 更新聊天头部信息
            function updateChatHeader(userId) {
                const user = chatData[userId];
                if (!user) return;

                // 更新顶部会话头像
                $('.chat-header .chat-avatar').html(`<img src="${user.avatarUrl ?? '__IMG__/user-1.jpg'}">`);
                $('.chat-header h2').text(user.name);
            }

            // 更新URL参数
            function updateURLParameter(userId) {
                const url = new URL(window.location);
                url.searchParams.set('user', userId);
                // 使用 replaceState 避免在浏览器历史中创建新条目
                window.history.replaceState({}, '', url);
            }

            // 切换聊天
            function switchChat(userId) {
                if (!chatData[userId]) return;

                currentChatUser = userId;

                // 更新URL参数
                updateURLParameter(userId);

                // 保存当前用户到本地存储（作为备用）
                localStorage.setItem('currentChatUser', userId);

                updateChatHeader(userId);
                renderMessages(userId);

                // 更新聊天列表的活跃状态
                $('.chat-item').removeClass('active');
                $(`.chat-item[data-user="${userId}"]`).addClass('active');

                // 清除未读标记
                $(`.chat-item[data-user="${userId}"] .unread-badge`).fadeOut();
                if (chatData[userId].unread) {
                    delete chatData[userId].unread;
                }
            }

            // 检测是否为移动端
            function isMobile() {
                return window.innerWidth <= 768;
            }

            // 初始化移动端布局
            function initMobileLayout() {
                if (isMobile()) {
                    $('.sidebar').addClass('mobile-overlay');
                    $('.mobile-menu-btn').show();
                } else {
                    $('.sidebar').removeClass('mobile-overlay show');
                    $('.mobile-backdrop').removeClass('show');
                    $('.mobile-menu-btn').hide();
                }
            }

            // 页面加载时初始化
            initMobileLayout();

            // 初始化聊天界面（这会自动更新URL和localStorage）
            switchChat(currentChatUser);

            // 窗口大小改变时重新初始化
            $(window).resize(function() {
                initMobileLayout();
            });

            // 移动端菜单按钮点击
            $('.mobile-menu-btn').click(function() {
                $('.sidebar').addClass('show');
                $('.mobile-backdrop').addClass('show');
            });

            // 移动端关闭按钮点击
            $('.mobile-close-btn').click(function() {
                $('.sidebar').removeClass('show');
                $('.mobile-backdrop').removeClass('show');
            });

            // 点击背景遮罩关闭侧边栏
            $('.mobile-backdrop').click(function() {
                $('.sidebar').removeClass('show');
                $('.mobile-backdrop').removeClass('show');
            });

            // 文件上传相关变量
            let uploadedFiles = [];
            let uploadedImages = [];

            // 消息输入框交互
            $('.message-input').on('input', function() {
                const text = $(this).val().trim();
                const hasFiles = uploadedFiles.length > 0 || uploadedImages.length > 0;
                $('.send-btn').prop('disabled', text === '' && !hasFiles);
            });

            // 发送消息
            $('.send-btn').click(function() {
                sendMessage();
            });

            $('.message-input').keypress(function(e) {
                if (e.which === 13 && !e.shiftKey) {
                    e.preventDefault();
                    sendMessage();
                }
            });

            async function sendMessage() {
                const text = $('.message-input').val().trim();
                const hasFiles = uploadedFiles.length > 0 || uploadedImages.length > 0;
                if (text === '' && !hasFiles) return;

                const currentUser = chatData[currentChatUser];
                const currentTime = new Date().toLocaleString();
                const promises = [];

                try {
                    // 发送文本消息
                    if (text !== '') {
                        const messageData = {
                            id: Date.now(),
                            type: 'sent',
                            content: text,
                            time: currentTime,
                            avatarUrl: "{$user['avatar']}",
                            receiver_id: currentUser.user_id,
                            conversation_id: currentUser.conversation_id
                        };

                        promises.push(
                            saveMessageToBackend(messageData)
                                .then(function() {
                                    currentUser.messages.push(messageData);
                                    currentUser.lastTime = currentTime;
                                })
                                .catch(function(error) {
                                    throw error;
                                })
                        );
                    }

                    // 发送图片消息
                    uploadedImages.forEach(image => {
                        promises.push(
                            uploadFileToBackend(image.file)
                                .then(function(imageUrl) {
                                    const messageData = {
                                        id: Date.now() + Math.random(),
                                        type: 'image',
                                        content: imageUrl,
                                        time: currentTime,
                                        avatarUrl: "{$user['avatar']}",
                                        sender: 'sent',
                                        receiver_id: currentUser.user_id,
                                        conversation_id: currentUser.conversation_id
                                    };

                                    return saveMessageToBackend(messageData).then(() => messageData);
                                })
                                .then(function(messageData) {
                                    currentUser.messages.push(messageData);
                                    currentUser.lastTime = currentTime;
                                })
                                .catch(function(error) {
                                    throw error;
                                })
                        );
                    });

                    // 发送文件消息
                    uploadedFiles.forEach(file => {
                        promises.push(
                            uploadFileToBackend(file)
                                .then(function(fileUrl) {
                                    const messageData = {
                                        id: Date.now() + Math.random(),
                                        type: 'file',
                                        content: {
                                            name: file.name,
                                            size: file.size,
                                            url: fileUrl
                                        },
                                        time: currentTime,
                                        avatarUrl: "{$user['avatar']}",
                                        sender: 'sent',
                                        receiver_id: currentUser.user_id,
                                        conversation_id: currentUser.conversation_id
                                    };

                                    return saveMessageToBackend(messageData).then(() => messageData);
                                })
                                .then(function(messageData) {
                                    currentUser.messages.push(messageData);
                                    currentUser.lastTime = currentTime;
                                })
                                .catch(function(error) {
                                    throw error;
                                })
                        );
                    });

                    // 等待所有操作完成
                    await Promise.all(promises);

                    // 所有操作成功后才更新UI
                    renderMessages(currentChatUser);
                    updateChatListPreview(currentChatUser, text || '发送了文件');

                    // 清空输入和上传文件
                    $('.message-input').val('');
                    uploadedFiles = [];
                    uploadedImages = [];
                    updateUploadPreview();
                    $('.send-btn').prop('disabled', true);

                } catch (error) {
                    // alert('消息发送失败: ' + error.message)
                    console.log('消息发送失败: ' + error);
                }
            }

            // 保存消息到后端的函数
            function saveMessageToBackend(messageData) {
                return $.ajax({
                    url: '/message/save',
                    type: 'POST',
                    dataType: 'json',
                    data: messageData,
                }).then(response => {
                    // console.log('收到响应:', response);
                    if (response && response.code === 1) {
                        return response;
                    } else {
                        layer.msg('保存消息失败: ' + response.msg, {icon: 2});
                        throw new Error(response.msg || '保存消息失败');
                    }
                }).catch(function(jqXHR, textStatus, errorThrown) {
                    // console.error('AJAX请求失败:', textStatus, errorThrown);
                    throw new Error(textStatus);
                });
            }

            // 上传文件到后端的函数
            function uploadFileToBackend(file) {
                const formData = new FormData();
                formData.append('file', file);

                return $.ajax({
                    url: '/message/upload',
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                }).then(response => {
                    // console.log('收到响应:', response);
                    if (response && response.code === 1 && response.msg) {
                        return response.msg;
                    } else {
                        layer.msg('文件上传失败: ' + response.msg, {icon: 2});
                        throw new Error(response.msg || '文件上传失败');
                    }
                }).catch(function(jqXHR, textStatus, errorThrown) {
                    // console.error('AJAX请求失败:', textStatus, errorThrown);
                    throw new Error(textStatus);
                });
            }

            // 更新聊天列表预览
            function updateChatListPreview(userId, lastMessage) {
                const chatItem = $(`.chat-item[data-user="${userId}"]`);
                const user = chatData[userId];

                // 如果消息为空，显示默认文本
                const previewText = lastMessage || 'No message';
                chatItem.find('.chat-preview').text(previewText);
                chatItem.find('.chat-time').text(user.lastTime || '');

                // 如果有消息内容，将当前聊天移到列表顶部
                if (lastMessage) {
                    chatItem.prependTo('.chat-list');
                }
            }

            // 聊天列表项点击
            $('.chat-item').click(function() {
                const userId = $(this).data('user');
                if (userId) {
                    switchChat(userId);
                }

                // 移动端选择聊天后关闭侧边栏
                if (isMobile()) {
                    $('.sidebar').removeClass('show');
                    $('.mobile-backdrop').removeClass('show');
                }
            });

            // 刷新按钮
            $('.refresh-btn').click(function() {
                location.reload();
            });

            // 下载按钮
            $('.download-btn').click(function() {
                alert('Downloading files...');
            });

            // 文件大小格式化函数
            function formatFileSize(bytes) {
                if (bytes === 0) return '0 B';
                const k = 1024;
                const sizes = ['B', 'KB', 'MB', 'GB'];
                const i = Math.floor(Math.log(bytes) / Math.log(k));
                return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
            }

            // 下载文件函数
            window.downloadFile = function(fileName, fileUrl, fileSize) {
                if (!fileUrl) {
                    layer.msg('File download link is not available.', {icon: 2});
                    return;
                }

                // 创建下载链接
                const link = document.createElement('a');
                link.href = fileUrl;
                link.download = fileName;
                link.style.display = 'none';

                // 添加到页面并触发下载
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                // 显示下载提示
                showDownloadNotification(fileName, fileSize);
            };

            // 显示下载通知
            function showDownloadNotification(fileName, fileSize) {
                const notification = $(`
                    <div class="download-notification">
                        <div class="download-icon">⬇️</div>
                        <div class="download-info">
                            <div class="download-filename">${fileName}</div>
                            <div class="download-size">${formatFileSize(fileSize)}</div>
                        </div>
                        <div class="download-status">Downloading...</div>
                    </div>
                `);

                $('body').append(notification);

                // 3秒后自动消失
                setTimeout(() => {
                    notification.fadeOut(300, function() {
                        $(this).remove();
                    });
                }, 3000);
            }

            // 更新上传预览区域
            function updateUploadPreview() {
                const preview = $('#uploadPreview');
                preview.empty();

                if (uploadedImages.length === 0 && uploadedFiles.length === 0) {
                    preview.removeClass('show');
                    return;
                }

                preview.addClass('show');

                // 显示图片预览
                uploadedImages.forEach((image, index) => {
                    const previewHtml = `
                        <div class="preview-item">
                            <img src="${image.url}" class="preview-image" alt="预览">
                            <button class="preview-remove" onclick="removeUploadedImage(${index})">×</button>
                        </div>
                    `;
                    preview.append(previewHtml);
                });

                // 显示文件预览
                uploadedFiles.forEach((file, index) => {
                    const previewHtml = `
                        <div class="preview-item">
                            <div class="preview-file">
                                <div class="preview-file-icon">
                                    <svg t="1752200995890" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="23768" width="20" height="20"><path d="M901.9392 497.408c-14.1312 0-25.6 11.4688-25.6 25.6v13.056c0 14.1312 11.4688 25.6 25.6 25.6s25.6-11.4688 25.6-25.6v-13.056c0-14.1824-11.4688-25.6-25.6-25.6z" fill="#ffffff" p-id="23769"></path><path d="M748.3392 202.9568h-202.9056c-11.5712 0-22.5792-4.3008-31.0272-11.776A46.21824 46.21824 0 0 1 506.88 182.6304l-15.2064-22.272-15.2064-22.272a97.49504 97.49504 0 0 0-20.2752-21.7088s-0.0512 0-0.0512-0.0512c-0.8704-0.7168-1.792-1.3312-2.7136-1.9968-0.6656-0.512-1.3312-1.024-2.048-1.536-0.4608-0.3584-0.9728-0.6144-1.4848-0.9728-1.1264-0.768-2.304-1.536-3.4816-2.2528-0.6656-0.4096-1.3312-0.7168-1.9456-1.1264-1.0752-0.6144-2.0992-1.2288-3.1744-1.792a97.90976 97.90976 0 0 0-45.6704-11.3152H263.9872c-98.816 0-179.2 80.384-179.2 179.2v480.3072c0 98.816 80.384 179.2 179.2 179.2h484.352c98.816 0 179.2-80.384 179.2-179.2v-143.8208c0-14.1312-11.4688-25.6-25.6-25.6s-25.6 11.4688-25.6 25.6v143.8208c0 70.6048-57.3952 128-128 128h-484.352c-70.6048 0-128-57.3952-128-128v-333.824h740.352V450.56c0 14.1312 11.4688 25.6 25.6 25.6s25.6-11.4688 25.6-25.6V382.1568c0-98.816-80.384-179.2-179.2-179.2z m-612.352 71.68c0-70.6048 57.3952-128 128-128h131.6352c15.4112 0 29.8496 7.6288 38.5536 20.3264l30.4128 44.544c18.2784 26.7264 48.4864 42.6496 80.8448 42.6496h202.9056c2.1504 0 4.2496 0.0512 6.4 0.1536l2.0992 0.1536 4.1984 0.3072 2.4576 0.3072 3.7376 0.4608c0.8704 0.1536 1.7408 0.3072 2.6624 0.4608l3.4816 0.6144 2.7136 0.6144 3.328 0.768c0.9216 0.2048 1.792 0.4608 2.7136 0.7168l3.2256 0.9216c0.9216 0.256 1.792 0.5632 2.7136 0.8704 1.024 0.3584 2.048 0.6656 3.072 1.024 0.9216 0.3072 1.792 0.6656 2.6624 0.9728 1.024 0.3584 1.9968 0.768 2.9696 1.1776 0.8704 0.3584 1.792 0.7168 2.6624 1.1264 0.9728 0.4096 1.9456 0.8704 2.9184 1.3312l2.6112 1.2288c0.9216 0.4608 1.8944 0.9728 2.816 1.4336 0.8704 0.4608 1.6896 0.8704 2.5088 1.3824 0.9216 0.512 1.8432 1.024 2.7136 1.5872 0.8192 0.4608 1.6384 0.9728 2.4576 1.4848 0.8704 0.5632 1.792 1.1264 2.6624 1.6896a110.86848 110.86848 0 0 1 4.9152 3.4304c0.768 0.5632 1.536 1.0752 2.304 1.6896 0.8704 0.6656 1.6896 1.3312 2.5088 1.9968 0.7168 0.5632 1.4336 1.1264 2.1504 1.7408 0.8704 0.7168 1.6896 1.4848 2.56 2.2016 0.6656 0.5632 1.3312 1.1264 1.9456 1.7408 0.9728 0.8704 1.8944 1.792 2.8672 2.7136l2.304 2.304c1.0752 1.0752 2.1504 2.2016 3.1744 3.328 0.5632 0.6144 1.1264 1.28 1.6384 1.8944 0.768 0.8704 1.536 1.7408 2.2528 2.6112l1.6896 2.1504c0.6656 0.8704 1.3824 1.6896 1.9968 2.56 0.5632 0.768 1.1264 1.536 1.6384 2.2528 0.6144 0.8704 1.2288 1.6896 1.8432 2.56 0.512 0.768 1.0752 1.5872 1.5872 2.4064 0.5632 0.8704 1.1264 1.7408 1.6896 2.6624 0.512 0.8192 0.9728 1.6384 1.4848 2.4576 0.512 0.9216 1.0752 1.792 1.5872 2.7136 0.4608 0.8192 0.9216 1.6896 1.3824 2.56 0.4608 0.9216 0.9728 1.8432 1.4336 2.7648l1.2288 2.6112c0.4608 0.9728 0.8704 1.8944 1.28 2.8672 0.4096 0.8704 0.768 1.792 1.1264 2.6624 0.4096 0.9728 0.768 1.9456 1.1776 2.9696 0.3584 0.9216 0.6656 1.792 1.024 2.7136 0.3584 1.024 0.7168 2.048 1.024 3.072 0.3072 0.9216 0.6144 1.8432 0.8704 2.7648 0.3072 1.024 0.6144 2.0992 0.8704 3.1232 0.256 0.9216 0.512 1.8432 0.7168 2.7648l0.768 3.2256 0.6144 2.816c0.2048 1.0752 0.4096 2.2016 0.6144 3.328 0.1536 0.9216 0.3072 1.8432 0.4608 2.816l0.4608 3.4816c0.1024 0.9216 0.256 1.8432 0.3072 2.7648 0 0.1536 0 0.256 0.0512 0.4096H135.9872V274.6368z" fill="#ffffff" p-id="23770"></path></svg></div>
                                <div class="preview-file-name" title="${file.name}">${file.name}</div>
                            </div>
                            <button class="preview-remove" onclick="removeUploadedFile(${index})">×</button>
                        </div>
                    `;
                    preview.append(previewHtml);
                });

                // 更新发送按钮状态
                const text = $('.message-input').val().trim();
                const hasFiles = uploadedFiles.length > 0 || uploadedImages.length > 0;
                $('.send-btn').prop('disabled', text === '' && !hasFiles);
            }

            // 移除上传的图片
            window.removeUploadedImage = function(index) {
                uploadedImages.splice(index, 1);
                updateUploadPreview();
            };

            // 移除上传的文件
            window.removeUploadedFile = function(index) {
                uploadedFiles.splice(index, 1);
                updateUploadPreview();
            };

            // 图片上传按钮点击（jQuery方式）
            $('#imageUploadBtn').on('click', function(e) {
                e.preventDefault();
                $('#imageInput')[0].click();
            });

            // 文件上传按钮点击（jQuery方式）
            $('#fileUploadBtn').on('click', function(e) {
                e.preventDefault();
                $('#fileInput')[0].click();
            });

            // 图片文件选择处理
            $('#imageInput').change(function(e) {
                const files = Array.from(e.target.files);
                files.forEach(file => {
                    if (file.type.startsWith('image/')) {
                        const reader = new FileReader();
                        reader.onload = function(e) {
                            uploadedImages.push({
                                file: file,
                                url: e.target.result,
                                name: file.name,
                                size: file.size
                            });
                            updateUploadPreview();
                        };
                        reader.readAsDataURL(file);
                    }
                });
                // 清空input以允许重复选择同一文件
                $(this).val('');
            });

            // 普通文件选择处理
            $('#fileInput').change(function(e) {
                const files = Array.from(e.target.files);
                files.forEach(file => {
                    uploadedFiles.push(file);
                });
                updateUploadPreview();
                // 清空input以允许重复选择同一文件
                $(this).val('');
            });

            // 图片预览相关变量
            let currentZoom = 1;
            let currentImageSrc = '';

            // 图片预览模态框功能
            window.openImageModal = function(imageSrc) {
                currentImageSrc = imageSrc;
                currentZoom = 1;

                const modalImage = $('#modalImage');
                modalImage.attr('src', imageSrc);
                modalImage.css('transform', 'scale(1)');

                // 更新图片信息
                updateImageInfo(imageSrc);

                $('#imageModal').fadeIn(300);
                $('body').css('overflow', 'hidden'); // 防止背景滚动
            };

            // 更新图片信息
            function updateImageInfo(imageSrc) {
                const img = new Image();
                img.onload = function() {
                    const info = `${this.naturalWidth} × ${this.naturalHeight}`;
                    $('#imageModalInfo').text(info);
                };
                img.src = imageSrc;
            }

            // 关闭图片模态框
            $('#imageModalClose').click(function(e) {
                e.preventDefault();
                e.stopPropagation();
                closeImageModal();
            });

            // 关闭模态框函数
            function closeImageModal() {
                $('#imageModal').fadeOut(300);
                $('body').css('overflow', 'auto');
                currentZoom = 1;
                currentImageSrc = '';
            }

            // 图片缩放控制
            $('#zoomInBtn').click(function(e) {
                e.stopPropagation();
                currentZoom = Math.min(currentZoom * 1.2, 3);
                updateImageZoom();
            });

            $('#zoomOutBtn').click(function(e) {
                e.stopPropagation();
                currentZoom = Math.max(currentZoom / 1.2, 0.5);
                updateImageZoom();
            });

            $('#resetZoomBtn').click(function(e) {
                e.stopPropagation();
                currentZoom = 1;
                updateImageZoom();
            });

            $('#downloadImageBtn').click(function(e) {
                e.stopPropagation();
                if (currentImageSrc) {
                    downloadImage(currentImageSrc);
                }
            });

            // 更新图片缩放
            function updateImageZoom() {
                $('#modalImage').css('transform', `scale(${currentZoom})`);
            }

            // 下载图片
            function downloadImage(imageSrc) {
                const link = document.createElement('a');
                link.href = imageSrc;
                link.download = `image_${Date.now()}.jpg`;
                link.style.display = 'none';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                showDownloadNotification('Image file', 0);
            }

            // 鼠标滚轮缩放
            $('#modalImage').on('wheel', function(e) {
                e.preventDefault();
                const delta = e.originalEvent.deltaY;
                if (delta < 0) {
                    // 向上滚动，放大
                    currentZoom = Math.min(currentZoom * 1.1, 3);
                } else {
                    // 向下滚动，缩小
                    currentZoom = Math.max(currentZoom / 1.1, 0.5);
                }
                updateImageZoom();
            });

            // 双击重置缩放
            $('#modalImage').on('dblclick', function(e) {
                e.stopPropagation();
                currentZoom = 1;
                updateImageZoom();
            });

            // ESC键关闭模态框
            $(document).keydown(function(e) {
                if (e.keyCode === 27) { // ESC键
                    closeImageModal();
                }
            });

            // 为现有图片添加点击预览功能
            $(document).on('click', '.image-message', function() {
                const imgSrc = $(this).find('img').attr('src');
                if (imgSrc) {
                    openImageModal(imgSrc);
                }
            });

            // 头部操作按钮
            $('.action-btn').click(function() {
                const icon = $(this).data('btn');
                switch(icon) {
                    case 'history':
                        openSearchModal();
                        break;
                    case 'delete':
                        deleteChatUser();
                        break;
                }
            });

            // 搜索框中的删除消息记录按钮
            $('#clearMessagesBtn').click(function() {
                clearChatHistory();
            });

            // 删除聊天用户功能
            function deleteChatUser() {
                const currentUser = chatData[currentChatUser];
                if (!currentUser) return;

                // 显示确认对话框
                if (confirm(`Are you sure to delete the chat conversation with ${currentUser.name}? This action cannot be undone.`)) {

                    //清除会话内的消息，保留会话
                    $.ajax({
                        url: '/message/delete',
                        type: 'POST',
                        dataType: 'json',
                        data: {conversation_id: currentUser.conversation_id, type:0},
                    }).then(response => {
                        if (response && response.code === 1) {
                            layer.msg(response.msg, {icon: 1});
                        } else {
                            layer.msg('删除失败: ' + response.msg, {icon: 2});
                            throw new Error(response.msg || '删除会话失败');
                        }
                    }).catch(function(jqXHR, textStatus, errorThrown) {
                        // console.error('AJAX请求失败:', textStatus, errorThrown);
                        throw new Error(textStatus);
                    });

                    // 从聊天数据中删除用户
                    delete chatData[currentChatUser];

                    // 从DOM中移除聊天项
                    $(`.chat-item[data-user="${currentChatUser}"]`).remove();

                    // 切换到剩余的第一个用户
                    const remainingUsers = Object.keys(chatData);
                    if (remainingUsers.length > 0) {
                        const nextUser = remainingUsers[0];
                        switchChat(nextUser);
                    } else {
                        // 如果没有剩余用户，显示空状态
                        $('.messages-container').html('<div class="no-chats">No chat conversation yet.</div>');
                        $('.chat-header h2').text('Please select chat');
                    }

                    // 显示删除成功提示
                    showDeleteUserNotification(currentUser.name);
                }
            }

            // 清空聊天记录功能
            function clearChatHistory() {
                // 显示确认对话框
                if (confirm('确定要清空当前聊天记录吗？此操作不可撤销。')) {
                    const currentUser = chatData[currentChatUser];
                    if (currentUser) {

                        //清除会话内的消息，保留会话
                        $.ajax({
                            url: '/message/delete',
                            type: 'POST',
                            dataType: 'json',
                            data: {conversation_id: currentUser.conversation_id, type:1},
                        }).then(response => {
                            if (response && response.code === 1) {
                                layer.msg(response.msg, {icon: 1});
                            } else {
                                layer.msg('删除失败: ' + response.msg, {icon: 2});
                                throw new Error(response.msg || '删除消息失败');
                            }
                        }).catch(function(jqXHR, textStatus, errorThrown) {
                            // console.error('AJAX请求失败:', textStatus, errorThrown);
                            throw new Error(textStatus);
                        });

                        // 清空消息记录
                        currentUser.messages = [];

                        // 重新渲染消息列表（显示为空）
                        renderMessages(currentChatUser);

                        // 更新聊天列表预览
                        updateChatListPreview(currentChatUser, '');

                        // 显示清空成功提示
                        showClearNotification();

                        // 关闭搜索模态框
                        closeSearchModal();
                    }
                }
            }

            // 显示删除用户成功通知
            function showDeleteUserNotification(userName) {
                const notification = $(`
                    <div class="clear-notification">
                        <div class="clear-icon">👋</div>
                        <div class="clear-info">
                            <div class="clear-title">聊天对话已删除</div>
                            <div class="clear-desc">与 ${userName} 的聊天对话已删除</div>
                        </div>
                    </div>
                `);

                $('body').append(notification);

                // 3秒后自动消失
                setTimeout(() => {
                    notification.fadeOut(300, function() {
                        $(this).remove();
                    });
                }, 3000);
            }

            // 显示清空成功通知
            function showClearNotification() {
                const notification = $(`
                    <div class="clear-notification">
                        <div class="clear-icon">🗑️</div>
                        <div class="clear-info">
                            <div class="clear-title">聊天记录已清空</div>
                            <div class="clear-desc">当前对话的所有消息已删除</div>
                        </div>
                    </div>
                `);

                $('body').append(notification);

                // 3秒后自动消失
                setTimeout(() => {
                    notification.fadeOut(300, function() {
                        $(this).remove();
                    });
                }, 3000);
            }

            // 搜索功能相关
            function openSearchModal() {
                // 更新搜索标题显示当前用户
                const currentUser = chatData[currentChatUser];
                $('#searchTitle').text(`History of ${currentUser.name}`);

                // 显示所有历史记录
                displayAllMessages();

                $('#searchModal').fadeIn(300);
                $('#searchInput').focus();
                $('body').css('overflow', 'hidden');
            }

            function closeSearchModal() {
                $('#searchModal').fadeOut(300);
                $('body').css('overflow', 'auto');
                $('#searchInput').val('');
            }

            // 搜索关闭按钮
            $('#searchCloseBtn').click(closeSearchModal);

            // 点击背景关闭搜索
            $('#searchModal').click(function(e) {
                if (e.target === this) {
                    closeSearchModal();
                }
            });

            // 搜索按钮点击
            $('#searchBtn').click(performSearch);

            // 搜索输入框回车
            $('#searchInput').keypress(function(e) {
                if (e.which === 13) {
                    performSearch();
                }
            });

            // 实时搜索（输入时延迟搜索）
            let searchTimeout;
            $('#searchInput').on('input', function() {
                clearTimeout(searchTimeout);
                const query = $(this).val().trim();
                if (query.length > 0) {
                    searchTimeout = setTimeout(() => {
                        performSearch();
                    }, 300);
                } else {
                    // 如果搜索框为空，显示所有历史记录
                    displayAllMessages();
                }
            });

            // 显示所有历史记录
            function displayAllMessages() {
                const user = chatData[currentChatUser];
                if (!user || !user.messages || user.messages.length === 0) {
                    $('#searchResults').html(`
                        <div class="search-no-results">
                            <div class="search-no-results-icon">
                                <svg t="1752199554675" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="12156" width="48" height="48"><path d="M778.24 61.44a122.88 122.88 0 0 1 122.88 122.88v655.36a122.88 122.88 0 0 1-122.88 122.88H245.76a122.88 122.88 0 0 1-122.88-122.88V184.32a122.88 122.88 0 0 1 122.88-122.88h532.48z m0 61.44H245.76a61.44 61.44 0 0 0-61.3376 57.83552L184.32 184.32v655.36a61.44 61.44 0 0 0 57.83552 61.3376L245.76 901.12h532.48a61.44 61.44 0 0 0 61.3376-57.83552L839.68 839.68V184.32a61.44 61.44 0 0 0-57.83552-61.3376L778.24 122.88zM563.2 532.48a30.72 30.72 0 0 1 0 61.44h-266.24a30.72 30.72 0 0 1 0-61.44h266.24z m163.84-225.28a30.72 30.72 0 0 1 0 61.44h-430.08a30.72 30.72 0 0 1 0-61.44h430.08z" fill="#131415" p-id="12157"></path></svg>
                            </div>
                            <div class="search-no-results-text">No history record</div>
                            <div class="search-no-results-desc">After starting the chat, historical messages will be displayed here</div>
                        </div>
                    `);
                    return;
                }

                // 获取所有消息并按时间排序
                const allMessages = user.messages.map(message => ({
                    userId: currentChatUser,
                    userName: user.name,
                    userAvatar: user.avatar,
                    message: message,
                    matchedContent: getMessageDisplayContent(message)
                }));

                // 按时间排序（最新的在前）
                allMessages.sort((a, b) => new Date(b.message.time) - new Date(a.message.time));

                displaySearchResults(allMessages, null);
            }

            // 获取消息显示内容
            function getMessageDisplayContent(message) {
                if (message.type === 'file') {
                    return message.content.name || 'file';
                } else if (message.type === 'image') {
                    return 'picture';
                } else {
                    return message.content || '';
                }
            }

            // 执行搜索
            function performSearch() {
                const query = $('#searchInput').val().trim();
                if (query === '') {
                    displayAllMessages();
                    return;
                }

                const results = searchMessages(query);
                displaySearchResults(results, query);
            }

            // 搜索消息函数
            function searchMessages(query) {
                const results = [];
                const searchQuery = query.toLowerCase();

                // 只搜索当前用户
                const user = chatData[currentChatUser];
                if (!user || !user.messages) return results;

                user.messages.forEach(message => {
                    let shouldInclude = false;
                    let matchedContent = '';

                    // 搜索所有类型的消息
                    if (message.type === 'text' || message.type === 'sent' || message.type === 'received' || (!message.type && message.content && typeof message.content === 'string')) {
                        // 文本消息：搜索消息内容
                        if (message.content.toLowerCase().includes(searchQuery)) {
                            shouldInclude = true;
                            matchedContent = message.content;
                        }
                    } else if (message.type === 'file') {
                        // 文件消息：搜索文件名
                        if (message.content.name && message.content.name.toLowerCase().includes(searchQuery)) {
                            shouldInclude = true;
                            matchedContent = message.content.name;
                        }
                    } else if (message.type === 'image') {
                        // 图片消息：搜索时间信息
                        const imageSearchText = `picture ${message.time}`.toLowerCase();
                        if (imageSearchText.includes(searchQuery)) {
                            shouldInclude = true;
                            matchedContent = 'picture';
                        }
                    }

                    if (shouldInclude) {
                        results.push({
                            userId: currentChatUser,
                            userName: user.name,
                            userAvatar: user.avatar,
                            message: message,
                            matchedContent: matchedContent
                        });
                    }
                });

                // 按时间排序（最新的在前）
                results.sort((a, b) => new Date(b.message.time) - new Date(a.message.time));

                return results;
            }

            // 显示搜索结果
            function displaySearchResults(results, query) {
                const resultsContainer = $('#searchResults');

                if (results.length === 0) {
                    const noResultsText = query ? 'No relevant messages found' : 'No history record';
                    const noResultsDesc = query ? 'Try using other keywords' : 'After starting the chat, historical messages will be displayed here';

                    resultsContainer.html(`
                        <div class="search-no-results">
                            <div class="search-no-results-icon">${query ? '<svg t="1752199035897" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="10129" width="48" height="48"><path d="M907.814485 410.283993a27.347916 27.347916 0 0 0-15.196141-16.872469L671.220972 297.302033a27.33507 27.33507 0 0 0-34.104611 10.67455L556.466342 438.24849c-0.128454 0.192681-0.173413 0.37894-0.295445 0.597312l-186.258704 21.567474-13.243636-31.015286a27.251575 27.251575 0 0 0-13.307863-13.930867L153.980551 324.894012a27.315802 27.315802 0 0 0-36.898491 35.395576l60.540501 141.768565v364.533974a27.380029 27.380029 0 0 0 12.460065 22.948357l200.70981 130.059957c0.109186 0.064227 0.205527 0.064227 0.2826 0.122032a28.75449 28.75449 0 0 0 6.262146 2.890221c0.468858 0.128454 0.976253 0.282599 1.44511 0.37894a26.975398 26.975398 0 0 0 6.872304 1.001943 26.101909 26.101909 0 0 0 3.140707-0.186258l374.778204-43.353319a27.322225 27.322225 0 0 0 24.175095-27.155235v-362.883337l97.233466-157.619822a27.23873 27.23873 0 0 0 2.832417-22.511613zM596.10091 482.790011l137.799327 89.275723-137.799327 15.915486V482.790011z m-36.114921-11.361781a24.406313 24.406313 0 0 0 3.326966 2.890221v117.465015L432.469427 606.947494l-49.146607-115.114302z m-249.528859-11.117718l50.19351 117.535665-128.563465-83.315445a23.866805 23.866805 0 0 0-0.37894-3.513225 26.500118 26.500118 0 0 0-1.663483-5.273048l-34.278024-80.283924zM232.260588 533.7414l146.071783 94.619422v318.039948l-146.071783-94.651535z m200.722656 432.248648V641.854944c0.500972-0.68723 0.956984-1.412997 1.400152-2.132341l318.727178-36.853533v326.106877z m333.081944-412.126285l-154.787405-100.329214 59.866116-96.681113 170.773541 74.105273z" fill="#A5A5A5" p-id="10130"></path><path d="M520.216545 217.037377h-33.218277l-6.737427-85.486323a70.78473 70.78473 0 0 0 24.16225 4.181187 56.686873 56.686873 0 0 0 35.196472-11.149831 37.515072 37.515072 0 0 0 14.772242-31.824548 40.463098 40.463098 0 0 0-12.54356-31.015285 44.44518 44.44518 0 0 0-31.59333-11.734299 40.37318 40.37318 0 0 0-30.893254 10.918614 38.850997 38.850997 0 0 0-10.218538 27.61767 48.658481 48.658481 0 0 0 1.162512 9.756102H398.486848a108.967764 108.967764 0 0 1 9.63407-45.761836 84.291698 84.291698 0 0 1 37.630681-36.789306A139.694028 139.694028 0 0 1 514.860001 0.071677q55.517939 0 84.43942 27.065317a88.877515 88.877515 0 0 1 28.902213 67.714673 84.458688 84.458688 0 0 1-25.735815 64.342748 89.686777 89.686777 0 0 1-64.117953 24.406313q-3.487534 0-10.456178-0.462435z m-55.986798 111.755222v-77.843293h77.586385v77.824025z" fill="#A5A5A5" p-id="10131"></path></svg>' : '📭'}</div>
                            <div class="search-no-results-text">${noResultsText}</div>
                            <div class="search-no-results-desc">${noResultsDesc}</div>
                        </div>
                    `);
                    return;
                }

                let resultsHtml = '';
                results.forEach(result => {
                    // 如果有搜索词，则高亮显示；否则直接显示内容
                    const displayContent = query ?
                        highlightSearchTerm(result.matchedContent, query) :
                        result.matchedContent;

                    const messageType = getMessageTypeLabel(result.message);
                    const senderInfo = getSenderInfo(result.message, result.userName);

                    resultsHtml += `
                        <div class="search-result-item" onclick="closeSearchModal()">
                            <div class="search-result-header">
                                <div class="search-result-avatar ${result.message.type}">${senderInfo.avatar}</div>
                                <div class="search-result-user">
                                    ${senderInfo.userName}
                                    <span class="search-result-sender ${result.message.type}">${senderInfo.label}</span>
                                </div>
                                <div class="search-result-time">${result.message.time}</div>
                            </div>
                            <div class="search-result-content">${displayContent}</div>
                            <div class="search-result-type">${messageType}</div>
                        </div>
                    `;
                });

                resultsContainer.html(resultsHtml);
            }

            // 高亮搜索关键词
            function highlightSearchTerm(content, query) {
                const regex = new RegExp(`(${query})`, 'gi');
                return content.replace(regex, '<span class="highlight">$1</span>');
            }

            // 获取消息类型标签
            function getMessageTypeLabel(message) {
                if (message.type === 'file') {
                    return 'file';
                } else if (message.type === 'image') {
                    return 'picture';
                } else {
                    return 'text';
                }
            }

            // 获取发送者信息
            function getSenderInfo(message, userName) {
                if (message.sender === 'sent') {
                    return {
                        avatar: message.avatarUrl ?
                            `<img src="${message.avatarUrl}">` :
                            'D',
                        label: '(I sent)',
                        userName: message.userName
                    };
                } else if (message.sender === 'received') {
                    return {
                        avatar: message.avatarUrl ?
                            `<img src="${message.avatarUrl}">` :
                            'T',
                        label: '(other side)',
                        userName: message.userName
                    };
                }
            }

            // 自动调整输入框高度
            $('.message-input').on('input', function() {
                this.style.height = 'auto';
                this.style.height = Math.min(this.scrollHeight, 120) + 'px';
            });

            // 移动端滑动手势支持
            let startX = 0;
            let startY = 0;

            $('.main-chat').on('touchstart', function(e) {
                if (!isMobile()) return;
                startX = e.originalEvent.touches[0].clientX;
                startY = e.originalEvent.touches[0].clientY;
            });

            $('.main-chat').on('touchmove', function(e) {
                if (!isMobile()) return;
                const currentX = e.originalEvent.touches[0].clientX;
                const currentY = e.originalEvent.touches[0].clientY;
                const diffX = currentX - startX;
                const diffY = Math.abs(currentY - startY);

                // 如果是水平滑动且距离足够，显示侧边栏
                if (diffX > 50 && diffY < 100 && startX < 50) {
                    $('.sidebar').addClass('show');
                    $('.mobile-backdrop').addClass('show');
                }
            });

            // 侧边栏滑动关闭
            $('.sidebar').on('touchstart', function(e) {
                if (!isMobile()) return;
                startX = e.originalEvent.touches[0].clientX;
            });

            $('.sidebar').on('touchmove', function(e) {
                if (!isMobile() || !$(this).hasClass('show')) return;
                const currentX = e.originalEvent.touches[0].clientX;
                const diffX = currentX - startX;

                // 向左滑动关闭侧边栏
                if (diffX < -50) {
                    $('.sidebar').removeClass('show');
                    $('.mobile-backdrop').removeClass('show');
                }
            });

            // 防止移动端双击缩放
            let lastTouchEnd = 0;
            $(document).on('touchend', function(e) {
                const now = (new Date()).getTime();
                if (now - lastTouchEnd <= 300) {
                    e.preventDefault();
                }
                lastTouchEnd = now;
            });

            // 移动端键盘弹出时调整布局
            if (isMobile()) {
                const originalHeight = window.innerHeight;
                $(window).on('resize', function() {
                    const currentHeight = window.innerHeight;
                    if (currentHeight < originalHeight * 0.75) {
                        // 键盘弹出
                        $('.messages-container').css('padding-bottom', '10px');
                    } else {
                        // 键盘收起
                        $('.messages-container').css('padding-bottom', '20px');
                    }
                });
            }

            // 拖拽上传功能
            let dragCounter = 0;

            $('.main-chat').on('dragenter', function(e) {
                e.preventDefault();
                dragCounter++;
                $(this).addClass('drag-over');
            });

            $('.main-chat').on('dragleave', function(e) {
                e.preventDefault();
                dragCounter--;
                if (dragCounter === 0) {
                    $(this).removeClass('drag-over');
                }
            });

            $('.main-chat').on('dragover', function(e) {
                e.preventDefault();
            });

            $('.main-chat').on('drop', function(e) {
                e.preventDefault();
                dragCounter = 0;
                $(this).removeClass('drag-over');

                const files = Array.from(e.originalEvent.dataTransfer.files);
                files.forEach(file => {
                    if (file.type.startsWith('image/')) {
                        const reader = new FileReader();
                        reader.onload = function(e) {
                            uploadedImages.push({
                                file: file,
                                url: e.target.result,
                                name: file.name,
                                size: file.size
                            });
                            updateUploadPreview();
                        };
                        reader.readAsDataURL(file);
                    } else {
                        uploadedFiles.push(file);
                        updateUploadPreview();
                    }
                });
            });
        });
    </script>
</body>
</html>
