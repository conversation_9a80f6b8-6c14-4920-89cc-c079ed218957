/**
 * 用户信息卡片功能验证脚本
 * 确保所有用户头像都具有用户信息卡片功能
 */

window.UserCardVerification = (function() {
    'use strict';

    /**
     * 验证所有用户头像是否正确配置
     */
    function verifyAllUserAvatars() {
        console.log('=== 开始验证用户信息卡片功能 ===');
        
        const results = {
            total: 0,
            configured: 0,
            missing: 0,
            issues: []
        };
        
        // 查找所有用户头像容器
        const $avatarContainers = $('.iCommunity-left');
        results.total = $avatarContainers.length;
        
        console.log(`找到 ${results.total} 个用户头像容器`);
        
        $avatarContainers.each(function(index) {
            const $container = $(this);
            const hasUserCardClass = $container.hasClass('user-avatar-container');
            const hasTriggerClass = $container.find('.user-avatar-trigger').length > 0;
            const hasUserCard = $container.find('.user-info-card').length > 0;
            const hasUserId = $container.attr('data-user-id');
            
            const containerInfo = {
                index: index + 1,
                element: $container[0],
                hasUserCardClass,
                hasTriggerClass,
                hasUserCard,
                hasUserId,
                isConfigured: hasUserCardClass && hasTriggerClass && hasUserCard && hasUserId
            };
            
            if (containerInfo.isConfigured) {
                results.configured++;
                console.log(`✅ 头像 ${index + 1}: 已正确配置`);
            } else {
                results.missing++;
                const issues = [];
                if (!hasUserCardClass) issues.push('缺少 user-avatar-container 类');
                if (!hasTriggerClass) issues.push('缺少 user-avatar-trigger 类');
                if (!hasUserCard) issues.push('缺少 user-info-card 元素');
                if (!hasUserId) issues.push('缺少 data-user-id 属性');
                
                console.log(`❌ 头像 ${index + 1}: ${issues.join(', ')}`);
                results.issues.push({
                    container: index + 1,
                    issues: issues,
                    element: $container[0]
                });
            }
        });
        
        console.log('\n=== 验证结果 ===');
        console.log(`总计: ${results.total} 个头像`);
        console.log(`已配置: ${results.configured} 个`);
        console.log(`未配置: ${results.missing} 个`);
        
        if (results.missing > 0) {
            console.log('\n需要修复的头像:');
            results.issues.forEach(issue => {
                console.log(`- 头像 ${issue.container}: ${issue.issues.join(', ')}`);
            });
        }
        
        return results;
    }

    /**
     * 自动修复缺失的用户信息卡片配置
     */
    function autoFixUserAvatars() {
        console.log('=== 开始自动修复用户头像配置 ===');
        
        let fixedCount = 0;
        
        $('.iCommunity-left').each(function(index) {
            const $container = $(this);
            let needsFix = false;
            
            // 添加缺失的CSS类
            if (!$container.hasClass('user-avatar-container')) {
                $container.addClass('user-avatar-container');
                needsFix = true;
            }
            
            // 确定是主评论还是子评论
            const isSubComment = $container.closest('.comment-reply-item').length > 0;
            const avatarType = isSubComment ? 'sub-comment-avatar' : 'main-comment-avatar';
            
            if (!$container.hasClass(avatarType)) {
                $container.addClass(avatarType);
                needsFix = true;
            }
            
            // 添加data-user-id属性
            if (!$container.attr('data-user-id')) {
                $container.attr('data-user-id', index + 1); // 临时ID
                needsFix = true;
            }
            
            // 修复触发器类
            const $trigger = $container.find('.w-full.h-full');
            if ($trigger.length && !$trigger.hasClass('user-avatar-trigger')) {
                $trigger.addClass('user-avatar-trigger');
                needsFix = true;
            }
            
            // 添加用户信息卡片
            let $userCard = $container.find('.user-info-card');
            if ($userCard.length === 0) {
                // 查找现有的信息卡片容器
                $userCard = $container.find('.iCommunity-left-info');
                if ($userCard.length) {
                    $userCard.addClass('user-info-card');
                    // 更新z-index和样式
                    $userCard.css({
                        'z-index': '50',
                        'min-width': '300px'
                    });
                    if (window.innerWidth >= 768) {
                        $userCard.css('min-width', '400px');
                    }
                    needsFix = true;
                }
            }
            
            if (needsFix) {
                fixedCount++;
                console.log(`✅ 已修复头像 ${index + 1}`);
            }
        });
        
        console.log(`=== 修复完成，共修复 ${fixedCount} 个头像 ===`);
        
        // 重新初始化事件处理器
        if (typeof EventHandler !== 'undefined') {
            EventHandler.init();
            console.log('事件处理器已重新初始化');
        }
        
        return fixedCount;
    }

    /**
     * 测试用户信息卡片功能
     */
    function testUserCardFunctionality() {
        console.log('=== 测试用户信息卡片功能 ===');
        
        const $avatarContainers = $('.user-avatar-container');
        
        if ($avatarContainers.length === 0) {
            console.log('❌ 没有找到配置的用户头像容器');
            return false;
        }
        
        console.log(`找到 ${$avatarContainers.length} 个配置的用户头像容器`);
        
        // 测试第一个头像
        const $firstContainer = $avatarContainers.first();
        const $trigger = $firstContainer.find('.user-avatar-trigger');
        
        if ($trigger.length === 0) {
            console.log('❌ 第一个头像没有触发器');
            return false;
        }
        
        console.log('✅ 用户信息卡片功能配置正确');
        console.log('💡 可以手动测试：鼠标悬停在任何用户头像上查看效果');
        
        return true;
    }

    /**
     * 生成用户信息卡片内容（用于缺失的卡片）
     */
    function generateUserCardContent(userData = {}) {
        const defaultUser = {
            username: userData.username || '用户',
            userLevel: userData.userLevel || '普通用户',
            avatar: userData.avatar || '/images/user.jpg',
            stats: userData.stats || {
                questions: 0,
                posts: 0,
                replies: 0
            }
        };
        
        return `
            <div class="bg-[#fafbff] rounded-xl border border-[#dae9ff]" style="box-shadow: 0 0 1.25rem -0.125rem rgba(84, 111, 138, 0.3);">
                <div class="flex items-start gap-x-2 mb-3 md:mb-0 md:gap-x-8 p-4 md:p-10">
                    <div class="w-[2.5rem] h-[2.5rem] md:w-[8.125rem] md:h-[8.125rem] flex-shrink-0">
                        <img src="${defaultUser.avatar}" alt="${defaultUser.username}" class="w-full h-full rounded-md object-cover">
                    </div>
                    <div class="flex flex-col gap-y-2 md:flex-1">
                        <div class="flex w-full gap-x-2.5 md:gap-x-8 items-end">
                            <div class="name-info">
                                <a href="#" class="text-base md:text-3xl font-bold">${defaultUser.username}</a>
                                <p class="text-xs text-nowrap md:text-xl text-gray-500">${defaultUser.userLevel}</p>
                            </div>
                            <div class="message-btn">
                                <a href="#" class="text-sm px-2 py-1 md:text-xl bg-blue-600 text-white text-nowrap rounded-md md:px-4 md:py-2">
                                    Private message
                                </a>
                            </div>
                        </div>
                        <div class="mt-3 px-2 md:px-0 md:mt-5">
                            <ul class="text-sm justify-between md:text-2xl flex items-center gap-x-5 md:gap-x-10 text-center md:justify-start">
                                <li><span>${defaultUser.stats.questions}</span><p class="text-blue-600">Questions</p></li>
                                <li><span>${defaultUser.stats.posts}</span><p class="text-blue-600">Posts</p></li>
                                <li><span>${defaultUser.stats.replies}</span><p class="text-blue-600">Reply</p></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 完整的初始化和验证流程
     */
    function initializeAndVerify() {
        console.log('=== 用户信息卡片完整初始化 ===');
        
        // 1. 验证当前状态
        const verifyResults = verifyAllUserAvatars();
        
        // 2. 如果有问题，尝试自动修复
        if (verifyResults.missing > 0) {
            console.log('检测到配置问题，开始自动修复...');
            autoFixUserAvatars();
            
            // 重新验证
            const reVerifyResults = verifyAllUserAvatars();
            console.log(`修复后状态: ${reVerifyResults.configured}/${reVerifyResults.total} 个头像已配置`);
        }
        
        // 3. 测试功能
        testUserCardFunctionality();
        
        console.log('=== 初始化完成 ===');
        console.log('所有用户头像现在都应该具有用户信息卡片功能');
    }

    // 公开API
    return {
        verifyAllUserAvatars,
        autoFixUserAvatars,
        testUserCardFunctionality,
        generateUserCardContent,
        initializeAndVerify
    };
})();

// 页面加载完成后自动运行验证
$(document).ready(function() {
    // 延迟执行，确保所有模块都已加载
    setTimeout(function() {
        console.log('用户信息卡片验证脚本已加载');
        console.log('可用命令:');
        console.log('- UserCardVerification.verifyAllUserAvatars(): 验证所有头像配置');
        console.log('- UserCardVerification.autoFixUserAvatars(): 自动修复配置问题');
        console.log('- UserCardVerification.initializeAndVerify(): 完整初始化和验证');
        
        // 自动运行完整验证
        UserCardVerification.initializeAndVerify();
    }, 1000);
});
