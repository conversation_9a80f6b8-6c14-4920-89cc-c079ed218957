<?php
declare (strict_types = 1);

namespace app\admin\controller;

use app\admin\BaseController;
use think\facade\Db;
use think\captcha\facade\Captcha;

class Login extends BaseController
{

    public function index()
    {
        if ($this->request->isPost()) {
            $data = input('post.');

            //验证码校验
            if (!Captcha::check($data['verifycode'], $data['verify'])) {
                $this->error('验证码错误，请重新输入！');
            }

            //验证码用户名和密码
            if(!$data['username'] || !$data['password']){
                $this->error('用户名和密码都不能为空！');
            }

            //获取用户信息
            $where = [
                ["email", "=", $data['username']],
                ["status", "=", 1],
            ];
            $userData = Db::name("User")->where($where)->find();
            if(empty($userData)){
                $this->error('用户不存在或被禁用！');
            }

            $user_role = Db::name("Roles")->field("status, is_admin")->where("id", $userData['role_id'])->find();
            if($user_role['status'] == 0 || $user_role['is_admin'] == 0){
                return $this->error('您没有后台登录权限！');
            }

            // 依据用户表中的salt字段生成hash明码
            $password = generateHashPassword($data['password'], $userData['salt']);
            if (strcasecmp($password, $userData['password']) != 0) {
                return $this->error('密码错误，请重新输入！');
            }

            session('adminId', $userData['id']);
            session('adminUsername', $userData['email']);

            $this->success('登录成功');
        }
        if(session('adminId')){
            $this->redirect('/admin');
        }

        return view();
    }

    //自定义验证码
    public function captcha()
    {
        return Captcha::create();
    }

    //退出登录
    public function logout()
    {
        //清除后台登录信息
        session('adminId', null);
        session('adminUsername', null);

        //$this->redirect('/admin/login');
        echo "<script language=\"javascript\">window.open('/admin/login"."','_top');</script>";
    }

}
