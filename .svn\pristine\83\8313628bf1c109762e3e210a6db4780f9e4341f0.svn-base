<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <title>User - HitGen OpenDEL™</title>
    <link rel="shortcut icon" href="/favicon.ico" />
    <link href="__CSS__/vendors/aos.css" rel="stylesheet">
    <link href="__CSS__/style.css" rel="stylesheet" />
</head>

<body>
    <section class="bg-[url(__IMG__/backgrounds/m_bj.jpg)] bg-no-repeat pb-10 bg-size-[100%] md:bg-[url(__IMG__/backgrounds/pc_bj.jpg)] md:bg-size-[100%] md:pb-20">

        {include file="public:header"}

        <div class="w-11/12 mx-auto md:w-10/12">
            <div class="mt-5 Navigation mb-3.5 md:mb-14">
                <ul class="text-[#999] flex items-center text-md md:text-xl">
                    <li>
                        <a href="/" class="text-[#155797]">
                            Home
                        </a>
                    </li>
                    <li class="max-w-[12.5rem] line-clamp-1 md:max-w-2xs">
                        Personal Center
                    </li>
                </ul>
            </div>

            <!-- data-aos="fade-up" -->
            <div class="md:flex md:flex-row md:gap-x-4">

                {include file="user:left"}

                <div class="bg-white border border-[var(--border-color)] rounded-lg shadow-2xs
                md:w-2/3 md:rounded-2xl
                ">
                    <div
                        class="grid grid-cols-2 text-sm md:text-[1.5625rem] md:px-[2.5rem] md:pt-4 md:flex Roboto_Bold md:text-2xl md:gap-x-[4.375rem] user-tab-link">
                        <span data-tab="information" class="user-tab-item">
                            Basic information
                        </span>
                        <span data-tab="settings" class="user-tab-item">
                            Security Settings
                        </span>
                    </div>
                    <div class="user-profile">
                        <!-- 资料修改 -->
                        <div data-tab="information" class="profile-item">
                            <form action="" method="post" id="userForm" class="p-4 pb-10 md:w-full md:p-[3.125rem]" enctype="multipart/form-data">
                                <div class="flex flex-col gap-y-4 text-sm md:text-xl md:gap-y-7">
                                    <div class="flex items-center text-[.75rem] gap-2 md:gap-x-[1.25rem] Roboto_Bold md:text-xl md:mb-[1.25rem]">
                                        <div class="w-[3.75rem] h-[3.75rem] rounded-full overflow-hidden *:
                                        md:w-[10rem] md:h-[10rem]">
                                            <!-- 默认头像 -->
                                            <img src="{$user.avatar ?? '__IMG__/user-2.jpg'}" alt="" class="object-cover w-full h-full"
                                                id="user_view" />
                                        </div>
                                        <div class="w-[8.5rem] h-[2.5rem] bg-white border border-[#dae9ff] rounded-full overflow-hidden text-center flex items-center justify-center relative px-1
                                        md:w-[15rem] md:h-[3.75rem]">
                                            <span>Upload new picture</span>
                                            <input type="file" name="avatar" id="file_btn"
                                                class="absolute w-full h-full opacity-0 left-0 top-0 z-10 cursor-pointer">
                                        </div>
                                        <button type="button"
                                            class="rounded-full w-[4.375rem] h-[2.5rem] bg-[#ffefdd] text-[#111111] md:w-[7.5rem] md:h-[3.75rem] cursor-pointer"
                                            id="Delete_btn">Delete</button>
                                    </div>

                                    <div class="input-item">
                                        <label class="Roboto_Bold">
                                            <span class="text-[#ff0000] align-text-top">*</span> Email
                                        </label>
                                        <div class="relative flex flex-col mt-2">
                                            <input type="email" class="w-full h-[3.125rem] border border-[#d8e2ff] rounded-md bg-[#f8fdff] bg-[url(__IMG__/icons/youxiang.png)] bg-no-repeat bg-[.75rem_center] bg-size-[1.25rem] px-5 pl-12
                                                md:h-[5rem] md:pl-16 md:bg-auto md:bg-[1.2rem_center] md:pr-32
                                                " value="{$user.email}" disabled autocomplete="off" />
                                        </div>
                                    </div>

                                    <div class="input-item">
                                        <label class="Roboto_Bold">
                                            <span class="text-[#ff0000] align-text-top">*</span> Country/Region
                                        </label>
                                        <div class="relative flex flex-col mt-2 z-10">
                                            <select name="country" id="country" class="country w-full h-[3.125rem] border border-[#d8e2ff] rounded-md bg-transparent bg-[url(__IMG__/icons/news.png)] bg-no-repeat bg-[.75rem_center] bg-size-[1.25rem] px-5 pl-12 pr-12 appearance-none text-[#ddd] cursor-pointer
                                                    md:h-[5rem] md:pl-16 md:bg-auto md:bg-[1.2rem_center]
                                                    ">
                                                <option value="" disabled selected style="color:#ddd;">Please select
                                                    your country/region</option>
                                                {volist name="country" id="vo"}
                                                <option value="{$vo.en_name}" style="color: #333;" {if $user.country==$vo.en_name}selected{/if}>{$vo.en_name} {$vo.cn_name}</option>
                                                {/volist}
                                            </select>
                                            <button type="button"
                                                class="bg-[url(__IMG__/icons/mti-jiantouyou.png)] bg-no-repeat bg-[.75rem_center] bg-size-[1.125rem] rounded-md top-1/2 -translate-y-1/2 absolute right-2 w-10 h-full -z-10">
                                            </button>
                                        </div>
                                        <div class=" hidden error text-[#ff0000] mt-2
                                            md:text-base
                                            ">
                                            Please fill in the information.
                                        </div>
                                    </div>
                                    <div class="input-item grid grid-cols-1 gap-y-4
                                        md:grid-cols-2 md:gap-x-6
                                        ">
                                        <div class="">
                                            <label for="first_name" class="Roboto_Bold">
                                                <span class="text-[#ff0000] align-text-top">*</span> First Name
                                            </label>
                                            <div class="relative flex flex-col mt-2">
                                                <input type="text" name="first_name" id="first_name"
                                                    placeholder="Please enter your first name" class="w-full h-[3.125rem] border border-[#d8e2ff] rounded-md bg-white bg-[url(__IMG__/icons/yonghu-1.png)] bg-no-repeat bg-[.75rem_center] bg-size-[1.25rem] px-5 pl-12 pr-6
                                                        md:h-[5rem] md:pl-16 md:bg-auto md:bg-[1.2rem_center]
                                                        " autocomplete="off" value="{$user.first_name}" />
                                            </div>
                                            <div class=" hidden error text-[#ff0000] mt-2
                                                md:text-base
                                                ">
                                                Please fill in the information.
                                            </div>
                                        </div>
                                        <div class="">
                                            <label for="last_name" class="Roboto_Bold">
                                                <span class="text-[#ff0000] align-text-top">*</span> Last Name
                                            </label>
                                            <div class="relative flex flex-col mt-2">
                                                <input type="text" name="last_name" id="last_name"
                                                    placeholder="Please enter your last name" class="w-full h-[3.125rem] border border-[#d8e2ff] rounded-md bg-white bg-[url(__IMG__/icons/yonghu-1.png)] bg-no-repeat bg-[.75rem_center] bg-size-[1.25rem] px-5 pl-12 pr-6
                                                        md:h-[5rem] md:pl-16 md:bg-auto md:bg-[1.2rem_center]
                                                        " autocomplete="off" value="{$user.last_name}" />
                                            </div>
                                            <div class=" hidden error text-[#ff0000] mt-2
                                                md:text-base
                                                ">
                                                Please fill in the information.
                                            </div>
                                        </div>
                                    </div>
                                    <div class="input-item">
                                        <label for="title" class="Roboto_Bold">
                                            Title:
                                        </label>
                                        <div class="relative flex flex-col mt-2">
                                            <input type="text" name="title" id="title"
                                                placeholder="Please enter your title"
                                                class="w-full h-[3.125rem] border border-[#d8e2ff] rounded-md bg-white bg-[url(__IMG__/icons/zhiweimoban.png)] bg-no-repeat bg-[.75rem_center] bg-size-[1.25rem] px-5 pl-12 pr-6
                                                    md:h-[5rem] md:pl-16 md:bg-auto md:bg-[1.2rem_center]
                                                    " autocomplete="off" value="{$user.title}" />
                                        </div>
                                    </div>

                                    <div class="input-item">
                                        <label for="phone" class="Roboto_Bold">
                                            <span class="text-[#ff0000] align-text-top">*</span>
                                            Phone:
                                        </label>
                                        <div class="relative flex flex-col mt-2">
                                            <input type="tel" name="phone" id="phone"
                                                placeholder="Enter your phone number" class="w-full h-[3.125rem] border border-[#d8e2ff] rounded-md bg-white bg-[url(__IMG__/icons/phone.png)] bg-no-repeat bg-[.75rem_center] bg-size-[1.25rem] px-5 pl-12 pr-6
                                                    md:h-[5rem] md:pl-16 md:bg-auto md:bg-[1.2rem_center]
                                                    " autocomplete="off" value="{$user.phone}" />
                                        </div>
                                        <div class=" hidden error text-[#ff0000] mt-2
                                            md:text-base
                                            ">
                                            Please enter the phone number.
                                        </div>
                                    </div>

                                    <div class="input-item">
                                        <label for="organization" class="Roboto_Bold">
                                            <span class="text-[#ff0000] align-text-top">*</span>
                                            Organization/Institution/Corporation
                                        </label>
                                        <div class="relative flex flex-col mt-2">
                                            <input type="text" name="organization" id="organization"
                                                placeholder="Please enter your organization/institution/corporation"
                                                class="w-full h-[3.125rem] border border-[#d8e2ff] rounded-md bg-white bg-[url(__IMG__/icons/jigouguanli.png)] bg-no-repeat bg-[.75rem_center] bg-size-[1.25rem] px-5 pl-12 pr-6
                                                    md:h-[5rem] md:pl-16 md:bg-auto md:bg-[1.2rem_center]" autocomplete="off" value="{$user.organization}" />
                                        </div>
                                        <div class=" hidden error text-[#ff0000] mt-2
                                            md:text-base
                                            ">
                                            Please fill in the information.
                                        </div>
                                    </div>

                                    <div class="input-item">
                                        <label for="shipping_address" class="Roboto_Bold">
                                            Shipping Address:
                                        </label>
                                        <div class="relative flex flex-col mt-2">
                                            <input type="text" name="shipping_address" id="shipping_address"
                                                placeholder="Enter your shipping addressEnter your shipping address"
                                                class="w-full h-[3.125rem] border border-[#d8e2ff] rounded-md bg-white bg-[url(__IMG__/icons/icon_address.png)] bg-no-repeat bg-[.75rem_center] bg-size-[1.25rem] px-5 pl-12 pr-6
                                                    md:h-[5rem] md:pl-16 md:bg-auto md:bg-[1.2rem_center]
                                                    " autocomplete="off" value="{$user.shipping_address}" />
                                        </div>
                                    </div>
                                    <div class="input-item">
                                        <label for="city" class="Roboto_Bold">
                                            City:
                                        </label>
                                        <div class="relative flex flex-col mt-2">
                                            <input type="text" name="city" id="city" placeholder="Enter your city name"
                                                class="w-full h-[3.125rem] border border-[#d8e2ff] rounded-md bg-white bg-[url(__IMG__/icons/chengshi.png)] bg-no-repeat bg-[.75rem_center] bg-size-[1.25rem] px-5 pl-12 pr-6
                                                    md:h-[5rem] md:pl-16 md:bg-auto md:bg-[1.2rem_center]
                                                    " autocomplete="off" value="{$user.city}" />
                                        </div>
                                    </div>
                                    <div class="input-item">
                                        <label for="postcode" class="Roboto_Bold">
                                            Postcode:
                                        </label>
                                        <div class="relative flex flex-col mt-2">
                                            <input type="text" name="postcode" id="postcode"
                                                placeholder="Please enter the postal code for your delivery address"
                                                class="w-full h-[3.125rem] border border-[#d8e2ff] rounded-md bg-white bg-[url(__IMG__/icons/youzhengbianma.png)] bg-no-repeat bg-[.75rem_center] bg-size-[1.25rem] px-5 pl-12 pr-6
                                                    md:h-[5rem] md:pl-16 md:bg-auto md:bg-[1.2rem_center]
                                                    " autocomplete="off" value="{$user.postcode}" />
                                        </div>
                                    </div>

                                    <div class="input-item mt-2
                                        md:mt-5
                                        ">
                                        <button type="submit"
                                            class="bg-[#f08411] text-white rounded-xl text-base py-2 w-full cursor-pointer Roboto_Bold
                                                md:h-[5rem] md:inline-block md:w-[30.625rem] md:text-xl md:rounded-full"
                                            id="submitBtn">Save changes</button>
                                    </div>
                                </div>
                            </form>
                        </div>

                        <!-- 密码修改 -->
                        <div data-tab="settings" class="profile-item md:max-w-3xl">
                            <form action="" method="post" id="securityForm" class="p-4 md:w-full md:p-[3.125rem] pb-10">
                                <div class="flex flex-col gap-y-4 text-sm md:text-xl md:gap-y-7">
                                    <div class="input-item">
                                        <label class="Roboto_Bold">
                                            <span class="text-[#ff0000] align-text-top">*</span> Email
                                        </label>
                                        <div class="relative flex flex-col mt-2">
                                            <input type="email" name="profile_email" id="profile_email"
                                                 class="w-full h-[3.125rem] border border-[#d8e2ff] bg-[#f8fdff] rounded-md bg-[url(__IMG__/icons/youxiang.png)] bg-no-repeat bg-[12px_center] bg-size-[1.25rem] px-5 pl-12
                                            md:h-[5rem] md:pl-16 md:bg-auto md:bg-[1.2rem_center] md:pr-32
                                            " value="{$user.email}" disabled autocomplete="off">
                                            <button type="button" class="text-white bg-[#f08411] rounded-md top-1/2 -translate-y-1/2 absolute right-2 px-2 py-1.5 cursor-pointer
                                                md:right-4 md:text-base md:py-3 md:px-4" id="SendOtp">Send OTP</button>
                                        </div>
                                    </div>
                                    <div class="input-item">
                                        <label for="captcha" class="Roboto_Bold">
                                            <span class="text-[#ff0000] align-text-top">*</span> Captcha
                                        </label>
                                        <div class="relative flex flex-col mt-2">
                                            <input type="text" name="captcha" id="captcha"
                                                placeholder="Please enter email verification code" class="w-full h-[3.125rem] border border-[#d8e2ff] rounded-md bg-white bg-[url(__IMG__/icons/yanzhengma.png)] bg-no-repeat bg-[12px_center] bg-size-[1.25rem] px-5 pl-12 pr-20
                                                md:h-[5rem] md:pl-16 md:bg-auto md:bg-[1.2rem_center]
                                                " autocomplete="off">
                                        </div>
                                        <div class=" hidden error text-[#ff0000] mt-2
                                        md:text-base
                                        ">
                                            Please enter the verification information
                                        </div>
                                    </div>

                                    <div class="input-item">
                                        <label for="password" class="Roboto_Bold">
                                            <span class="text-[#ff0000] align-text-top">*</span> Password
                                        </label>
                                        <div class="relative flex flex-col mt-2">
                                            <input type="password" name="password" id="password"
                                                placeholder="Advise a secure password" class="w-full h-[3.125rem] border border-[#d8e2ff] rounded-md bg-white bg-[url(__IMG__/icons/mima.png)] bg-no-repeat bg-[12px_center] bg-size-[1.25rem] px-5 pl-12 pr-12
                                                md:h-[5rem] md:pl-16 md:bg-auto md:bg-[1.2rem_center]
                                                " autocomplete="off">
                                            <button type="button" class="bg-[url(__IMG__/icons/yanjing_yincang_o.png)] bg-no-repeat bg-[12px_center] bg-size-[1.25rem] rounded-md top-1/2 -translate-y-1/2 absolute right-2 w-10 h-full cursor-pointer
                                                md:bg-size-auto" id="passwordEye">
                                            </button>
                                        </div>
                                        <div class="hidden flex error text-[#ff0000] mt-2 items-center gap-x-1.5
                                        md:text-base
                                        ">
                                            <img src="__IMG__/icons/tis.png" alt=""> 8-16
                                            characters must contain both digits and letters
                                        </div>
                                    </div>

                                    <div class="input-item">
                                        <label for="password_confirm" class="Roboto_Bold">
                                            <span class="text-[#ff0000] align-text-top">*</span> Confirm Password
                                        </label>
                                        <div class="relative flex flex-col mt-2">
                                            <input type="password" name="password_confirm"
                                                id="password_confirm" placeholder="Advise a secure password"
                                                class="w-full h-[3.125rem] border border-[#d8e2ff] rounded-md bg-white bg-[url(__IMG__/icons/mima.png)] bg-no-repeat bg-[12px_center] bg-size-[1.25rem] px-5 pl-12 pr-12
                                                md:h-[5rem] md:pl-16 md:bg-auto md:bg-[1.2rem_center]
                                                " autocomplete="off">
                                            <button type="button" class="bg-[url(__IMG__/icons/yanjing_yincang_o.png)] bg-no-repeat bg-[12px_center] bg-size-[1.25rem] rounded-md top-1/2 -translate-y-1/2 absolute right-2 w-10 h-full cursor-pointer
                                                md:bg-size-auto" id="passwordEye2">
                                            </button>
                                        </div>
                                        <div class=" hidden flex error text-[#ff0000] mt-2 items-center gap-x-1.5
                                        md:text-base
                                        ">
                                            <img src="__IMG__/icons/tis.png" alt=""> 8-16
                                            characters must contain both digits and letters
                                        </div>
                                    </div>

                                    <div class="input-item mt-2
                                        md:mt-5 md:text-right
                                        ">
                                        <button type="submit"
                                            class="bg-[#f08411] text-white rounded-full text-base py-2 w-full cursor-pointer Roboto_Bold
                                                md:h-[4.375rem] md:inline-block md:w-[17.5rem] md:text-xl md:rounded-full"
                                            id="ModifySubmitBtn">Submit</button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>

                </div>
            </div>

        </div>
    </section>

    {include file="public:footer"}

    {include file="public:foot"}
    
    <script src="__JS__/TabSwitch.js"></script>


    <script>
        initTabSwitch('.user-tab-item', '.profile-item');

        document.addEventListener('DOMContentLoaded', function () {
            // 处理 select 元素的颜色变化
            const handleSelectColor = (select) => {
                const updateColor = () => {
                    select.style.color = select.value === "" ? "#ddd" : "#333";
                };
                select.addEventListener('change', updateColor);
                updateColor(); // 初始化颜色
            };

            // 初始化所有 country select
            document.querySelectorAll('select.country').forEach(handleSelectColor);

           // 表单验证配置
            const validationFields = [
                { id: 'country', name: 'Country' },
                { id: 'first_name', name: 'First Name' },
                { id: 'last_name', name: 'Last Name' },
                { id: 'organization', name: 'Organization' },
                { id: 'phone', name: 'Phone' }
            ];
            // 表单验证函数
            const validateField = (field) => {
                const input = document.getElementById(field.id);
                if (!input) return { valid: true }; // 字段不存在时跳过验证

                const value = input.value.trim();
                const errorDiv = input.parentElement.parentElement.querySelector('.error');

                if (!value) {
                    errorDiv?.classList.remove('hidden');
                    return { valid: false, element: input };
                }

                errorDiv?.classList.add('hidden');
                return { valid: true };
            };

            //用户资料修改
            $('#userForm').on('submit', function(e) {
                //阻止表单提交
                e.preventDefault();

                var valid = true;

                $('.error').addClass('hidden'); // 隐藏所有错误提示

                // 验证所有字段
                const validationResults = validationFields.map(validateField);
                const isValid = validationResults.every(result => result.valid);
                if (!isValid) {
                    // 找到第一个无效的输入框并聚焦
                    const firstInvalid = validationResults.find(result => !result.valid);
                    firstInvalid?.element?.focus();
                    return false;
                }

                // 禁用提交按钮
                var $submitBtn = $('#submitBtn');
                $submitBtn.prop('disabled', true).addClass('opacity-50 cursor-not-allowed');

                // 获取表单元素
                var form = $(this)[0]; // 获取原生 DOM 元素
                var formData = new FormData(form); // 创建 FormData 对象
                // 发送AJAX请求
                $.ajax({
                    url: '/user/edit',
                    type: 'POST',
                    data: formData,
                    dataType: 'json',
                    processData: false, // 告诉 jQuery 不要处理数据
                    contentType: false, // 告诉 jQuery 不要设置 content-Type 请求头
                    success: function(data) {
                        if (data.code === 1) {
                            //修改成功
                            layer.msg(data.msg, { icon: 1, time: 2000 }, () => {
                                location.href = data.url;
                            });
                        } else {
                            //修改失败
                            layer.msg('Error: ' + data.msg, { icon: 2 });
                        }
                    },
                    error: function(xhr, status, error) {
                        layer.msg('An error occurred: ' + error, { icon: 2 });
                    },
                    complete: function() {
                        // 无论成功失败，都重新启用按钮
                        $submitBtn.prop('disabled', false).removeClass('opacity-50 cursor-not-allowed');
                    }
                });
            });

            // 处理文件上传
            const fileBtn = document.getElementById('file_btn');
            const userView = document.getElementById('user_view');
            fileBtn.addEventListener('change', function (e) {
                const file = e.target.files[0];
                if (!file) return;

                // 检查文件类型
                const validTypes = ['image/jpeg', 'image/jpg', 'image/png'];
                if (!validTypes.includes(file.type)) {
                    alert('只能上传 JPG 或 PNG 格式的图片！');
                    fileBtn.value = ''; // 清空选择
                    return;
                }

                // 创建本地预览URL
                const imageUrl = URL.createObjectURL(file);
                userView.src = imageUrl;

                // 清理URL对象
                userView.onload = function () {
                    URL.revokeObjectURL(imageUrl);
                };
            });
            // 处理删除按钮
            const deleteBtn = document.getElementById('Delete_btn');
            deleteBtn.addEventListener('click', function () {
                userView.src = '__IMG__/user-2.jpg'; // 重置为默认图片
                fileBtn.value = ''; // 清空文件选择
            });
        });

        document.addEventListener('DOMContentLoaded', function () {
            // 导航切换
            const tabItems = document.querySelectorAll('.user-tab-item');
            const profileItems = document.querySelectorAll('.profile-item');

            tabItems.forEach((tab, index) => {
                tab.addEventListener('click', () => {
                    // 移除所有tab的active类
                    tabItems.forEach(item => item.classList.remove('active'));
                    // 为当前点击的tab添加active类
                    tab.classList.add('active');

                    // 处理对应的profile-item
                    profileItems.forEach((item, i) => {
                        if (i === index) {
                            item.classList.remove('hidden');
                        } else {
                            item.classList.add('hidden');
                        }
                    });
                });
            });

            // 修改密码
            // 通用验证函数
            function validateField(input, rules) {
                const value = input.value.trim();
                const error = input.parentElement.parentElement.querySelector('.error');
                if (error) error.classList.add('hidden');

                for (const rule of rules) {
                    if (!rule.validate(value)) {
                        if (error) {
                            error.innerHTML = rule.message;
                            error.classList.remove('hidden');
                        }
                        return false;
                    }
                }
                return true;
            }

            // 验证规则
            const validationRules = {
                email: [
                    {
                        validate: value => value.length > 0,
                        message: 'Please enter your email address.'
                    },
                    {
                        validate: value => /^[\w.-]+@[\w.-]+\.[a-zA-Z]{2,}$/.test(value),
                        message: 'Please enter the correct email format'
                    }
                ],
                password: [
                    {
                        validate: value => value.length > 0,
                        message: '<img src="__IMG__/icons/tis.png" alt=""> Please enter password'
                    },
                    {
                        validate: value => /^(?=.*[A-Za-z])(?=.*\d).{8,16}$/.test(value),
                        message: '<img src="__IMG__/icons/tis.png" alt=""> 8-16 characters must contain both digits and letters'
                    }
                ]
            };

            // 发送邮件验证码
            $('#SendOtp').click(function() {
                var $btn = $(this); // 保存按钮引用

                var $emailInput = $btn.prev('input'); // 获取邮箱输入框
                var $error = $btn.closest('.input-item').find('.error'); // 错误提示元素
                var emailReg = /^[\w.-]+@[\w.-]+\.[a-zA-Z]{2,}$/; // 邮箱正则

                $error.addClass('hidden'); // 先隐藏错误提示

                // 验证邮箱是否为空
                if (!$emailInput.val()) {
                    $error.text('Please fill in the email information').removeClass('hidden');
                    $emailInput.focus();
                    return;
                }

                // 验证邮箱格式
                if (!emailReg.test($emailInput.val())) {
                    $error.text('Please enter a valid email address').removeClass('hidden');
                    $emailInput.focus();
                    return;
                }

                // 禁用发送按钮
                $btn.prop('disabled', true).addClass('opacity-50');

                // 发送AJAX请求
                $.ajax({
                    url: '/send_captcha',
                    type: 'POST',
                    data: {email: $emailInput.val(), type: 'login'},
                    dataType: 'json',
                    success: function(data) {
                        if (data.code === 1) {
                            //发送成功
                            layer.msg(data.msg, { icon: 1, time: 2000 });
                        } else {
                            //发送失败
                            layer.msg('Error: ' + data.msg, { icon: 2 });
                        }
                    },
                    error: function(xhr, status, error) {
                        layer.msg('An error occurred: ' + error, { icon: 2 });
                    },
                    complete: function() {
                        // 无论成功失败，都重新启用按钮
                        $btn.prop('disabled', false).removeClass('opacity-50');
                    }
                });
            });

            // 修改密码
            $('#securityForm').on('submit', function(e) {
                //阻止表单提交
                e.preventDefault();

                let valid = true;
                let firstInvalidInput = null;

                // 验证邮箱
                const emailInput = document.getElementById('profile_email');
                if (!validateField(emailInput, validationRules.email)) {
                    valid = false;
                    firstInvalidInput = firstInvalidInput || emailInput;
                }

                // 验证验证码
                const captchaInput = document.getElementById('captcha');
                if (!validateField(captchaInput, [{
                    validate: value => value.length > 0,
                    message: 'Please enter the verification code'
                }])) {
                    valid = false;
                    firstInvalidInput = firstInvalidInput || captchaInput;
                }

                // 验证密码
                const pwdInput = document.getElementById('password');
                if (!validateField(pwdInput, validationRules.password)) {
                    valid = false;
                    firstInvalidInput = firstInvalidInput || pwdInput;
                }

                // 验证确认密码
                const confirmPwdInput = document.getElementById('password_confirm');
                const confirmPwdRules = [
                    {
                        validate: value => value.length > 0,
                        message: 'Please confirm your password'
                    },
                    {
                        validate: value => value === pwdInput.value,
                        message: 'The passwords entered twice are inconsistent'
                    }
                ];
                if (!validateField(confirmPwdInput, confirmPwdRules)) {
                    valid = false;
                    firstInvalidInput = firstInvalidInput || confirmPwdInput;
                }

                if (!valid) {
                    firstInvalidInput.focus();
                    return false
                }

                // 禁用提交按钮
                var $submitBtn = $('#ModifySubmitBtn');
                $submitBtn.prop('disabled', true).addClass('opacity-50 cursor-not-allowed');

                // 发送AJAX请求
                $.ajax({
                    url: '/user/editpassword',
                    type: 'POST',
                    data: $(this).serialize(),
                    dataType: 'json',
                    success: function(data) {
                        if (data.code === 1) {
                            //注册成功
                            layer.msg(data.msg, { icon: 1, time: 2000 }, () => {
                                location.href = data.url;
                            });
                        } else {
                            //注册失败
                            layer.msg('Error: ' + data.msg, { icon: 2 });
                        }
                    },
                    error: function(xhr, status, error) {
                        layer.msg('An error occurred: ' + error, { icon: 2 });
                    },
                    complete: function() {
                        // 无论成功失败，都重新启用按钮
                        $submitBtn.prop('disabled', false).removeClass('opacity-50 cursor-not-allowed');
                    }
                });
            });

            // 密码显示/隐藏处理
            function bindPasswordEye(eyeId) {
                const eye = document.getElementById(eyeId);
                if (eye) {
                    eye.addEventListener('click', function () {
                        const input = eye.parentElement.querySelector('input');
                        if (input) {
                            input.type = input.type === 'password' ? 'text' : 'password';
                            eye.style.backgroundImage = `url('__IMG__/icons/yanjing_yincang_${input.type === 'password' ? 'o' : '1'}.png')`;
                        }
                    });
                }
            }
            bindPasswordEye('passwordEye');
            bindPasswordEye('passwordEye2');
        });
    </script>

</body>

</html>