/**
 * 帖子详情页面主文件
 * 初始化所有模块和功能
 *
 * ===== 后端开发者注意 =====
 *
 * 1. 这个文件是评论系统的入口文件
 * 2. 页面加载时会自动初始化评论系统
 * 3. 会从DOM中提取现有评论数据
 * 4. 后端需要在页面渲染时输出评论的HTML结构
 * 5. 新的评论操作会调用后端API (详见 BACKEND-API-DOCS.md)
 */
(function() {
    'use strict';

    // 页面配置
    const pageConfig = {
        enableComments: true,
        enableNotifications: true,
        autoSave: false,
        maxCommentLength: 1000,
        minCommentLength: 2
    };

    /**
     * 页面初始化
     */
    function initPage() {
        // 检查依赖
        if (!checkDependencies()) {
            console.error('缺少必要的依赖模块');
            return;
        }

        try {
            // 初始化AOS动画
            if (typeof AOS !== 'undefined') {
                AOS.init();
            }

            // 初始化评论系统
            initCommentSystem();

            // 初始化事件处理
            initEventHandlers();

            // 初始化其他功能
            initOtherFeatures();

            console.log('帖子详情页面初始化完成');

        } catch (error) {
            console.error('页面初始化失败:', error);
            CommentUtils.showMessage('页面初始化失败，请刷新重试', 'error');
        }
    }

    /**
     * 检查依赖模块
     * @returns {boolean} 是否所有依赖都存在
     */
    function checkDependencies() {
        const requiredModules = [
            'jQuery', 
            'CommentUtils', 
            'TemplateManager', 
            'CommentSystem', 
            'EventHandler'
        ];

        const missing = requiredModules.filter(module => {
            return typeof window[module] === 'undefined';
        });

        if (missing.length > 0) {
            console.error('缺少依赖模块:', missing);
            return false;
        }

        return true;
    }

    /**
     * 初始化评论系统
     */
    function initCommentSystem() {
        // 配置评论系统
        CommentSystem.init(pageConfig);

        // 注册事件回调
        CommentSystem.on('onCommentAdded', function(comment) {
            console.log('新评论已添加:', comment);
            updatePageStats();
        });

        CommentSystem.on('onReplyAdded', function(reply, parentComment) {
            console.log('新回复已添加:', reply, '父评论:', parentComment);
            updatePageStats();
        });

        // 添加调试信息
        const loadedComments = CommentSystem.getAllComments();
        console.log(`评论系统初始化完成，加载了 ${loadedComments.length} 个评论`);

        // 检查DOM和系统数据是否一致
        const $domComments = $('.reply-list[data-comment-id]');
        if ($domComments.length > 0 && loadedComments.length === 0) {
            console.warn('警告: DOM中有评论但系统中无数据，可能存在加载问题');
        }

        CommentSystem.on('onCommentDeleted', function(comment) {
            console.log('评论已删除:', comment);
            updatePageStats();
        });
    }

    /**
     * 初始化事件处理器
     */
    function initEventHandlers() {
        EventHandler.init();
    }

    /**
     * 初始化其他功能
     */
    function initOtherFeatures() {
        // 初始化搜索功能
        initSearchFeature();

        // 初始化消息通知
        initMessageNotification();

        // 初始化用户下拉菜单
        initUserDropdown();

        // 初始化返回顶部按钮
        initBackToTop();

        // 初始化页面统计
        updatePageStats();
    }

    /**
     * 初始化搜索功能
     */
    function initSearchFeature() {
        // 搜索按钮点击事件
        $('#search_btn').on('click', function() {
            $('#search_form').toggle();
        });

        // 搜索表单提交事件
        $('#search_form').on('submit', function(e) {
            e.preventDefault();
            const keyword = $(this).find('input[type="text"]').val().trim();
            if (keyword) {
                // 这里可以实现搜索功能
                console.log('搜索关键词:', keyword);
                CommentUtils.showMessage(`搜索功能暂未实现: ${keyword}`, 'info');
            }
        });
    }

    /**
     * 初始化消息通知
     */
    function initMessageNotification() {
        // 消息按钮悬停事件
        $('#message_btn').on('mouseenter', function() {
            $('#message_drop').show();
        }).on('mouseleave', function() {
            setTimeout(() => {
                if (!$('#message_drop:hover').length) {
                    $('#message_drop').hide();
                }
            }, 200);
        });

        // 消息下拉框悬停事件
        $('#message_drop').on('mouseleave', function() {
            $(this).hide();
        });

        // 清空消息按钮
        $('#Empty').on('click', function() {
            $('#message_cont ul').empty();
            $('#message_more').hide();
            $('#no_message').show();
        });
    }

    /**
     * 初始化用户下拉菜单
     */
    function initUserDropdown() {
        $('.btn-name').on('click', function(e) {
            e.stopPropagation();
            $('.user-name-drop').toggle();
        });

        // 点击其他地方关闭下拉菜单
        $(document).on('click', function() {
            $('.user-name-drop').hide();
        });

        $('.user-name-drop').on('click', function(e) {
            e.stopPropagation();
        });
    }

    /**
     * 初始化返回顶部按钮
     */
    function initBackToTop() {
        const $backToTop = $('#btn_up');

        // 监听滚动事件
        $(window).on('scroll', CommentUtils.throttle(function() {
            if ($(window).scrollTop() > 300) {
                $backToTop.fadeIn();
            } else {
                $backToTop.fadeOut();
            }
        }, 100));

        // 点击返回顶部
        $backToTop.on('click', function() {
            $('html, body').animate({ scrollTop: 0 }, 500);
        });
    }

    /**
     * 初始化移动端菜单
     */
    function initMobileMenu() {
        $('#menu_btn').on('click', function() {
            $('#nav_list').toggleClass('hidden');
        });

        // 移动端导航子菜单
        $('.navigation').on('click', function(e) {
            if ($(window).width() < 768) {
                e.preventDefault();
                $(this).find('.secondary').toggle();
            }
        });

        $('.navigation-item').on('click', function(e) {
            if ($(window).width() < 768) {
                e.preventDefault();
                $(this).find('.three-menu').toggle();
            }
        });
    }

    /**
     * 更新页面统计信息
     */
    function updatePageStats() {
        const stats = CommentSystem.getStats();
        
        // 更新评论数量显示
        const $commentHeader = $('.p_postlist header');
        if ($commentHeader.length) {
            $commentHeader.text(`${stats.totalComments} replies`);
        }

        // 更新页面标题中的评论数（如果需要）
        const originalTitle = document.title;
        if (stats.totalComments > 0 && !originalTitle.includes('(')) {
            document.title = `${originalTitle} (${stats.totalComments})`;
        }
    }

    /**
     * 页面卸载时的清理工作
     */
    function cleanup() {
        // 清理事件处理器
        if (typeof EventHandler !== 'undefined') {
            EventHandler.cleanup();
        }

        // 清理定时器等
        $(window).off('scroll resize');
    }

    /**
     * 错误处理
     */
    function handleError(error, context = '') {
        console.error(`错误 [${context}]:`, error);
        
        if (typeof CommentUtils !== 'undefined') {
            CommentUtils.showMessage('操作失败，请重试', 'error');
        } else {
            alert('操作失败，请重试');
        }
    }

    // 全局错误处理
    window.addEventListener('error', function(e) {
        handleError(e.error, '全局错误');
    });

    // 页面加载完成后初始化
    $(document).ready(function() {
        initPage();
        initMobileMenu();
    });

    // 页面卸载时清理
    $(window).on('beforeunload', function() {
        cleanup();
    });

    // 暴露一些全局方法供调试使用
    window.PostDetailsPage = {
        getCommentStats: () => CommentSystem.getStats(),
        getAllComments: () => CommentSystem.getAllComments(),
        clearAllComments: () => CommentSystem.clearAll(),
        showMessage: (msg, type) => CommentUtils.showMessage(msg, type)
    };

    // 调试函数
    window.debugComments = function() {
        console.log('=== 评论系统调试信息 ===');
        console.log('1. 当前评论数据:', CommentSystem.getAllComments());
        console.log('2. 统计信息:', CommentSystem.getStats());
        console.log('3. DOM中的评论容器:');
        $('.reply-list[data-comment-id]').each(function() {
            const $this = $(this);
            console.log('  - 评论ID:', $this.attr('data-comment-id'), '容器:', $this[0]);
        });
        console.log('4. DOM中的回复项目:');
        $('.comment-reply-item').each(function(index) {
            const $this = $(this);
            const $parentReplyList = $this.closest('.reply-list');
            console.log(`  - 回复 ${index + 1}:`, $this[0], '父评论ID:', $parentReplyList.attr('data-comment-id'));
        });
        console.log('5. 活动编辑器:', EventHandler.activeEditors);
        console.log('========================');

        // 显示在页面上
        const stats = CommentSystem.getStats();
        CommentUtils.showMessage(`调试信息已输出到控制台。当前: ${stats.totalComments} 评论, ${stats.totalReplies} 回复`, 'info');
    };

    // 状态检查函数
    window.checkCommentStatus = function() {
        const systemComments = CommentSystem.getAllComments();
        const domComments = $('.reply-list[data-comment-id]');

        console.log('=== 评论状态检查 ===');
        console.log(`系统中的评论: ${systemComments.length} 个`);
        console.log(`DOM中的评论容器: ${domComments.length} 个`);

        if (domComments.length > 0 && systemComments.length === 0) {
            console.warn('⚠️ 检测到数据不一致：DOM中有评论但系统中无数据');
            console.log('建议运行修复: fixAndInitComments()');
            CommentUtils.showMessage('检测到评论数据不一致，建议点击 FIX 按钮修复', 'warning');
        } else if (domComments.length === systemComments.length) {
            console.log('✓ 数据一致性检查通过');
            CommentUtils.showMessage('评论系统状态正常', 'success');
        } else {
            console.warn(`⚠️ 数据数量不匹配: DOM(${domComments.length}) vs 系统(${systemComments.length})`);
        }

        return {
            domCount: domComments.length,
            systemCount: systemComments.length,
            consistent: domComments.length === systemComments.length
        };
    };

    // HTML实体测试函数
    window.testHtmlEntities = function() {
        console.log('=== 测试HTML实体处理 ===');

        const testCases = [
            '<span>@用户</span>&nbsp;测试内容',
            '<span style="color:#155797;" data-showname="张三">@张三</span>&nbsp;这是回复',
            '&nbsp;&nbsp;多个空格&nbsp;&nbsp;测试',
            '&lt;script&gt;&amp;test&amp;&lt;/script&gt;'
        ];

        testCases.forEach((testCase, index) => {
            const result = CommentUtils.stripHtml(testCase);
            console.log(`测试 ${index + 1}:`);
            console.log(`  输入: "${testCase}"`);
            console.log(`  输出: "${result}"`);
            console.log(`  包含&nbsp;: ${result.includes('&nbsp;') ? '❌ 是' : '✅ 否'}`);
            console.log('');
        });

        console.log('测试完成');
        CommentUtils.showMessage('HTML实体测试完成，请查看控制台', 'info');
    };

    // 检查"暂无评论"提示显示逻辑
    window.checkNoCommentTip = function() {
        console.log('=== 检查"暂无评论"提示显示逻辑 ===');

        const comments = CommentSystem.getAllComments();
        const $noCommentTip = $('.no-comment-tip');
        const $itemReplyList = $('.item-reply-list');

        console.log(`当前评论数量: ${comments.length}`);
        console.log(`"暂无评论"提示数量: ${$noCommentTip.length}`);
        console.log(`评论列表容器: ${$itemReplyList.length > 0 ? '存在' : '不存在'}`);

        // 检查逻辑是否正确
        if (comments.length === 0) {
            if ($noCommentTip.length > 0) {
                console.log('✅ 正确：没有评论时显示"暂无评论"提示');
                CommentUtils.showMessage('✅ "暂无评论"提示显示正确', 'success');
            } else {
                console.log('❌ 错误：没有评论但未显示"暂无评论"提示');
                CommentUtils.showMessage('❌ 缺少"暂无评论"提示', 'error');

                // 尝试修复
                console.log('尝试添加"暂无评论"提示...');
                if ($itemReplyList.length > 0) {
                    $itemReplyList.append('<div class="no-comment-tip py-10 text-center">暂无评论</div>');
                    console.log('已添加"暂无评论"提示');
                }
            }
        } else {
            if ($noCommentTip.length === 0) {
                console.log('✅ 正确：有评论时隐藏"暂无评论"提示');
                CommentUtils.showMessage('✅ "暂无评论"提示隐藏正确', 'success');
            } else {
                console.log('❌ 错误：有评论但仍显示"暂无评论"提示');
                CommentUtils.showMessage('❌ "暂无评论"提示应该隐藏', 'error');

                // 尝试修复
                console.log('尝试移除多余的"暂无评论"提示...');
                $noCommentTip.remove();
                console.log('已移除"暂无评论"提示');
            }
        }

        console.log('检查完成');
    };

})();
