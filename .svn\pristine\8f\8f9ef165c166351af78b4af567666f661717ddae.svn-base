<?php

declare(strict_types=1);

namespace app\admin\controller;

use app\admin\controller\Common;
use think\facade\Db;

class News extends Common
{
    public function index()
    {
        $params = request()->param();
        $where = [];

        if (!empty($params['keyword'])) {
            $where[] = ["title|content", "like", "%" . $params['keyword'] . "%"];
        }

        $List = Db::name('News')
            ->where($where)
            ->order("id desc")
            ->paginate([
                'query'     =>  request()->param(), //url额外参数
                'list_rows' => 20, //每页数量
            ]);

        return view("", [
            "List" => $List,
            "params" => $params,
        ]);
    }

    public function add()
    {
        if ($this->request->isPost()) {
            $data = input('post.');

            if(!$data['title']){
                $this->error("必填项未填！");
            }

            if(!trim($data['seo_url'])){
                $data['seo_url'] = seoFriendlyUrl($data['title']);
            }
            if(Db::name('News')->where("seo_url", $data['seo_url'])->find()){
                throw new \Exception("URL已存在！");
            }

            if($_FILES['image']['name']) $data['image'] = $this->upload(request()->file("image"));

            $data['create_time'] = date("Y-m-d H:i:s");
            $s = Db::name("News")->insertGetId($data);
            if ($s) {
                $this->success('添加成功！');
            } else {
                $this->error("添加失败，请重试！");
            }
        } else {
            //新闻分类
            $category = Db::name("News_category")->column("id, name");

            return view("", [
                "category" => $category
            ]);
        }
    }

    public function edit()
    {
        if ($this->request->isPost()) {
            $data = input('post.');

            if(!$data['title']){
                $this->error("必填项未填！");
            }

            if(!trim($data['seo_url'])){
                $data['seo_url'] = seoFriendlyUrl($data['title']);
            }
            $where = [
                ['id', '<>', $data['id']],
                ['seo_url', '=', $data['seo_url']],
            ];
            if(Db::name('News')->where($where)->find()){
                throw new \Exception("URL已存在！");
            }

            if($_FILES['image']['name']) $data['image'] = $this->upload(request()->file("image"));

            $data['update_time'] = date("Y-m-d H:i:s");
            $s = Db::name("News")->strict(false)->save($data);
            if ($s) {
                $this->success('修改成功！');
            } else {
                $this->error("修改失败，请重试！");
            }
        } else {
            $id = input('id');
            $getone = Db::name("News")->where("id", $id)->find();

            //新闻分类
            $category = Db::name("News_category")->column("id, name");

            return view("", [
                "category" => $category,
                "getone" => $getone,
            ]);
        }
    }

    public function del()
    {
        $id = input('id');
        $s = Db::name("News")->where("id", $id)->delete();
        if ($s) {
            $this->success("删除成功！");
        } else {
            $this->error("删除失败！");
        }
    }


    public function category()
    {
        $List = Db::name('News_category')
            ->order("id desc")
            ->paginate([
                'query'     =>  request()->param(), //url额外参数
                'list_rows' => 20, //每页数量
            ]);

        return view("", [
            "List" => $List
        ]);
    }

    public function add_category()
    {
        if ($this->request->isPost()) {
            $data = input('post.');

            if(!$data['name']){
                $this->error("必填项未填！");
            }

            $data['create_time'] = date("Y-m-d H:i:s");
            $s = Db::name("News_category")->insertGetId($data);
            if ($s) {
                $this->success('添加成功！');
            } else {
                $this->error("添加失败，请重试！");
            }
        } else {
            return view();
        }
    }

    public function edit_category()
    {
        if ($this->request->isPost()) {
            $data = input('post.');

            if(!$data['name']){
                $this->error("必填项未填！");
            }

            $data['update_time'] = date("Y-m-d H:i:s");
            $s = Db::name("News_category")->strict(false)->save($data);
            if ($s) {
                $this->success('修改成功！');
            } else {
                $this->error("修改失败，请重试！");
            }
        } else {
            $id = input('id');
            $getone = Db::name("News_category")->where("id", $id)->find();

            return view("", [
                "getone" => $getone,
            ]);
        }
    }

    public function del_category()
    {
        $id = input('id');
        $s = Db::name("News_category")->where("id", $id)->delete();
        if ($s) {
            $this->success("删除成功！");
        } else {
            $this->error("删除失败！");
        }
    }

}
