<?php
namespace app\home\controller;

use app\home\controller\Publics;
use think\facade\Db;
use think\facade\View;

class Common extends Publics
{
    public function initialize()
	{
        //测试
        session("userId", 3);

        $this->getBasic();

        if(session("userId")) {
            $this->getLeft();
        }
    }

    //获取全局配置
    public function getBasic(){
        //全局配置
        $basic = Db::name("Basic")->where("Id", 1)->find();
        View::assign("basic", $basic);

        //头部菜单
        //产品-服务
        $product = Db::name("Product")->column("id, seo_url, name");
        foreach($product as $key=>$val){
            $product[$key]['service'] = Db::name("Product_relation")->alias("pr")
                ->field("s.id, s.seo_url, s.name")
                ->leftjoin("Service s", "pr.related_id=s.id")
                ->where(["product_id"=>$val['id'], "type"=>1])
                ->order("pr.sort asc")
                ->select();
        }
        View::assign("menu_product", $product);
        //资源分类
        $resources_category = Db::name("Resource_category")->select();
        View::assign("menu_resources_category", $resources_category);
    }

    //公用左侧栏
    public function getLeft(){
        if(!session('userId')) {
            $this->error("Please log in first!", "/login");
        }

        $user_id = session('userId');
        $user = Db::name("User")->where("id", $user_id)->find();
        View::assign("user", $user);

        //未使用的优惠券数量
        $where = [
                "uc.user_id" => $user_id,
                "uc.is_used" => 0,  //未使用
            ];
        $currentTime = date('Y-m-d H:i:s');
        $coupon_count = Db::name("User_coupon")->alias("uc")
                ->leftjoin("Coupon c", "uc.coupon_id=c.id")
                ->where($where)
                ->whereTime('uc.start_time', '<=', $currentTime) // 开始时间小于等于当前时间
                ->whereTime('uc.end_time', '>=', $currentTime)   // 结束时间大于等于当前时间
                ->count();
        View::assign("coupon_count", $coupon_count);

        //未读消息
        $where = [
            "user_id" => $user_id,
            "is_read" => 0,  //未读
        ];
        $user_message = Db::name("User_message")->where($where)->select();
        View::assign("user_message", $user_message);
    }
 }