<!DOCTYPE html>
<html>
<head lang="en">
    <meta charset="UTF-8">
    <title></title>

    {include file="common:head"}
</head>

<body>
    <div class="menus_r">
        <div id="con_two_1" style="display: block;">
            <div class="maps">
                <i></i>
                <p class="current">您当前的位置：<a href="{:url('Public/main')}">首页</a>&nbsp;>&nbsp; <a href="">保密文件下载记录</a></p>

                <div class="search-container">
                    <form method="get" action="{:url('index')}">
                        <input
                            type="text"
                            name="keyword"
                            placeholder="请输入邮箱/姓名或者产品/服务名称"
                            value="{$params.keyword ?? ''}"
                            class="search-input"
                        >
                        <button type="submit" class="search-button">搜索</button>
                    </form>
                </div>
            </div>

            <div class="form_con">
                <table class="bor_cen">
                    <thead>
                        <tr class="mid_01">
                            <td class="mid_one"></td>
                            <td class="mid_one">邮箱</td>
                            <td class="mid_one">姓名</td>
                            <td class="mid_one">下载内容</td>
                            <td class="mid_one">IP</td>
                            <td class="mid_one">下载时间</td>
                            <td class="mid_one">产品/服务</td>
                            <td class="mid_one">产品经理</td>
                        </tr>
                    </thead>
                    <tbody>
                    {volist name="List" key="k" id="vo"}
                        <tr  class="mid_02">
                            <td class="mid_one">{$k}</td>
                            <td class="mid_one"><a href="/iCommunity/user/{$vo.user_id}" target="_blank">{$vo.email}</a></td>
                            <td class="mid_one"><a href="/iCommunity/user/{$vo.user_id}" target="_blank">{$vo.first_name} {$vo.last_name}</a></td>
                            <td class="mid_one">{$vo.file_name ?: $vo.url}</td>
                            <td class="mid_one">{$vo.ip}</td>
                            <td class="mid_one">{$vo.download_time}</td>
                            <td class="mid_one">{$vo.product_name}</td>
                            <td class="mid_one">{$vo.product_managers}</td>
                        </tr>
                    {/volist}
                    </tbody>
                </table>

                <div class="interpret">
                    {$List|raw}
                </div>
            </div>
        </div>
    </div>

    {include file="common:foot"}
</body>
</html>