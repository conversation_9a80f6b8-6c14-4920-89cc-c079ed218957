<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <title>news-details - HitGen OpenDEL™</title>
    <link rel="shortcut icon" href="/favicon.ico" />
    <link href="__CSS__/vendors/aos.css" rel="stylesheet">
    <link href="__CSS__/style.css" rel="stylesheet" />
</head>

<body>
    <section class=" md:bg-size-[100%] md:pb-0 bg-[url(__IMG__/backgrounds/m_bj.jpg)] bg-no-repeat pb-10 bg-size-[100%] md:bg-[url(__IMG__/backgrounds/pc_bj.jpg)]">

        {include file="public:header"}

        <div class="w-11/12 mx-auto md:w-10/12">
            <div class="mt-5 Navigation mb-2.5 md:mb-10">
                <ul class="text-[#999] flex items-center text-md md:text-xl">
                    <li>
                        <a href="/" class="text-[#155797]">
                            Home
                        </a>
                    </li>
                    <li class="max-w-[12.5rem] line-clamp-1 md:max-w-2xs">
                        <a href="/news/" class="text-[#155797]">
                            DELHunter
                        </a>
                    </li>
                    <li class="max-w-[12.5rem] line-clamp-1 md:max-w-2xs">
                        News
                    </li>
                </ul>
            </div>
        </div>

        <main class="w-full">
            <div class="w-11/12 mx-auto py-6
            md:w-10/12 md:pb-20 md:pt-0" data-aos="fade-up">
                <div class="flex flex-col gap-5">
                    <div class="bg-[#f8fdff] border border-[#e0eaff] rounded-xl p-5 md:p-10">
                        <header class="border-b border-[#e0eaff] pb-3 mb-4 md:pb-8">
                            <h1 class="text-xl text-[#155797] mb-2.5 md:mb-5 md:text-3xl">
                                {$getone.title}
                            </h1>
                            <time datetime="" class="text-base">
                                {:date('j F Y', strtotime($getone.publish_date))}
                            </time>
                        </header>
                        <div class="text-sm text-[#111111] about-container">
                            {$getone.content|raw}
                        </div>
                    </div>
                    <div class="mt-5 text-[#155290]">
                        <a href="/news/" class="flex items-center gap-x-3 md:text-xl">
                            <img src="__IMG__/icons/changjiantou-zuoshang3.png" class="w-4 md:w-[1.5rem]" alt="">
                            <span>Back to DELHunter </span>
                        </a>
                    </div>
                </div>
            </div>
        </main>
    </section>

    {include file="public:footer"}

    {include file="public:foot"}

</body>

</html>