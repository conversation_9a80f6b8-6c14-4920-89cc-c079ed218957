<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>No login prompt</title>
    <meta name="viewport" content="width=device-width,initial-scale=1.0,maximum-scale=1.0,minimum-scale=1.0,user-scalable=no">
    <link rel="shortcut icon" href="/favicon.ico" />
    <link href="__CSS__/vendors/aos.css" rel="stylesheet">
    <link href="__CSS__/style.css" rel="stylesheet" />

    <style>
        .countdown {
            font-size: 24px;
            color: #e74c3c;
            margin: 20px 0;
        }
        .ellipsis::after {
            content: "...";
            display: inline-block;
            width: 20px;
            animation: ellipsis-anim 1.5s infinite;
        }
        @keyframes ellipsis-anim {
            0% { content: "."; }
            33% { content: ".."; }
            66% { content: "..."; }
        }
        #seconds{
            color: #ff6d27;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <section
        class="pb-5 md:pb-20 relative bg-[url(__IMG__/bg.jpg)] ">
        <span class="absolute top-0 left-0 w-full h-full bg-[url(__IMG__/tm-left.png)] bg-no-repeat bg-[top_left] z-0 bg-size-[10rem] md:bg-size-[26.9375rem]"></span>
        <span class="absolute top-0 right-0 w-full h-full bg-[url(__IMG__/tm-right.png)] bg-no-repeat bg-[top_right] z-0 bg-size-[10rem] md:bg-size-[25.0625rem]"></span>

        {include file="public:header"}

        <div class="w-11/12 mx-auto md:w-10/12">
            <div class="mt-5 Navigation mb-2.5 md:mb-10">
                <ul class="text-[#999] flex items-center text-md md:text-xl">

                </ul>
            </div>
        </div>

        <main class="w-full">
            <div class="w-11/12 mx-auto py-6 md:w-10/12 md:pb-20 md:pt-0" data-aos="fade-up">
                <div class="flex flex-col gap-5">
                    <div class="bg-[#f8fdff] border border-[#e0eaff] rounded-xl p-5 md:p-10">
                        <div style="text-align: center; margin: 100px 0;">
                            <h1 class="text-xl text-[#000] mb-2.5 md:mb-5 md:text-3xl">Please log in first<span class="ellipsis"></span></h1>

                            <div class="countdown text-sm text-[#111111] about-container">
                                <p>You will be redirected to the login page in <span id="seconds">5</span> seconds...</p>
                                <p>If not, please <a href="/login" style="color: #3498db;">click here</a>.</p>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </main>
    </section>

    {include file="public:footer"}

    {include file="public:foot"}

    <script>
        $(document).ready(function() {
            let seconds = 5;
            const countdown = setInterval(function() {
                seconds--;
                $("#seconds").text(seconds);

                if (seconds <= 0) {
                    clearInterval(countdown);
                    window.location.href = "/login";
                }
            }, 1000);
        });
    </script>
</body>
</html>