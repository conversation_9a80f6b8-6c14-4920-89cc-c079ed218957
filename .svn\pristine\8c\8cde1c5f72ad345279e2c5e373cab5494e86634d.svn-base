<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <title>iCommunity - HitGen OpenDEL™</title>
    <link rel="shortcut icon" href="/favicon.ico" />
    <link href="__CSS__/vendors/aos.css" rel="stylesheet">
    <link href="__CSS__/style.css" rel="stylesheet" />
</head>

<body>
    <section class="bg-[url(__IMG__/backgrounds/m_bj.jpg)] bg-no-repeat pb-5 bg-size-[100%] md:bg-[url(__IMG__/backgrounds/pc_bj.jpg)] md:bg-size-[100%] md:pb-8">

        {include file="public:header"}

        <div class="w-11/12 mx-auto md:w-10/12">
            <div class="mt-5 Navigation mb-3.5 md:mb-14">
                <ul class="text-[#999] flex items-center text-md md:text-xl">
                    <li>
                        <a href="/" class="text-[#155797]">
                            Home
                        </a>
                    </li>
                    <li class="max-w-[200px] line-clamp-1 md:max-w-2xs">
                        iCommunity
                    </li>
                </ul>
            </div>

            <!-- data-aos="fade-up" -->
            <div class="contact" data-aos="fade-up">
                <h1 class="text-2xl md:text-5xl md:mb-10 mb-5 Roboto_Bold"> iCommunity</h1>
                <div class="flex flex-col gap-5">
                    <div
                        class="bg-white rounded-tl-xl rounded-tr-xl flex flex-col p-5 border border-[#dae9ff] md:p-10 md:py-16">
                        <div class="flex flex-col justify-center">
                            <header class="mb-2.5">
                                <h2 class="text-xl Roboto_Bold mb-2 md:text-5xl ">
                                    What is Opendelclub icommunity ?
                                </h2>
                            </header>
                            <div class="text-[#666] text-sm leading-6 mb-3 md:text-xl md:mb-8">
                                <p class="md:line-clamp-6">
                                    iCommunity is your hub to connect, learn, and get inspired by like-minded
                                    individuals in DEL or drug discovery. Here, you will:Connect with like-minded
                                    scientific peers, Access cutting-edge and Spark inspiration and drive breakthroughs.
                                    Join our community to get access to DEL technology resources and communicate with
                                    scientific peers.
                                </p>

                            </div>
                            <div class="flex bg-[#f08411] rounded-md text-sm md:text-xl md:h-[60px] md:w-[340px]">
                                <a href="/iCommunity/post" class="text-white flex justify-center items-center gap-2 w-full h-full p-3 md:p-5 md:gap-4">
                                    <img src="__IMG__/icons/tianjia.png" alt="" class="w-[1rem] md:w-[1.5rem]">
                                    <span>ASK THE COMMUNITY</span>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <main class="w-full">
        <div class="w-11/12 mx-auto pt-6 md:w-10/12 md:pt-20" data-aos="fade-up">
            <!-- 数据展示和搜索 -->
            <div class="data-browsing h-[300px] bg-[url(__IMG__/Icommunity/bg_m.jpg)] bg-no-repeat bg-cover bg-center relative md:bg-[url(__IMG__/Icommunity/bg_pc.jpg)] md:h-lvh ">
                <!-- 数据展示 -->
                <div class="absolute bottom-0 w-full">
                    <!-- 数据集合 -->
                    <div class="flex bg-white p-3.5 flex-wrap gap-3 md:h-[100px] md:items-center md:gap-x-5 md:px-8">
                        <div class="browsing-item">
                            <strong>{$post_count|number_format}</strong>
                            <span>Posts</span>
                        </div>
                        <div class="browsing-item">
                            <strong>{$reply_count|number_format}</strong>
                            <span>Replies</span>
                        </div>
                        <div class="browsing-item">
                            <strong>{$user_count|number_format}</strong>
                            <span>Members</span>
                        </div>
                    </div>

                    <!-- 数据搜索 -->
                    <div class="browsing-search px-3.5 pb-5 md:pb-20">
                        <h3 class="text-2xl Roboto_Bold py-3 text-center text-white md:text-5xl md:py-[50px]">
                            Have a question?
                        </h3>
                        <form action="/iCommunity" method="get" class="relative w-full">
                            <div class="browsing-search-box flex items-center gap-x-2.5 relative md:max-w-[1100px] md:mx-auto">
                                <input type="text" name="q" value="{$keyword}" placeholder="Search Opendel resources" class="w-full bg-white h-10 rounded-md border border-[#e0eaff] p-2 pl-4 text-xs pr-10 md:h-[80px] md:text-xl md:pr-[90px] md:pl-[40px]" />

                                <button type="submit" class="w-10 h-10 bg-[url(__IMG__/icons/sousuo.png)] bg-no-repeat bg-center bg-size-[1rem] rounded-full cursor-pointer absolute right-0 top-1/2 -translate-y-1/2 md:w-[80px] md:h-[80px] md:bg-auto">
                                    <span class="sr-only">Search</span>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <!-- 搜索 -->
        </div>

        <div class="bg-[#f8fdff] pb-7">
            <div class="w-11/12 mx-auto py-6 mb-5 md:w-10/12 md:py-20  relative z-50" data-aos="fade-up">
                <!-- 标题 -->
                <div class="browsing-header mb-8 md:mb-10">
                    <div class="browsing-header-title mb-2 md:mb-5">
                        <h3 class="text-xl Roboto_Bold md:text-5xl">
                            What have you missed:
                        </h3>
                    </div>
                    <div class="browsing-header-switch text-sm text-[#f08411] md:text-2xl md:mt-5">
                        Make sure you are logged in to see all content.
                    </div>
                </div>

                <!-- 切换 -->
                <div class="iCommunity-tab">
                    <!-- tab按钮只有3个分类 -->
                    <div class="w-full overflow-x-auto mb-5 md:mb-9">
                        <div class="flex flex-nowrap min-w-max gap-x-4 pb-2 text-sm md:text-2xl md:gap-x-10">
                            <div data-tab="news" class="iCommunity-btn">
                                Recent News
                            </div>
                            <div data-tab="activity" class="iCommunity-btn">
                                Recent Activity
                            </div>
                            <div data-tab="discussion" class="iCommunity-btn">
                                New Discussion/Questions
                            </div>
                        </div>
                    </div>

                    <!-- 相对应的内容 -->
                    <div class="iCommunity-content-all bg-white border border-[#c4d7ff] rounded-md">
                        <div data-tab="news" class="iCommunity-content-item">
                            <ul class="grid grid-cols-1 mb-2" role="list">
                                {volist name="news" id="vo"}
                                <li>
                                    <div class="iCommunity-left w-[40px] h-[40px] rounded-md flex-shrink-0 relative md:w-[80px] md:h-[80px] border border-[#dae9ff]">
                                        <div class="w-full h-full cursor-pointer">
                                            <img src="{$vo.user.avatar ?? '__IMG__/user-1.jpg'}" alt="" class="w-full h-full rounded-md object-cover">
                                        </div>
                                        <div class="iCommunity-left-info absolute left-0 top-full z-10" style="display: none;">
                                            <div class="bg-[#fafbff] rounded-xl" style="box-shadow: 0 0 20px -2px rgba(84, 111, 138, 0.3);">
                                                <div class="flex items-start gap-x-2 mb-3 md:mb-0 md:gap-x-8 p-4 md:p-10">
                                                    <div class="w-[40px] h-[40px] md:w-[130px] md:h-[130px] flex-shrink-0">
                                                        <img src="{$vo.user.avatar ?? '__IMG__/user-1.jpg'}" alt="" class="w-full h-full rounded-md object-cover">
                                                    </div>
                                                    <!-- 右侧个人信息 -->
                                                    <div class="flex flex-col gap-y-2 md:flex-1">
                                                        <!-- 名称、经验、私信 -->
                                                        <div class="flex w-full gap-x-2.5 md:gap-x-8 items-end">
                                                            <div class="name-info">
                                                                <a href="/iCommunity/user/{$vo.user.id}" class="text-base md:text-3xl Roboto_Bold ">{$vo.user.first_name} {$vo.user.last_name}</a>
                                                                <p class="text-xs text-nowrap md:text-xl text-[#999]">{$vo.user.role_name}</p>
                                                            </div>
                                                            <!-- 私信 -->
                                                            {if $vo.user.id != session('userId')}
                                                            <div class="message-btn">
                                                                <a href="/iCommunity/message?user={$vo.user.first_name}_{$vo.user.last_name}" class="text-sm px-2 py-1 md:text-xl bg-[#155797] text-white text-nowrap rounded-md md:px-4 md:py-2 ">
                                                                    Private message
                                                                </a>
                                                            </div>
                                                            {/if}
                                                        </div>
                                                        <!-- 发帖数量等 -->
                                                        <div class="mt-3 px-2 md:px-0 md:mt-5">
                                                            <ul class="text-sm justify-between md:text-2xl flex items-center gap-x-5 md:gap-x-10 text-center md:justify-start">
                                                                <li>
                                                                    <span>{$vo.user.question_count}</span>
                                                                    <p class="text-[#155797]">Questions</p>
                                                                </li>
                                                                <li>
                                                                    <span>{$vo.user.posting_count}</span>
                                                                    <p class="text-[#155797]">Posts</p>
                                                                </li>
                                                                <li>
                                                                    <span>{$vo.user.reply_count}</span>
                                                                    <p class="text-[#155797]">Reply</p>
                                                                </li>
                                                            </ul>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="badge-about">
                                                    <!-- 徽章-关于 tab按钮 -->
                                                    <div class="flex items-center gap-x-10 border-b border-[#dae9ff] px-10">
                                                        <div class="badge-about-btn active">
                                                            Badge
                                                        </div>
                                                        <div class="badge-about-btn">
                                                            About
                                                        </div>
                                                    </div>
                                                    <!-- tab详情 -->
                                                    <div class="tab-content p-5 md:p-10">
                                                        <div class="tab-content-item flex gap-x-10">
                                                            <!-- 没有徽章的时候显示 -->
                                                            <div class="tab-content-item-no-badge text-sm md:text-2xl" style="display: {$vo.user.role_id == 1 || $vo.user.role_id == 2 || $vo.user.role_id == 3 ? 'block' : 'none'};">
                                                                {$vo.user.first_name} {$vo.user.last_name} did not receive any badges yet.
                                                            </div>
                                                            <!-- 有徽章的时候 -->
                                                            <div class="tab-content-item-badge" style="display: {$vo.user.role_id == 4 || $vo.user.role_id == 5 || $vo.user.role_id == 6 ? 'block' : 'none'};">
                                                                <div class="flex items-center gap-x-2.5 md:gap-x-10 badge-item ">
                                                                    <div class=" flex flex-col gap-y-2 items-center">
                                                                        <div class="w-[40px] md:w-[75px] md:h-[81px] flex-shrink-0 bg-white">
                                                                            <img src="__IMG__/iCommunity/icon_1.png" alt="" class="w-full h-full object-cover">
                                                                        </div>

                                                                        <div class="text-sm md:text-xl text-[#999]">
                                                                            <p>Junior Badge</p>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="tab-content-item-badge" style="display: {$vo.user.role_id == 5 || $vo.user.role_id == 6 ? 'block' : 'none'};">
                                                                <div class="flex items-center gap-x-2.5 md:gap-x-10 badge-item">
                                                                    <div class=" flex flex-col gap-y-2 items-center">
                                                                        <div class="w-[40px] md:w-[75px] md:h-[81px] flex-shrink-0 bg-white">
                                                                            <img src="__IMG__/iCommunity/icon_2.png" alt=""
                                                                                class="w-full h-full object-cover">
                                                                        </div>

                                                                        <div class="text-sm md:text-xl text-[#999]">
                                                                            <p>Intermediate Badge</p>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="tab-content-item-badge" style="display: {$vo.user.role_id == 6 ? 'block' : 'none'};">
                                                                <div class="flex items-center gap-x-2.5 md:gap-x-10 badge-item">
                                                                    <div class=" flex flex-col gap-y-2 items-center">
                                                                        <div class="w-[40px] md:w-[75px] md:h-[81px] flex-shrink-0 bg-white">
                                                                            <img src="__IMG__/iCommunity/icon_3.png" alt="" class="w-full h-full object-cover">
                                                                        </div>

                                                                        <div class="text-sm md:text-xl text-[#999]">
                                                                            <p>Senior Badge</p>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>

                                                        <div class="tab-content-item flex flex-col gap-x-2.5 md:gap-x-10 gap-y-3 md:gap-y-5 text-sm md:text-xl" style="display: none;">
                                                            <div class="about-item flex flex-col md:flex-row md:items-center justify-between gap-y-2 md:gap-y-0 md:gap-x-10">
                                                                <div class="about-item-left flex-1/2">
                                                                    Registration Date
                                                                </div>
                                                                <div class="about-item-right Roboto_Bold flex-1/2">
                                                                    <p>{:date('j F Y', strtotime($vo.user.create_time))}</p>
                                                                </div>
                                                            </div>
                                                            <div class="about-item flex flex-col md:flex-row md:items-center justify-between gap-y-2 md:gap-y-0 md:gap-x-10">
                                                                <div class="about-item-left flex-1/2">
                                                                    Organization/Institution/Corporation
                                                                </div>
                                                                <div class="about-item-right Roboto_Bold flex-1/2">
                                                                    <p>{$vo.user.organization}</p>
                                                                </div>
                                                            </div>
                                                            <div class="about-item flex flex-col md:flex-row md:items-center justify-between gap-y-2 md:gap-y-0 md:gap-x-10">
                                                                <div class="about-item-left flex-1/2">
                                                                    Title
                                                                </div>
                                                                <div class="about-item-right Roboto_Bold flex-1/2">
                                                                    <p>{$vo.user.title}</p>
                                                                </div>
                                                            </div>
                                                            <div class="about-item flex flex-col md:flex-row md:items-center justify-between gap-y-2 md:gap-y-0 md:gap-x-10">
                                                                <div class="about-item-left flex-1/2">
                                                                    Location (City, Country, Earth)
                                                                </div>
                                                                <div class="about-item-right Roboto_Bold flex-1/2">
                                                                    <p>{$vo.user.country}</p>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="iCommunity-right md:pt-5 flex-1">
                                        <div class="iCommunity-right-info flex md:flex-row flex-col gap-x-2 mb-2 text-sm md:gap-x-3.5 md:text-xl md:mb-5">
                                            <div class="iCommunity-right-title-name">
                                                <a href="/iCommunity/user/{$vo.user.id}" class="text-[#155797]">{$vo.user.first_name} {$vo.user.last_name}</a>
                                            </div>
                                            <div class="iCommunity-right-title-time text-[#999]">
                                                {$vo.user.role_name} | Published in
                                                <a href="/iCommunity/topic/{$vo.topic}" class="underline text-[#999]">{$vo.topic}</a>
                                            </div>
                                        </div>
                                        <div class="iCommunity-right-content mb-2 md:mb-5">
                                            <div class="iCommunity-right-content-title mb-2">
                                                <a href="/iCommunity/news/{$vo.id}" class="text-base Roboto_Bold line-clamp-1 text-[#999] md:text-2xl">
                                                    {$vo.title}
                                                </a>
                                            </div>
                                            <div class="iCommunity-right-content-info text-sm line-clamp-3 md:text-xl">
                                                {$vo.content|strip_tags}
                                            </div>
                                        </div>
                                        <div class="iCommunity-right-time text-[#999] text-sm flex items-center justify-between md:justify-start md:gap-x-5 md:text-xl">
                                            <div class="iCommunity-right-time-left">
                                                {:date('F j, Y, g:i A', strtotime($vo.create_time))}
                                            </div>
                                            <div class="iCommunity-right-time-right">
                                                <a href="/iCommunity/news/{$vo.id}" class="text-[#999] flex items-center gap-x-1 md:gap-x-2">
                                                    <img src="__IMG__/icons/pinglun.png" alt="" class="w-[1rem] md:w-[1.5rem]">
                                                    <span>{$vo.reply_count}</span>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                                {/volist}
                            </ul>
                            <div class="text-sm p-3 md:py-[40px] md:px-[57px] md:text-xl">
                                <button type="button" class="show-more-activity-btn rounded-md bg-[#155797] text-white py-3.5 w-full cursor-pointer md:h-[80px]">
                                    SHOW MORE ACTIVITY
                                </button>
                            </div>
                        </div>

                        <div data-tab="activity" class="iCommunity-content-item">
                            <ul class="grid grid-cols-1 mb-2" role="list">
                                {volist name="activity" id="vo"}
                                <li>
                                    <div class="iCommunity-left w-[40px] h-[40px] rounded-md flex-shrink-0 relative md:w-[80px] md:h-[80px] border border-[#dae9ff]">
                                        <div class="w-full h-full cursor-pointer">
                                            <img src="{$vo.user.avatar ?? '__IMG__/user-1.jpg'}" alt="" class="w-full h-full rounded-md object-cover">
                                        </div>
                                        <div class="iCommunity-left-info absolute left-0 top-full z-10" style="display: none;">
                                            <div class="bg-[#fafbff] rounded-xl" style="box-shadow: 0 0 20px -2px rgba(84, 111, 138, 0.3);">
                                                <div class="flex items-start gap-x-2 mb-3 md:mb-0 md:gap-x-8 p-4 md:p-10">
                                                    <div class="w-[40px] h-[40px] md:w-[130px] md:h-[130px] flex-shrink-0">
                                                        <img src="{$vo.user.avatar ?? '__IMG__/user-1.jpg'}" alt="" class="w-full h-full rounded-md object-cover">
                                                    </div>
                                                    <!-- 右侧个人信息 -->
                                                    <div class="flex flex-col gap-y-2 md:flex-1">
                                                        <!-- 名称、经验、私信 -->
                                                        <div class="flex w-full gap-x-2.5 md:gap-x-8 items-end">
                                                            <div class="name-info">
                                                                <a href="/iCommunity/user/{$vo.user.id}" class="text-base md:text-3xl Roboto_Bold ">{$vo.user.first_name} {$vo.user.last_name}</a>
                                                                <p class="text-xs text-nowrap md:text-xl text-[#999]">{$vo.user.role_name}</p>
                                                            </div>
                                                            <!-- 私信 -->
                                                             {if $vo.user.id != session('userId')}
                                                            <div class="message-btn">
                                                                <a href="/iCommunity/message?user={$vo.user.first_name}_{$vo.user.last_name}" class="text-sm px-2 py-1 md:text-xl bg-[#155797] text-white text-nowrap rounded-md md:px-4 md:py-2 ">
                                                                    Private message
                                                                </a>
                                                            </div>
                                                            {/if}
                                                        </div>
                                                        <!-- 发帖数量等 -->
                                                        <div class="mt-3 px-2 md:px-0 md:mt-5">
                                                            <ul class="text-sm justify-between md:text-2xl flex items-center gap-x-5 md:gap-x-10 text-center md:justify-start">
                                                                <li>
                                                                    <span>{$vo.user.question_count}</span>
                                                                    <p class="text-[#155797]">Questions</p>
                                                                </li>
                                                                <li>
                                                                    <span>{$vo.user.posting_count}</span>
                                                                    <p class="text-[#155797]">Posts</p>
                                                                </li>
                                                                <li>
                                                                    <span>{$vo.user.reply_count}</span>
                                                                    <p class="text-[#155797]">Reply</p>
                                                                </li>
                                                            </ul>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="badge-about">
                                                    <!-- 徽章-关于 tab按钮 -->
                                                    <div class="flex items-center gap-x-10 border-b border-[#dae9ff] px-10">
                                                        <div class="badge-about-btn active">
                                                            Badge
                                                        </div>
                                                        <div class="badge-about-btn">
                                                            About
                                                        </div>
                                                    </div>
                                                    <!-- tab详情 -->
                                                    <div class="tab-content p-5 md:p-10">
                                                        <div class="tab-content-item flex gap-x-10">
                                                            <!-- 没有徽章的时候显示 -->
                                                            <div class="tab-content-item-no-badge text-sm md:text-2xl" style="display: {$vo.user.role_id == 1 || $vo.user.role_id == 2 || $vo.user.role_id == 3 ? 'block' : 'none'};">
                                                                {$vo.user.first_name} {$vo.user.last_name} did not receive any badges yet.
                                                            </div>
                                                            <!-- 有徽章的时候 -->
                                                            <div class="tab-content-item-badge" style="display: {$vo.user.role_id == 4 || $vo.user.role_id == 5 || $vo.user.role_id == 6 ? 'block' : 'none'};">
                                                                <div class="flex items-center gap-x-2.5 md:gap-x-10 badge-item ">
                                                                    <div class=" flex flex-col gap-y-2 items-center">
                                                                        <div class="w-[40px] md:w-[75px] md:h-[81px] flex-shrink-0 bg-white">
                                                                            <img src="__IMG__/iCommunity/icon_1.png" alt="" class="w-full h-full object-cover">
                                                                        </div>

                                                                        <div class="text-sm md:text-xl text-[#999]">
                                                                            <p>Junior Badge</p>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="tab-content-item-badge" style="display: {$vo.user.role_id == 5 || $vo.user.role_id == 6 ? 'block' : 'none'};">
                                                                <div class="flex items-center gap-x-2.5 md:gap-x-10 badge-item">
                                                                    <div class=" flex flex-col gap-y-2 items-center">
                                                                        <div class="w-[40px] md:w-[75px] md:h-[81px] flex-shrink-0 bg-white">
                                                                            <img src="__IMG__/iCommunity/icon_2.png" alt=""
                                                                                class="w-full h-full object-cover">
                                                                        </div>

                                                                        <div class="text-sm md:text-xl text-[#999]">
                                                                            <p>Intermediate Badge</p>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="tab-content-item-badge" style="display: {$vo.user.role_id == 6 ? 'block' : 'none'};">
                                                                <div class="flex items-center gap-x-2.5 md:gap-x-10 badge-item">
                                                                    <div class=" flex flex-col gap-y-2 items-center">
                                                                        <div class="w-[40px] md:w-[75px] md:h-[81px] flex-shrink-0 bg-white">
                                                                            <img src="__IMG__/iCommunity/icon_3.png" alt="" class="w-full h-full object-cover">
                                                                        </div>

                                                                        <div class="text-sm md:text-xl text-[#999]">
                                                                            <p>Senior Badge</p>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>

                                                        <div class="tab-content-item flex flex-col gap-x-2.5 md:gap-x-10 gap-y-3 md:gap-y-5 text-sm md:text-xl" style="display: none;">
                                                            <div class="about-item flex flex-col md:flex-row md:items-center justify-between gap-y-2 md:gap-y-0 md:gap-x-10">
                                                                <div class="about-item-left flex-1/2">
                                                                    Registration Date
                                                                </div>
                                                                <div class="about-item-right Roboto_Bold flex-1/2">
                                                                    <p>{:date('j F Y', strtotime($vo.user.create_time))}</p>
                                                                </div>
                                                            </div>
                                                            <div class="about-item flex flex-col md:flex-row md:items-center justify-between gap-y-2 md:gap-y-0 md:gap-x-10">
                                                                <div class="about-item-left flex-1/2">
                                                                    Organization/Institution/Corporation
                                                                </div>
                                                                <div class="about-item-right Roboto_Bold flex-1/2">
                                                                    <p>{$vo.user.organization}</p>
                                                                </div>
                                                            </div>
                                                            <div class="about-item flex flex-col md:flex-row md:items-center justify-between gap-y-2 md:gap-y-0 md:gap-x-10">
                                                                <div class="about-item-left flex-1/2">
                                                                    Title
                                                                </div>
                                                                <div class="about-item-right Roboto_Bold flex-1/2">
                                                                    <p>{$vo.user.title}</p>
                                                                </div>
                                                            </div>
                                                            <div class="about-item flex flex-col md:flex-row md:items-center justify-between gap-y-2 md:gap-y-0 md:gap-x-10">
                                                                <div class="about-item-left flex-1/2">
                                                                    Location (City, Country, Earth)
                                                                </div>
                                                                <div class="about-item-right Roboto_Bold flex-1/2">
                                                                    <p>{$vo.user.country}</p>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="iCommunity-right md:pt-5 flex-1">
                                        <div class="iCommunity-right-info flex md:flex-row flex-col gap-x-2 mb-2 text-sm md:gap-x-3.5 md:text-xl md:mb-5">
                                            <div class="iCommunity-right-title-name">
                                                <a href="/iCommunity/user/{$vo.user.id}" class="text-[#155797]">{$vo.user.first_name} {$vo.user.last_name}</a>
                                            </div>
                                            <div class="iCommunity-right-title-time text-[#999]">
                                                {$vo.user.role_name} | Published in
                                                <a href="/iCommunity/topic/{$vo.topic}" class="underline text-[#999]">{$vo.topic}</a>
                                            </div>
                                        </div>
                                        <div class="iCommunity-right-content mb-2 md:mb-5">
                                            <div class="iCommunity-right-content-title mb-2">
                                                <a href="/iCommunity/news/{$vo.id}" class="text-base Roboto_Bold line-clamp-1 text-[#999] md:text-2xl">
                                                    {$vo.title}
                                                </a>
                                            </div>
                                            <div class="iCommunity-right-content-info text-sm line-clamp-3 md:text-xl">
                                                {$vo.content|strip_tags}
                                            </div>
                                        </div>
                                        <div class="iCommunity-right-time text-[#999] text-sm flex items-center justify-between md:justify-start md:gap-x-5 md:text-xl">
                                            <div class="iCommunity-right-time-left">
                                                {:date('F j, Y, g:i A', strtotime($vo.create_time))}
                                            </div>
                                            <div class="iCommunity-right-time-right">
                                                <a href="/iCommunity/news/{$vo.id}" class="text-[#999] flex items-center gap-x-1 md:gap-x-2">
                                                    <img src="__IMG__/icons/pinglun.png" alt="" class="w-[1rem] md:w-[1.5rem]">
                                                    <span>{$vo.reply_count}</span>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                                {/volist}
                            </ul>
                            <div class="text-sm p-3 md:py-[40px] md:px-[57px] md:text-xl">
                                <button type="button"
                                    class="show-more-activity-btn rounded-md bg-[#155797] text-white py-3.5 w-full cursor-pointer md:h-[80px]">
                                    SHOW MORE ACTIVITY
                                </button>
                            </div>
                        </div>

                        <div data-tab="discussion" class="iCommunity-content-item">
                            <ul class="grid grid-cols-1 mb-2" role="list">
                                {volist name="post" id="vo"}
                                <li>
                                    <div class="iCommunity-left w-[40px] h-[40px] rounded-md flex-shrink-0 relative md:w-[80px] md:h-[80px] border border-[#dae9ff]">
                                        <div class="w-full h-full cursor-pointer">
                                            <img src="{$vo.user.avatar ?? '__IMG__/user-1.jpg'}" alt="" class="w-full h-full rounded-md object-cover">
                                        </div>
                                        <div class="iCommunity-left-info absolute left-0 top-full z-10" style="display: none;">
                                            <div class="bg-[#fafbff] rounded-xl" style="box-shadow: 0 0 20px -2px rgba(84, 111, 138, 0.3);">
                                                <div class="flex items-start gap-x-2 mb-3 md:mb-0 md:gap-x-8 p-4 md:p-10">
                                                    <div class="w-[40px] h-[40px] md:w-[130px] md:h-[130px] flex-shrink-0">
                                                        <img src="{$vo.user.avatar ?? '__IMG__/user-1.jpg'}" alt="" class="w-full h-full rounded-md object-cover">
                                                    </div>
                                                    <!-- 右侧个人信息 -->
                                                    <div class="flex flex-col gap-y-2 md:flex-1">
                                                        <!-- 名称、经验、私信 -->
                                                        <div class="flex w-full gap-x-2.5 md:gap-x-8 items-end">
                                                            <div class="name-info">
                                                                <a href="/iCommunity/user/{$vo.user.id}" class="text-base md:text-3xl Roboto_Bold ">{$vo.user.first_name} {$vo.user.last_name}</a>
                                                                <p class="text-xs text-nowrap md:text-xl text-[#999]">{$vo.user.role_name}</p>
                                                            </div>
                                                            <!-- 私信 -->
                                                            {if $vo.user.id != session('userId')}
                                                            <div class="message-btn">
                                                                <a href="/iCommunity/message?user={$vo.user.first_name}_{$vo.user.last_name}" class="text-sm px-2 py-1 md:text-xl bg-[#155797] text-white text-nowrap rounded-md md:px-4 md:py-2 ">
                                                                    Private message
                                                                </a>
                                                            </div>
                                                            {/if}
                                                        </div>
                                                        <!-- 发帖数量等 -->
                                                        <div class="mt-3 px-2 md:px-0 md:mt-5">
                                                            <ul class="text-sm justify-between md:text-2xl flex items-center gap-x-5 md:gap-x-10 text-center md:justify-start">
                                                                <li>
                                                                    <span>{$vo.user.question_count}</span>
                                                                    <p class="text-[#155797]">Questions</p>
                                                                </li>
                                                                <li>
                                                                    <span>{$vo.user.posting_count}</span>
                                                                    <p class="text-[#155797]">Posts</p>
                                                                </li>
                                                                <li>
                                                                    <span>{$vo.user.reply_count}</span>
                                                                    <p class="text-[#155797]">Reply</p>
                                                                </li>
                                                            </ul>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="badge-about">
                                                    <!-- 徽章-关于 tab按钮 -->
                                                    <div class="flex items-center gap-x-10 border-b border-[#dae9ff] px-10">
                                                        <div class="badge-about-btn active">
                                                            Badge
                                                        </div>
                                                        <div class="badge-about-btn">
                                                            About
                                                        </div>
                                                    </div>
                                                    <!-- tab详情 -->
                                                    <div class="tab-content p-5 md:p-10">
                                                        <div class="tab-content-item flex gap-x-10">
                                                            <!-- 没有徽章的时候显示 -->
                                                            <div class="tab-content-item-no-badge text-sm md:text-2xl" style="display: {$vo.user.role_id == 1 || $vo.user.role_id == 2 || $vo.user.role_id == 3 ? 'block' : 'none'};">
                                                                {$vo.user.first_name} {$vo.user.last_name} did not receive any badges yet.
                                                            </div>
                                                            <!-- 有徽章的时候 -->
                                                            <div class="tab-content-item-badge" style="display: {$vo.user.role_id == 4 || $vo.user.role_id == 5 || $vo.user.role_id == 6 ? 'block' : 'none'};">
                                                                <div class="flex items-center gap-x-2.5 md:gap-x-10 badge-item ">
                                                                    <div class=" flex flex-col gap-y-2 items-center">
                                                                        <div class="w-[40px] md:w-[75px] md:h-[81px] flex-shrink-0 bg-white">
                                                                            <img src="__IMG__/iCommunity/icon_1.png" alt="" class="w-full h-full object-cover">
                                                                        </div>

                                                                        <div class="text-sm md:text-xl text-[#999]">
                                                                            <p>Junior Badge</p>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="tab-content-item-badge" style="display: {$vo.user.role_id == 5 || $vo.user.role_id == 6 ? 'block' : 'none'};">
                                                                <div class="flex items-center gap-x-2.5 md:gap-x-10 badge-item">
                                                                    <div class=" flex flex-col gap-y-2 items-center">
                                                                        <div class="w-[40px] md:w-[75px] md:h-[81px] flex-shrink-0 bg-white">
                                                                            <img src="__IMG__/iCommunity/icon_2.png" alt=""
                                                                                class="w-full h-full object-cover">
                                                                        </div>

                                                                        <div class="text-sm md:text-xl text-[#999]">
                                                                            <p>Intermediate Badge</p>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="tab-content-item-badge" style="display: {$vo.user.role_id == 6 ? 'block' : 'none'};">
                                                                <div class="flex items-center gap-x-2.5 md:gap-x-10 badge-item">
                                                                    <div class=" flex flex-col gap-y-2 items-center">
                                                                        <div class="w-[40px] md:w-[75px] md:h-[81px] flex-shrink-0 bg-white">
                                                                            <img src="__IMG__/iCommunity/icon_3.png" alt="" class="w-full h-full object-cover">
                                                                        </div>

                                                                        <div class="text-sm md:text-xl text-[#999]">
                                                                            <p>Senior Badge</p>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>

                                                        <div class="tab-content-item flex flex-col gap-x-2.5 md:gap-x-10 gap-y-3 md:gap-y-5 text-sm md:text-xl" style="display: none;">
                                                            <div class="about-item flex flex-col md:flex-row md:items-center justify-between gap-y-2 md:gap-y-0 md:gap-x-10">
                                                                <div class="about-item-left flex-1/2">
                                                                    Registration Date
                                                                </div>
                                                                <div class="about-item-right Roboto_Bold flex-1/2">
                                                                    <p>{:date('j F Y', strtotime($vo.user.create_time))}</p>
                                                                </div>
                                                            </div>
                                                            <div class="about-item flex flex-col md:flex-row md:items-center justify-between gap-y-2 md:gap-y-0 md:gap-x-10">
                                                                <div class="about-item-left flex-1/2">
                                                                    Organization/Institution/Corporation
                                                                </div>
                                                                <div class="about-item-right Roboto_Bold flex-1/2">
                                                                    <p>{$vo.user.organization}</p>
                                                                </div>
                                                            </div>
                                                            <div class="about-item flex flex-col md:flex-row md:items-center justify-between gap-y-2 md:gap-y-0 md:gap-x-10">
                                                                <div class="about-item-left flex-1/2">
                                                                    Title
                                                                </div>
                                                                <div class="about-item-right Roboto_Bold flex-1/2">
                                                                    <p>{$vo.user.title}</p>
                                                                </div>
                                                            </div>
                                                            <div class="about-item flex flex-col md:flex-row md:items-center justify-between gap-y-2 md:gap-y-0 md:gap-x-10">
                                                                <div class="about-item-left flex-1/2">
                                                                    Location (City, Country, Earth)
                                                                </div>
                                                                <div class="about-item-right Roboto_Bold flex-1/2">
                                                                    <p>{$vo.user.country}</p>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="iCommunity-right md:pt-5 flex-1">
                                        <div class="iCommunity-right-info flex md:flex-row flex-col gap-x-2 mb-2 text-sm md:gap-x-3.5 md:text-xl md:mb-5">
                                            <div class="iCommunity-right-title-name">
                                                <a href="/iCommunity/user/{$vo.user.id}" class="text-[#155797]">{$vo.user.first_name} {$vo.user.last_name}</a>
                                            </div>
                                            <div class="iCommunity-right-title-time text-[#999]">
                                                {$vo.user.role_name} | Published in
                                                <a href="/iCommunity/topic/{$vo.topic}" class="underline text-[#999]">{$vo.topic}</a>
                                            </div>
                                        </div>
                                        <div class="iCommunity-right-content mb-2 md:mb-5">
                                            <div class="iCommunity-right-content-title mb-2 flex items-center gap-x-2">
                                                <a href="/iCommunity/post/{$vo.id}" class="text-base Roboto_Bold line-clamp-1 text-[#999] md:text-2xl">
                                                    {$vo.title}
                                                </a>
                                                {if $vo.post_type==0}
                                                <span class="inline-block flex-shrink-0">
                                                    <img src="__IMG__/icons/w.png" alt="" class="w-4 h-4">
                                                </span>
                                                {/if}
                                            </div>

                                            <div class="iCommunity-right-content-info text-sm line-clamp-3 md:text-xl">
                                                {$vo.content|strip_tags}
                                            </div>
                                        </div>
                                        <div class="iCommunity-right-time text-[#999] text-sm flex items-center justify-between md:justify-start md:gap-x-5 md:text-xl">
                                            <div class="iCommunity-right-time-left">
                                                {:date('F j, Y, g:i A', strtotime($vo.create_time))}
                                            </div>
                                            <div class="iCommunity-right-time-right">
                                                <a href="/iCommunity/post/{$vo.id}" class="text-[#999] flex items-center gap-x-1 md:gap-x-2">
                                                    <img src="__IMG__/icons/pinglun.png" alt="" class="w-[1rem] md:w-[1.5rem]">
                                                    <span>{$vo.reply_count}</span>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                                {/volist}
                            </ul>
                            <div class="text-sm p-3 md:py-[40px] md:px-[57px] md:text-xl">
                                <button type="button"
                                    class="show-more-activity-btn rounded-md bg-[#155797] text-white py-3.5 w-full cursor-pointer md:h-[80px]">
                                    SHOW MORE ACTIVITY
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="iCommunity-membership w-11/12 md:w-10/12 mx-auto" data-aos="fade-up">
                <div class="iCommunity-membership-title text-xl font-bold mb-3 md:text-3xl md:mb-10">
                    What are the benefits of icommunity membership?
                </div>
                <div class="iCommunity-membership-content grid grid-cols-1 gap-y-3 md:grid-cols-3 md:gap-x-5">
                    <div class="iCommunity-membership-content-item min-h-[176px] md:min-h-[350px]">
                        <div
                            class="w-10 h-10 rounded-full overflow-hidden border border-[#c4d7ff] bg-[#f8fdff] flex-shrink-0 flex items-center justify-center md:w-14 md:h-14">
                            <img src="__IMG__/icons/wenxian-2.png" alt="" class="w-[1rem] object-cover md:w-[1.5rem]" />
                        </div>
                        <div class="iCommunity-membership-content-item-title text-base Roboto_Bold md:text-3xl">
                            Posts
                        </div>
                        <div class="iCommunity-membership-content-item-content text-sm leading-5 text-[#999] md:text-xl md:leading-8">
                            Interact with thousands of other DEL users from around the globe.
                        </div>
                    </div>
                    <div class="iCommunity-membership-content-item min-h-[176px] md:min-h-[350px]">
                        <div
                            class="w-10 h-10 rounded-full overflow-hidden border border-[#c4d7ff] bg-[#f8fdff] flex-shrink-0 flex items-center justify-center md:w-14 md:h-14">
                            <img src="__IMG__/icons/mima-2.png" alt="" class="w-[1rem] object-cover md:w-[1.5rem]" />
                        </div>
                        <div class="iCommunity-membership-content-item-title text-base Roboto_Bold md:text-3xl">
                            Private Channels
                        </div>
                        <div class="iCommunity-membership-content-item-content text-sm leading-5 text-[#999] md:text-xl md:leading-8">
                            Private channels are available to discuss specific topics and technology applications.
                        </div>
                    </div>
                    <div class="iCommunity-membership-content-item min-h-[176px] md:min-h-[350px]">
                        <div
                            class="w-10 h-10 rounded-full overflow-hidden border border-[#c4d7ff] bg-[#f8fdff] flex-shrink-0 flex items-center justify-center md:w-14 md:h-14">
                            <img src="__IMG__/icons/refresh.png" alt="" class="w-[1rem] object-cover md:w-[1.5rem]" />

                        </div>
                        <div class="iCommunity-membership-content-item-title text-base Roboto_Bold md:text-3xl">
                            Product and technology updates
                        </div>
                        <div class="iCommunity-membership-content-item-content text-sm leading-5 text-[#999] md:text-xl md:leading-8">
                            Stay up to date with the latest HitGen’s services and products.
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    {include file="public:footer"}

    {include file="public:foot"}

    <script>
        initTabSwitch('.iCommunity-btn', '.iCommunity-content-item');

        $('.iCommunity-left').hover(function(){
            $(this).find('.iCommunity-left-info').show();
        },function(){
            $(this).find('.iCommunity-left-info').hide();
        });

        $('.badge-about').each(function(){
            var $badgeAbout = $(this);
            $badgeAbout.find('.badge-about-btn').each(function(index){
                $(this).click(function(){
                    // 当前tab高亮，兄弟去除高亮
                    $(this).addClass('active').siblings().removeClass('active');
                    // 只切换当前区块下的tab内容
                    $badgeAbout.find('.tab-content-item').eq(index).show().siblings('.tab-content-item').hide();
                });
            });
        });

        //显示更多
        setupShowMoreActivity(
            '.iCommunity-content-item',   // 外层容器
            'ul[role="list"]',            // ul选择器
            '.show-more-activity-btn',                     // 按钮选择器
            4                             // 默认显示4个
        );
    </script>

</body>

</html>