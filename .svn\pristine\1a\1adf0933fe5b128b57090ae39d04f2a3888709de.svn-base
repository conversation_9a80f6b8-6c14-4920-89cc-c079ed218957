<?php

declare(strict_types=1);

namespace app\admin\controller;

use app\admin\controller\Common;

use think\facade\Db;

class Basic extends Common
{
    public function index()
    {
        if ($this->request->isPost()) {
            $data = input('post.');
            $id = $data['id'];

            $s = Db::name("Basic")->where("id", $id)->save($data);
            if ($s) {
                $this->success('修改成功');
            } else {
                $this->error("修改失败，请重试！");
            }
        } else {
            $id = 1;
            $getone = Db::name("Basic")->where("id", $id)->find();

            return view("", [
                "getone" => $getone,
            ]);
        }
    }

    public function test()
    {
        if ($this->request->isPost()) {
            $data = input('post.');
            $id = $data['id'];

            // 关闭HTML过滤
            $data['content_test1'] = input('post.content_test1', '', null);

            $s = Db::name("Basic")->where("id", $id)->save($data);
            if ($s) {
                $this->success('修改成功');
            } else {
                $this->error("修改失败，请重试！");
            }
        } else {
            $id = 1;
            $getone = Db::name("Basic")->where("id", $id)->find();

            return view("", [
                "getone" => $getone,
            ]);
        }
    }

    //mention实时获取用户列表
    public function searchUsers()
    {
        $q = input('q');
        $users = Db::name('User')
            ->where('first_name|last_name', 'like', "%{$q}%")
            ->field('id, CONCAT(first_name, " ", last_name) as name')
            ->limit(15)
            ->select();
        return json($users);
    }

}
