<?php

declare(strict_types=1);

namespace app\admin\controller;

use app\admin\controller\Common;
use think\facade\Db;

class Download extends Common
{
    public function index()
    {
        $List = Db::name('Download')
            ->order("id desc")
            ->paginate([
                'query'     =>  request()->param(), //url额外参数
                'list_rows' => 20, //每页数量
            ])
            ->each(function ($item) {
                $item['user'] = Db::name("User")->field("id, email, phone")->where("id", $item['user_id'])->find();

                return $item;
            });

        return view("", [
            "List" => $List
        ]);
    }

}
