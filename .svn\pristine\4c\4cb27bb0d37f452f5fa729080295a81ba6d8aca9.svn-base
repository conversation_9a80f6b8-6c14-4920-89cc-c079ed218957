<div id="header" class="relative z-[9999]">
    <header class="w-11/12 mx-auto md:w-10/12">
        <nav class="nav-wrapper flex justify-between items-center h-[3.125rem] md:h-24 top-0" role="navigation">
            <div class="flex-1 md:flex md:items-center md:gap-5 md:h-full">
                <a href="/">
                    <img src="__IMG__/logo.png" alt="HitGen OpenDEL™" class="w-24 md:w-[8.4375rem]" />
                </a>
                <ul class="fixed hidden items-center h-[calc(100%-3.125rem)] bottom-0 overflow-auto left-0 z-[99999] w-full bg-white text-base
                md:flex md:gap-x-[3.75rem] md:relative md:bg-transparent md:overflow-visible md:h-full md:flex-1 md:text-xl animate__animated animate__delay"
                    id="nav_list">
                    <li class="md:h-full flex">
                        <a href="/" class="text-[#000]  flex-1 leading-[3.125rem] px-5 border-b border-[#e0eaff]
                        md:border-0 md:leading-none md:flex md:items-center
                        ">Home</a>
                    </li>
                    <li class="relative cursor-pointer md:h-full flex flex-col">
                        <a href="/news/" class="text-[#000] cursor-pointer flex-1 h-full leading-[3.125rem] px-5 flex justify-between items-center border-b border-[#e0eaff] md:border-0">
                            <span>DELHunter</span>
                        </a>
                    </li>
                    <li class="relative flex justify-between items-center border-b border-[#e0eaff] cursor-pointer navigation flex-wrap group
                        md:border-0 md:h-full md:flex-nowrap">
                        <a href="/product/"
                            class="text-[#000] cursor-pointer leading-[3.125rem] px-5 md:px-0 md:leading-none">
                            Services
                        </a>
                        <i class="w-[1rem] h-[.5625rem] inline-block bg-[url(__IMG__/icons/dow.png)] bg-no-repeat bg-center ml-2 group-hover:rotate-180 transition-all mr-5
                        md:mr-0
                        "></i>
                        <ul class="
                            secondary static z-50 hidden bg-[#f8fdff]
                            w-full md:absolute md:top-24 md:left-1/2  md:-translate-x-1/2 md:bg-white md:w-[19.5rem] ">
                            {volist name="menu_product" id="vo"}
                            <li class="{$vo.service?'flex items-center justify-between flex-wrap border-b border-t border-[#e0eaff] relative group navigation-item':'grid grid-cols-1'} md:pr-[1.25rem] md:px-0">
                                <a href="/product/{$vo.seo_url}"
                                    class="text-[#000] flex items-center  md:py-5 px-8 h-[3.125rem] md:h-auto">
                                    {$vo.name}
                                </a>
                                <i class="w-[1rem] h-[.5625rem] inline-block bg-[url(__IMG__/icons/dow.png)] bg-no-repeat rotate-0 md:-rotate-90 bg-center ml-2 transition-all mr-5
                                md:mr-0 "></i>
                                <ul class="three-menu w-full hidden bg-[#f8fdff] md:absolute md:top-0 Roboto_Regular md:left-full md:w-full md:bg-[#fff] md:shadow-lg ">
                                    {volist name="vo.service" id="v"}
                                    <li class="border-b border-t border-[#e0eaff]">
                                        <a href="/service/{$v.seo_url}" class="
                                        flex items-center px-8 h-[3.125rem] md:h-auto
                                        md:px-8 md:py-5 line-clamp-1 ">
                                            {$v.name}
                                        </a>
                                    </li>
                                    {/volist}
                                </ul>
                            </li>
                            {/volist}
                        </ul>
                    </li>

                    <li class="relative flex justify-between items-center border-b border-[#e0eaff] cursor-pointer navigation flex-wrap group
                        md:border-0 md:h-full md:flex-nowrap">
                        <a href="" class="text-[#000] cursor-pointer leading-[3.125rem] px-5 md:px-0 md:leading-none">
                            Resources
                        </a>
                        <i class="w-[1rem] h-[.5625rem] inline-block bg-[url(__IMG__/icons/dow.png)] bg-no-repeat bg-center ml-2 group-hover:rotate-180 transition-all mr-5
                        md:mr-0
                        "></i>
                        <ul class="
                        secondary static bg-[#f8fdff] z-50
                        hidden
                        w-full
                        md:absolute
                        md:top-24
                        md:left-1/2
                        md:-translate-x-1/2
                            md:bg-white
                            md:w-[19.5rem] ">
                            {volist name="menu_resources_category" id="vo" key="k"}
                            <li class="grid grid-cols-1">
                                <a href="/resources/?tab={$k}" class="text-[#000] flex items-center md:py-5 px-8 h-[3.125rem] border-b border-[#e0eaff] md:h-auto">
                                    {$vo.name}
                                </a>
                            </li>
                            {/volist}

                            <li class="grid grid-cols-1">
                                <a href="/faq/" class="text-[#000] flex items-center md:py-5 px-8 h-[3.125rem] border-b border-[#e0eaff] md:h-auto">
                                    FAQ
                                </a>
                            </li>
                        </ul>
                    </li>
                    <li class="md:h-full grid grid-cols-1">
                        <a href="/iCommunity/" class="text-[#000] border-b border-[#e0eaff] leading-[3.125rem] px-5 md:border-0
                        md:leading-none
                        md:flex
                        md:items-center">iCommunity</a>
                    </li>
                </ul>
            </div>

            <div class="flex items-center gap-3.5 md:gap-8">
                <div class="md:flex md:items-center relative">
                    <div class="bg-white rounded-full p-1 h-8 w-8 border border-[#dfe7ff] md:w-12 md:h-12 "
                        id="search_btn">
                        <button type="button"
                            class="search-btn w-full h-full bg-[url(__IMG__/icons/sousuo.png)] bg-size-[1rem] rounded-full bg-no-repeat bg-center md:bg-auto cursor-pointer"
                            aria-label="搜索"></button>
                    </div>
                    <form action="" method="post"
                        class="hidden absolute w-full left-0 top-full md:relative z-10" id="search_form">
                        <div
                            class="bg-[#f8fdff] h-14 md:bg-white md:rounded-full md:ml-2 md:w-[300px] md:h-12 relative flex items-center px-2.5 md:p-0">
                            <input type="text"
                                class="border flex-1 rounded-md h-10 px-5 border-[#e0eaff] bg-white md:rounded-full md:ml-2 md:w-full md:h-12 md:px-5"
                                placeholder="Enter the keyword" />
                            <button type="submit" tabindex="-1"
                                class="w-8 h-8 md:absolute md:right-[5px] md:top-1/2 md:-translate-y-1/2 md:w-[2.375rem] md:h-[2.375rem] bg-[url(__IMG__/icons/sousuo.png)] rounded-full bg-no-repeat bg-center bg-size-[1rem] md:bg-auto z-10 cursor-pointer"
                                id="submit_btn"></button>
                        </div>
                    </form>
                </div>

                {if session('userId')}
                <div class="bg-white rounded-full p-1 h-8 w-8 border border-[#dfe7ff] relative cursor-pointer md:w-12 md:h-12 flex justify-center items-center"
                 id="message_btn" onmouseenter="showMessageDrop()" onmouseleave="hideMessageDrop()"
                    onclick="toggleMessageDrop()">
                    <div class="btn-message">
                        <div class="w-full h-full flex items-center justify-center">
                            <img src="__IMG__/icons/lingdang.png" alt="" class="w-[1rem] md:w-auto">
                        </div>
                        {if count($user_message)>0}
                        <div class="absolute -top-1 -right-[.525rem] text-white text-center" id="message_num">
                            <div class="min-w-[1rem] min-h-[1rem] px-1 text-[.75rem] bg-[#ff0000] rounded-full md:min-w-[1.25rem] md:h-[1.25rem] md:leading-[1.25rem]">
                                <span>{$user_message|count}</span>
                            </div>
                        </div>
                        {/if}
                    </div>

                    <!-- Message下拉通知 -->
                    <div class="absolute top-full left-[10%] -translate-x-1/2 z-20 min-w-[15.625rem] cursor-default pt-3
                        md:min-w-[25rem] md:-left-[100%]
                        " id="message_drop" onclick="event.stopPropagation()" style="display: none;">
                        {if count($user_message)>0}
                        <div class="bg-white shadow-2xl rounded-xl">
                            <div class="message-header flex items-center justify-between p-3 border-b border-[#e0eaff] text-base md:text-xl md:py-[1.25rem] md:px-[1.875rem]">
                                <h3 class="font-bold">Tooltip</h3>
                                <button type="button"
                                    class="w-[1rem] h-[1rem] bg-[url(__IMG__/icons/shanchu.png)] bg-no-repeat bg-size-[.95rem] cursor-pointer md:w-[1.25rem] md:h-[1.375rem] md:bg-auto"
                                    id="EmptyMessage"></button>
                            </div>
                            <div class="message-tips-list" id="message_cont">
                                <ul>
                                    {volist name="user_message" id="vo"}
                                    <li>
                                        <strong>[System]</strong>
                                        <span>{$vo.content}</span>
                                    </li>
                                    {/volist}
                                </ul>
                            </div>
                            <div class="flex items-center justify-center py-4 gap-2.5" id="message_more">
                                <a href="/user/message">
                                    <strong class="text-sm md:text-xl"> View All </strong>
                                </a>
                                <img src="__IMG__/icons/changjiantou-zuoshang2.png" alt="" class="w-[1rem]">
                            </div>
                            <!-- 清空/暂无消息时候显示 -->
                            <div class="text-center py-4 text-[#f08411] no-message hidden" id="no_message">
                                <span class="text-sm md:text-xl">No message</span>
                            </div>
                        </div>
                        {/if}
                    </div>
                </div>

                <div class="w-8 h-8 rounded-full md:w-[3.125rem] md:h-[3.125rem] relative cursor-pointer btn-name">
                    <div class="user-name">
                        <img src="{$user.avatar ?? '__IMG__/user-1.jpg'}" class="w-full h-full rounded-full object-cover" alt="{:session('userEmail')}" />
                    </div>
                    <!-- 登录以后的下拉 -->
                    <div class="user-name-drop absolute top-full -left-[50%] -translate-x-1/2 min-w-[12.5rem] pt-3 z-20 md:right-0 md:min-w-[15.625rem] md:-left-full cursor-default" onclick="event.stopPropagation()">
                        <div class="bg-white p-[1.25rem] shadow-2xl rounded-xl  md:p-6 ">
                            <div class="text-center mb-3">
                                <img src="{$user.avatar ?? '__IMG__/user-1.jpg'}" alt="" class="w-[4rem] h-[4rem] mx-auto rounded-full" />
                                <p class="mt-[.625rem] text-lg">
                                    <strong class="line-clamp-1">{$user.first_name} {$user.last_name}</strong>
                                </p>
                            </div>
                            <div class="text-sm md:text-xl">
                                <div class="border-b py-2  px-2 border-[#e0eaff]">
                                    <a href="/user/">
                                        Personal Center
                                    </a>
                                </div>
                                <div class="py-2 px-2">
                                    <a href="/logout">
                                        Sign Out
                                    </a>
                                </div>
                            </div>
                        </div>
                {else}
                <div class="flex items-center h-8 min-w-20">
                    <button aria-label="登录账户"
                        class="bg-[#f08411] h-full w-full text-white cursor-pointer bg-[url(__IMG__/icons/yonghu.png)] bg-no-repeat bg-size-[13px] bg-[12px_center] rounded-lg text-sm pl-6 pr-2 md:w-32 md:h-12 md:bg-auto md:text-xl">
                        <a href="/login/">Login</a>
                    </button>
                </div>
                {/if}

                <div class="menu min-w-8 h-5 ml-2 md:hidden">
                    <button aria-label="打开菜单"
                        class="h-full w-full bg-[url(__IMG__/icons/menu.png)] bg-no-repeat bg-center bg-size-[26px] cursor-pointer"
                        id="menu_btn"></button>
                </div>
            </div>
        </nav>
    </header>
</div>