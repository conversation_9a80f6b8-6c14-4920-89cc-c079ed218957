<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <title>iCommunity-User - HitGen OpenDEL™</title>
    <link rel="shortcut icon" href="/favicon.ico" />
    <link href="__CSS__/vendors/aos.css" rel="stylesheet">
    <link href="__CSS__/style.css" rel="stylesheet" />
</head>

<body>
    <section class="bg-[url(__IMG__/backgrounds/m_bj.jpg)] bg-no-repeat pb-5 bg-size-[100%] md:bg-[url(__IMG__/backgrounds/pc_bj.jpg)] md:bg-size-[100%] md:pb-8">

        {include file="public:header"}

        <div class="w-11/12 mx-auto md:w-10/12">
            <div class="mt-5 Navigation mb-3.5 md:mb-14">
                <ul class="text-[#999] flex items-center text-md md:text-xl">
                    <li>
                        <a href="/" class="text-[#155797]">
                            Home
                        </a>
                    </li>
                    <li class="max-w-[12.5rem] line-clamp-1 md:max-w-2xs">
                        Start here
                    </li>
                </ul>
            </div>
            <!-- data-aos="fade-up" -->
            <div class="contact md:mb-10">
                <!-- user-name -->
                <div class="bg-white border border-[#dae9ff] rounded-xl mb-4" data-aos="fade-up">
                    <!-- 头像左，名称经验右侧 -->
                    <div class="flex items-center gap-x-4 p-4 border-b border-[#dae9ff] md:px-[3.75rem] md:py-[3.125rem] md:gap-x-[2.5rem]">
                        <div class="flex-shrink-0 w-[4.375rem] h-[4.375rem] rounded-full overflow-hidden md:w-[8.75rem] md:h-[8.75rem]">
                            <img src="{$user.avatar ?? '__IMG__/user-1.jpg'}" class="w-full h-full object-cover" alt="" />
                        </div>
                        <div class="flex flex-col gap-y-2 text-sm flex-1 md:gap-y-4">
                            <div class="flex items-center flex-wrap gap-x-5">
                                <h1 class="text-lg font-bold md:text-3xl">{$user.first_name} {$user.last_name}</h1>
                                {if $user.id != session('userId')}
                                <a href="/iCommunity/message?user={$user.first_name}_{$user.last_name}" class="text-sm px-2 py-1 md:text-xl bg-[#155797] text-white text-nowrap rounded-md md:px-4 md:py-2 ">Private message</a>
                                {/if}
                            </div>
                            <div class="flex items-center gap-x-5 md:gap-x-5">
                                <div class="w-[100%] h-[.625rem] bg-[#dae9ff] rounded-md flex-1 md:flex-initial md:h-[.9375rem]  md:w-[12.5rem]">
                                    <div class="h-full bg-[#f08411] rounded-md" style="width: {$role.id <= 4 ? '0%' : ($role.id == 5 ? '50%' : '100%')};"></div>
                                </div>
                                <span class="flex-1 text-[#999] md:text-2xl md:flex-2">{$role.name}</span>
                            </div>
                        </div>
                    </div>

                    <!-- 展示话题 -->
                    <div class="p-4 border-b border-[#dae9ff] md:px-[3.75rem] md:py-[2.5rem]">
                        <div class="flex items-center gap-x-5 text-sm text-[#155797] md:text-2xl md:gap-x-10">
                            <span>Questions {$question_count}</span>
                            <span>posts {$posts_count}</span>
                            <span>Reply {$reply_count}</span>
                        </div>
                    </div>

                    <!-- 徽章列表 -->
                    <div class="p-4 md:px-[3.75rem] md:py-[2.5rem]">
                        <h2 class="text-lg font-bold mb-4 md:text-2xl md:mb-10">Badges</h2>

                        <!-- 徽章为空的时候 -->
                        <div class="text-center bg-[#fafbff] text-sm text-[#111] p-4 md:text-2xl flex-col gap-y-4 items-center justify-center py-10" style="display: {$user.role_id == 4 || $user.role_id == 5 || $user.role_id == 6 ? 'none' : 'flex'};">
                            <img src="__IMG__/ZW-1.png" alt="" class="w-24 md:w-52">
                            {$user.first_name} {$user.last_name} did not receive any badges yet.
                        </div>
                        <!-- 徽章不为空的时候 -->
                        <div class="grid grid-cols-3 gap-4 md:flex md:flex-wrap md:gap-x-10 md:gap-y-5">
                            <div class="flex flex-col items-center gap-y-2.5 md:gap-y-5 badge-item" style="display: {$user.role_id == 4 || $user.role_id == 5 || $user.role_id == 6 ? 'inherit' : 'none'};">
                                <div class="w-[2.5rem] md:w-auto flex-shrink-0">
                                    <img src="__IMG__/iCommunity/icon_1.png" alt="" class="w-full h-full object-cover">
                                </div>

                                <div class="text-sm md:text-xl text-[#111]">
                                    <p>Junior Badge</p>
                                </div>
                            </div>
                            <div class="flex flex-col items-center gap-y-2.5 md:gap-y-5 badge-item" style="display: {$user.role_id == 5 || $user.role_id == 6 ? 'inherit' : 'none'};">
                                <div class="w-[2.5rem] md:w-[4.6875rem] flex-shrink-0">
                                    <img src="__IMG__/iCommunity/icon_2.png" alt="" class="w-full h-full object-cover">
                                </div>

                                <div class="text-sm md:text-xl text-[#111]">
                                    <p>Intermediate Badge</p>
                                </div>
                            </div>
                            <div class="flex flex-col items-center gap-y-2.5 md:gap-y-5 badge-item" style="display: {$user.role_id == 6 ? 'inherit' : 'none'};">
                                <div class="w-[2.5rem] md:w-[4.6875rem] flex-shrink-0">
                                    <img src="__IMG__/iCommunity/icon_3.png" alt="" class="w-full h-full object-cover">
                                </div>

                                <div class="text-sm md:text-xl text-[#111]">
                                    <p>Senior Badge</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 关于我 -->
                    <div class="p-4 md:px-[3.75rem] md:py-[2.5rem]">
                        <h2 class="text-lg font-bold mb-4 md:mb-10 md:text-2xl">About</h2>
                        <div class="border-t border-[#dae9ff] pt-4 text-sm flex flex-col gap-y-3 md:gap-y-5 md:text-xl md:pt-10">
                            <div class="flex flex-col md:flex-row md:items-center justify-between gap-y-2 md:gap-y-0 md:gap-x-10">
                                <div class="about-item-left flex-1/2">
                                    Registration Date
                                </div>
                                <div class="about-item-right Roboto_Bold flex-1/2">
                                    <p>{:date('j F Y', strtotime($user.create_time))}</p>
                                </div>
                            </div>
                            <div class="flex flex-col md:flex-row md:items-center justify-between gap-y-2 md:gap-y-0 md:gap-x-10">
                                <div class="about-item-left flex-1/2">
                                    Organization/Institution/Corporation
                                </div>
                                <div class="about-item-right Roboto_Bold flex-1/2">
                                    <p>{$user.organization}</p>
                                </div>
                            </div>
                            <div class="flex flex-col md:flex-row md:items-center justify-between gap-y-2 md:gap-y-0 md:gap-x-10">
                                <div class="about-item-left flex-1/2">
                                    Title
                                </div>
                                <div class="about-item-right Roboto_Bold flex-1/2">
                                    <p>{$user.title}</p>
                                </div>
                            </div>
                            <div
                                class="flex flex-col md:flex-row md:items-center justify-between gap-y-2 md:gap-y-0 md:gap-x-10">
                                <div class="about-item-left flex-1/2">
                                    Location (City, Country, Earth)
                                </div>
                                <div class="about-item-right Roboto_Bold flex-1/2">
                                    <p>{$user.country}</p>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>

                <!-- posts -->
                <div class="bg-white border border-[#dae9ff] rounded-xl" data-aos="fade-up" data-aos-delay="100">
                    <h2 class="text-lg font-bold mb-4 p-4 border-b border-[#dae9ff] md:text-2xl md:py-[2.5rem] md:px-[3.75rem]">Questions/Posts</h2>
                    <div class="list-posts">
                        <ul role="list">
                            {volist name="posts" id="vo"}
                            <li>
                                <div class="item-posts">
                                    <div class="item-posts-left flex-shrink-0 w-[2.5rem] h-[2.5rem] rounded-md overflow-hidden md:w-[5rem] md:h-[5rem] border border-[#dae9ff]">
                                        <img src="{$user.avatar ?? '__IMG__/user-1.jpg'}" class="w-full h-full object-cover" alt="" />
                                    </div>

                                    <div class="item-posts-right text-sm md:text-xl">
                                        <div class="item-posts-right-info text-[#999]">
                                            <span>{:date('F j, Y, g:i A', strtotime($vo.create_time))}</span> |
                                            <span>Published in
                                                <a href="/iCommunity/topic/{$vo.topic}" class="underline">{$vo.topic}</a>
                                            </span>
                                        </div>
                                        <div class="item-posts-right-title">
                                            <h3 class="text-base md:text-2xl">
                                                <a href="/iCommunity/post/{$vo.id}" class="text-[#666]">{$vo.title}</a>
                                                {if $vo.post_type==0}
                                                <span class="inline-block flex-shrink-0">
                                                    <img src="__IMG__/icons/w.png" alt="" class="w-4 h-4">
                                                </span>
                                                {/if}
                                            </h3>
                                        </div>
                                    </div>
                                </div>
                            </li>
                            {/volist}
                        </ul>
                        <div class="show-more-activity-btn text-sm p-3 md:py-[2.5rem] md:px-[3.5625rem] md:text-xl">
                            <button type="button" class=" rounded-md bg-[#155797] text-white py-3.5 w-full cursor-pointer md:h-[5rem]">
                                SHOW MORE ACTIVITY
                            </button>
                        </div>
                    </div>
                    <!-- 没有数据的时候显示 -->
                     <div class="text-center bg-[#fafbff] text-sm text-[#111] p-4 md:text-2xl items-center justify-center py-10" style="display: {$posts_count == 0 ? 'flex' : 'none'};">
                        <img src="__IMG__/ZW-1.png" alt="" class="w-24 md:w-52">
                    </div>
                </div>
            </div>
        </div>
    </section>

    {include file="public:footer"}

    {include file="public:foot"}

    <script>
        //显示更多
        setupShowMoreActivity(
            '.list-posts',   // 外层容器
            'ul[role="list"]',            // ul选择器
            '.show-more-activity-btn',                     // 按钮选择器
            4                             // 默认显示4个
        );
    </script>

</body>

</html>