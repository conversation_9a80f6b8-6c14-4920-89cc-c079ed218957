<?php

declare(strict_types=1);

namespace app\admin\controller;

use app\admin\controller\Common;
use think\facade\Db;
use think\facade\Filesystem;

class Order extends Common
{
    protected $_order_status = ["进行中", "已完成", "已取消"];

    public function index()
    {
        $params = request()->param();
        $where[] = ["o.parent_id", "=", 0];

        $params['order_status'] = isset($params['order_status'])?$params['order_status']:0;
        $where[] = ["o.order_status", "=", $params['order_status']];

        if (!empty($params['keyword'])) {
            $where[] = ["o.order_no|oi.email", "like", "%" . $params['keyword'] . "%"];
        }

        $List = Db::name('Order')->alias("o")
            ->field("o.*, oi.email, oi.organization")
            ->where($where)
            ->order("o.id desc")
            ->leftjoin("Order_info oi", "o.info_id=oi.id")
            ->paginate([
                'query'     =>  request()->param(), //url额外参数
                'list_rows' => 20, //每页数量
            ])
            ->each(function ($item){
                //产品名称
                $item['product_name'] = $item['product_id']?Db::name("Product")->where("id", $item['product_id'])->value("name"):"";

                //服务名称
                $item['service_name'] = $item['service_id']?Db::name("Service")->where("id", $item['service_id'])->value("name"):"";

                //后续服务
                $where = [
                    "parent_id" => $item['id'],
                ];
                $item['services'] = Db::name('Order')
                    ->where($where)
                    ->order("id desc")
                    ->select()
                    ->each(function ($i) {
                        $i['service_name'] = Db::name("Service")->where("id", $i['service_id'])->value('name');

                        return $i;
                    });

                $item['order_status'] = $this->_order_status[$item['order_status']];

                return $item;
            });

        return view("", [
            "List" => $List,
            "params" => $params,
        ]);
    }

    public function add()
    {
        if ($this->request->isPost()) {
            $data = input('post.');

            if(empty($data['email']) || empty($data['first_name']) || empty($data['last_name']) || empty($data['product_id'])) {
                $this->error("必填项未填！");
            }

            if(!empty($data['tracking_no']) && empty($data['tracking_company'])){
                $this->error("请填写物流公司！");
            }

            //如果邮箱存在，则自动归入邮箱所在用户；如果邮箱不存在，则自动创建一个用户账号，密码初始化opd123456。
            $where = [
                "email" => $data['email'],
            ];
            $user = Db::name("User")->where($where)->find();
            if(empty($user)){
                //自动创建用户账号
                $userData = [
                    "email" => $data['email'],
                    "first_name" => $data['first_name'],
                    "last_name" => $data['last_name'],
                    "country" => $data['country'],
                    "organization" => $data['organization'],
                    "title" => $data['title'],
                    "phone" => $data['phone'],
                    "express_address" => $data['express_address'],
                    "express_postcode" => $data['express_postcode'],
                    "role_id" => 4,  //默认初级用户
                    "create_time" => date("Y-m-d H:i:s"),
                ];

                // 生成salt
                $userData['salt'] = generateSalt();
                // 明码进行加盐hash解决
                $userData['password'] = generateHashPassword("opd123456", $data['salt']);

                $data['user_id'] = Db::name("User")->strict(false)->insertGetId($userData);
            } else {
                //自动归入user_id
                $data['user_id'] = $user['id'];
            }

            $data['create_time'] = date("Y-m-d H:i:s");
            $info_id = Db::name("Order_info")->strict(false)->insertGetId($data);
            if ($info_id) {
                //添加订单主表
                $data['info_id'] = $info_id;  //订单用户信息id
                $data['order_no'] = generateOrderNo();   //订单编号
                $order_id = Db::name("Order")->strict(false)->insertGetId($data);

                //更新用户等级（只升不降）
                $currentRoleId = Db::name("User")->where("id", $data['user_id'])->value("role_id");
                if($currentRoleId == 4 || $currentRoleId == 5){
                    //初级和中级用户
                    $deservedRoleId = self::calculateDeservedRole($data['user_id']);
                    // 如果没有当前等级或应得等级比当前高，则升级
                    if (!is_null($deservedRoleId) && $deservedRoleId > $currentRoleId) {
                        Db::name('User')
                            ->where('id', $data['user_id'])
                            ->update([
                                'role_id' => $deservedRoleId,
                                'role_upgrade_time' => date('Y-m-d H:i:s')
                            ]);
                    }
                }

                //给用户赠送积分，产品100积分，服务10积分
                $points = isset($data['service_id']) ? 110 : 100;
                $old_points = Db::name("User")->where("id", $data['user_id'])->value("points");
                $points_data = [
                    "user_id" => $data['user_id'],
                    "order_id" => $order_id,
                    "points" => $points,
                    "content" => "Buy products",
                    "create_time" => date('Y-m-d H:i:s')
                ];
                Db::name("User_points")->insert($points_data);
                Db::name("User")->where("id", $data['user_id'])->update([
                    "points" => $points + $old_points
                ]);

                //添加用户消息
                $message_data = [
                    "user_id" => $data['user_id'],
                    "sender_id" => session("adminId"),
                    "content" => "The administrator has generated your order.",
                    "main_id" => $order_id,
                    "type" => 0,
                    "create_time" => date('Y-m-d H:i:s')
                ];
                Db::name("User_message")->insert($message_data);

                //转化记录
                if($data['quote_id']){
                    Db::name("Quote")->where("id", $data['quote_id'])->update([
                        "is_transform" => 1,  //已转化
                        "update_time" => date('Y-m-d H:i:s')
                    ]);
                }

                //给账号email发送订单通知邮件
                $product_name = $data['product_id']?Db::name("Product")->where("id", $data['product_id'])->value("name"):"";
                $service_name = isset($data['service_id']) ? Db::name("Service")->where("id", $data['service_id'])->value("name"):"";
                $email_data = [
                    "system_name" => config('app.system_name'),
                    "username" => strstr($data['email'], '@', true),
                    "order_no" => $data['order_no'],
                    "order_date" => date("Y-m-d"),
                    "product_name" => $product_name,
                    "service_name" => $service_name,
                    "login_link" => config('app.site_url')."login",
                ];
                \app\services\MailService::sendEmail("order-notification", $data['email'], $email_data);

                //给邮寄email发送邮寄通知邮件
                if(!empty($data['express_email']) && !empty($data['tracking_no'])) {
                    $email_data = [
                        "system_name" => config('app.system_name'),
                        "username" => strstr($data['express_email'], '@', true),
                        "tracking_no" => $data['tracking_no'],
                        "tracking_company" => $data['tracking_company'],
                    ];
                    \app\services\MailService::sendEmail("shipment-notification", $data['express_email'], $email_data);
                }

                $this->success('创建成功！');
            } else {
                $this->error("创建失败，请重试！");
            }
        } else {
            $quote_id = input("quote_id");

            $quote = $quote_id ? Db::name("Quote")->where("id", $quote_id)->find() : [];

            $service = [];
            if($quote_id && $quote['product_id']){
                //产品的所有服务
                $service = Db::name("Product_relation")->alias("pr")
                    ->field("s.id, s.name")
                    ->leftjoin("Service s", "pr.related_id=s.id")
                    ->where(["pr.product_id"=>$quote['product_id'], "pr.type"=>1])
                    ->order("pr.sort asc")
                    ->select();
            }

            //所有用户
            $users = Db::name("User")->where("status", 1)->column("id, email");

            $product = Db::name("Product")->column("id, name");

            $country = getCountry();

            return view("", [
                "users" => $users,
                'product' => $product,
                'country' => $country,
                "quote" => $quote,
                "service" => $service,
            ]);
        }
    }


    /**
     * 计算用户应得的等级（不考虑当前等级）
     */
    public static function calculateDeservedRole($userId)
    {
        // 获取用户总消费金额
        $totalAmount = Db::name('Order')
            ->where('user_id', $userId)
            ->sum('money');

        // 获取所有角色（按金额升序排列）
        $roles = Db::name('Roles')
            ->order('money', 'asc')
            ->select();

        $deservedRole = null;
        foreach ($roles as $role) {
            if ($totalAmount >= $role['money']) {
                $deservedRole = $role;
            }
        }

        return $deservedRole ? $deservedRole['id'] : null;
    }

    public function edit()
    {
        if ($this->request->isPost()) {
            $data = input('post.');

            if(empty($data['email']) || empty($data['first_name']) || empty($data['last_name']) || empty($data['product_id'])) {
                $this->error("必填项未填！");
            }

            if(!empty($data['tracking_no']) && empty($data['tracking_company'])){
                $this->error("请填写物流公司！");
            }

            $info_id = Db::name("Order")->where("id", $data['id'])->value("info_id");
            if(!$info_id){
                $this->error("订单用户信息id参数错误！");
            }

            $order_id = $data['id'];
            unset($data['id']);

            $old_tracking_no = Db::name("Order")->where("id", $order_id)->value("tracking_no");

            $old_order_info = Db::name("Order_info")->field("email, express_email")->where("id", $info_id)->find();

            $data['update_time'] = date("Y-m-d H:i:s");
            $s = Db::name("Order_info")->where("id", $info_id)->strict(false)->save($data);
            if ($s) {
                //修改订单主表
                Db::name("Order")->where("id", $order_id)->strict(false)->save($data);

                //更新用户等级（只升不降）
                $currentRoleId = Db::name("User")->where("id", $data['user_id'])->value("role_id");
                if($currentRoleId == 4 || $currentRoleId == 5){
                    //初级和中级用户
                    $deservedRoleId = self::calculateDeservedRole($data['user_id']);
                    // 如果没有当前等级或应得等级比当前高，则升级
                    if (!is_null($deservedRoleId) && $deservedRoleId > $currentRoleId) {
                        Db::name('User')
                            ->where('id', $data['user_id'])
                            ->update([
                                'role_id' => $deservedRoleId,
                                'role_upgrade_time' => date('Y-m-d H:i:s')
                            ]);
                    }
                }

                if($data['email'] !== $old_order_info['email']) {
                    //修改过账号email，给账号email发送订单通知邮件
                    $product_name = $data['product_id']?Db::name("Product")->where("id", $data['product_id'])->value("name"):"";
                    $service_name = $data['service_id']?Db::name("Service")->where("id", $data['service_id'])->value("name"):"";
                    $email_data = [
                        "system_name" => config('app.system_name'),
                        "username" => strstr($data['email'], '@', true),
                        "order_no" => $data['order_no'],
                        "product_name" => $product_name,
                        "service_name" => $service_name,
                        "order_date" => date("Y-m-d"),
                        "login_link" => config('app.site_url')."login",
                    ];
                    \app\services\MailService::sendEmail("order-notification", $data['email'], $email_data);
                }

                if(!empty($data['express_email']) && !empty($data['tracking_no']) && ($data['express_email'] !== $old_order_info['express_email'] || $data['tracking_no'] !== $old_tracking_no)) {
                    //修改过邮寄email或tracking_no，给邮寄email发送邮寄通知邮件
                    $email_data = [
                        "system_name" => config('app.system_name'),
                        "username" => strstr($data['express_email'], '@', true),
                        "tracking_no" => $data['tracking_no'],
                        "tracking_company" => $data['tracking_company'],
                    ];
                    \app\services\MailService::sendEmail("shipment-notification", $data['express_email'], $email_data);
                }

                $this->success('修改成功！');
            } else {
                $this->error("修改失败，请重试！");
            }
        } else {
            $id = input('id');
            $getone = Db::name("Order")->where("id", $id)->find();
            $getone['product_name'] = Db::name("Product")->where("id", $getone['product_id'])->value("name");
            $getone['service_name'] = $getone['service_id'] ? Db::name("Service")->where("id", $getone['service_id'])->value("name") : "";

            //订单用户表信息
            $order_info = Db::name("Order_info")->where("id", $getone['info_id'])->find();

            //产品的所有服务
            $service = Db::name("Product_relation")->alias("pr")
                ->field("s.id, s.name")
                ->leftjoin("Service s", "pr.related_id=s.id")
                ->where(["pr.product_id"=>$getone['product_id'], "pr.type"=>1])
                ->order("pr.sort asc")
                ->select();

            //所有用户
            $users = Db::name("User")->where("status", 1)->column("id, email");

            //所有产品
            $product = Db::name("Product")->column("id, name");

            //产品进度列表
            $where = [
                "main_id" => $getone['product_id'],
                "type" => 0,  //产品
            ];
            $product_progress = Db::name("Progress")->where($where)->select();

            //服务进度列表
            $service_progress = [];
            if($getone['service_id']){
                $where = [
                    "main_id" => $getone['service_id'],
                    "type" => 1,  //服务
                ];
                $service_progress = Db::name("Progress")->where($where)->select();
            }

            //子订单
            $order_subs = Db::name('Order')
                ->where("parent_id", $id)
                ->order("id desc")
                ->select()
                ->each(function ($item) {
                    //服务名称
                    $item['service_name'] = Db::name("Service")->where("id", $item['service_id'])->value('name');

                    //服务进度
                    $item['service_progress'] = $item['service_progress_id']?Db::name("Progress")->where("id", $item['service_progress_id'])->value('name'):"";

                    //保密文件
                    $where = [
                        "order_id" => $item['id'],
                    ];
                    $item['order_file'] = Db::name('Order_file')->where($where)->order("id desc")->select()->toArray();

                    return $item;
                });

            //主订单 - 保密文件列表
            $order_file = Db::name('Order_file')
            ->where("order_id", $id)
            ->order("id desc")
            ->select()
            ->each(function ($item) use ($getone) {
                if($item['type'] == 0){
                    //产品
                    $item['main_name'] = Db::name("Product")->where("id", $getone['product_id'])->value("name");
                } else {
                    //服务
                    $item['main_name'] = Db::name("Service")->where("id", $getone['service_id'])->value("name");
                }

                return $item;
            });

            //当前用户-未使用的试用券
            $where = [
                "uc.user_id" => $getone['user_id'],
                "uc.is_used" => 0,  //未使用
            ];
            $currentTime = date('Y-m-d');
            $user_coupon = Db::name("User_coupon")->alias("uc")
                ->leftjoin("Coupon c", "uc.coupon_id=c.id")
                ->where($where)
                ->whereTime('uc.start_time', '<=', $currentTime) // 开始时间小于等于当前时间
                ->whereTime('uc.end_time', '>=', $currentTime)   // 结束时间大于等于当前时间
                ->column("uc.id, c.name,c.description");

            //当前用户-主订单已使用的试用券
            $where = [
                "uc.user_id" => $getone['user_id'],
                "uc.is_used" => 1,  //已使用
                "uc.order_id" => $id,
            ];
            $used_coupon = Db::name("User_coupon")->alias("uc")
            ->leftjoin("Coupon c", "uc.coupon_id=c.id")
            ->leftjoin("Order o", "uc.order_id=o.id")
            ->where($where)
            ->column("uc.id, c.name,c.description, o.order_no, uc.update_time, uc.create_time");
            //当前用户-本订单的子订单已使用的试用券
            $where = [
                "uc.user_id" => $getone['user_id'],
                "uc.is_used" => 1,  //已使用
                "o.parent_id" => $id,
            ];
            $used_coupon_subs = Db::name("User_coupon")->alias("uc")
            ->leftjoin("Coupon c", "uc.coupon_id=c.id")
            ->leftjoin("Order o", "uc.order_id=o.id")
            ->where($where)
            ->column("uc.id, c.name,c.description, o.order_no, uc.update_time, uc.create_time");
            $used_coupon = array_merge($used_coupon, $used_coupon_subs);

            $country = getCountry();

            return view("", [
                "getone" => $getone,  //订单详情
                "order_info" => $order_info,  //订单用户信息
                "users" => $users,   //所有用户
                'product' => $product,  //所有产品
                "service" => $service,  //产品所有服务
                "order_subs" => $order_subs,  //子订单列表
                "order_file" => $order_file,  //主订单-保密文件
                "product_progress" => $product_progress,  //产品进度列表
                "service_progress" => $service_progress,  //服务进度列表
                "user_coupon" => $user_coupon,  //用户未使用的试用券
                "used_coupon" => $used_coupon,  //本订单已使用的试用券
                'country' => $country
            ]);
        }
    }

    public function del()
    {
        $id = input('id');
        $s = Db::name("Order")->where("id", $id)->delete();
        if ($s) {
            $this->success("删除成功！");
        } else {
            $this->error("删除失败！");
        }
    }

    /**
     * 根据 user_id 获取 用户基本信息
     */
    public function getUserData()
    {
        $userId = $this->request->post('user_id');
        if (empty($userId)) {
            return json(['code' => 0, 'msg' => '请选择用户']);
        }

        $userData = Db::name("User")->where("id", $userId)->find();

        if ($userData) {
            return json(['code' => 1, 'msg' => '获取成功', 'data' => $userData]);
        } else {
            return json(['code' => 0, 'msg' => '用户不存在']);
        }
    }

    /**
     * 根据 product_id 获取 service 列表 以及 产品进度列表
     */
    public function getServices()
    {
        $product_id = input('product_id');

        //产品服务列表
        $service = Db::name("Product_relation")->alias("pr")
            ->field("s.id, s.name")
            ->leftjoin("Service s", "pr.related_id=s.id")
            ->where(["product_id"=>$product_id, "type"=>1])
            ->order("pr.sort asc")
            ->select();

        //产品进度列表
        $where = [
            "main_id" => $product_id,
            "type" => 0,  //产品
        ];
        $progress = Db::name("Progress")->where($where)->select();

        return json([
            "code" => 1,
            "data" => [
               "service" => $service,
               "progress" => $progress,
            ],
        ]);
    }

    /**
     * 根据 service_id 获取 服务进度列表
     */
    public function getServiceProgress(){
        $service_id = input('service_id');

        $where = [
            "main_id" => $service_id,
            "type" => 1,  //服务
        ];
        $progress = Db::name("Progress")->where($where)->select();

        return json([
            "code" => 1,
            "data" => $progress,
        ]);
    }


    //添加后续服务
    public function add_service(){
        if ($this->request->isPost()) {
            $data = input('post.');

            if(!$data['service_id'] || empty($data['money']) || empty($data['parent_id'])){
                $this->error("必填项未填！");
            }

            if(!empty($data['tracking_no']) && empty($data['tracking_company'])){
                $this->error("请填写物流公司！");
            }

            $data['order_no'] = generateOrderNo();

            $data['create_time'] = date("Y-m-d H:i:s");
            $order_id = Db::name("Order")->strict(false)->insertGetId($data);
            if ($order_id) {
                //给用户赠送积分，产品100积分，服务10积分
                $points = 10;
                $old_points = Db::name("User")->where("id", $data['user_id'])->value("points");
                $points_data = [
                    "user_id" => $data['user_id'],
                    "order_id" => $order_id,
                    "points" => $points,
                    "content" => "Buy service",
                    "create_time" => date('Y-m-d H:i:s')
                ];
                Db::name("User_points")->insert($points_data);
                Db::name("User")->where("id", $data['user_id'])->update([
                    "points" => $points + $old_points
                ]);

                //添加用户消息
                $message_data = [
                    "user_id" => $data['user_id'],
                    "sender_id" => session("adminId"),
                    "content" => "The administrator has generated your order.",
                    "main_id" => $order_id,
                    "type" => 0,
                    "create_time" => date('Y-m-d H:i:s')
                ];
                Db::name("User_message")->insert($message_data);

                //给产品订单的账号email发送订单通知邮件
                $order_info = Db::name("Order_info")->field("email, express_email")->where("id", $data['info_id'])->find();
                $service_name = $data['service_id']?Db::name("Service")->where("id", $data['service_id'])->value("name"):"";
                $email_data = [
                    "system_name" => config('app.system_name'),
                    "username" => strstr($order_info['email'], '@', true),
                    "order_no" => $data['order_no'],
                    "product_name" => "",
                    "service_name" => $service_name,
                    "order_date" => date("Y-m-d"),
                    "login_link" => config('app.site_url')."login",
                ];
                \app\services\MailService::sendEmail("order-notification", $order_info['email'], $email_data);

                //给产品订单的邮寄email发送邮寄通知邮件
                if(!empty($order_info['express_email']) && !empty($data['tracking_no'])) {
                    $email_data = [
                        "system_name" => config('app.system_name'),
                        "username" => strstr($order_info['express_email'], '@', true),
                        "tracking_no" => $data['tracking_no'],
                        "tracking_company" => $data['tracking_company'],
                    ];
                    \app\services\MailService::sendEmail("shipment-notification", $order_info['express_email'], $email_data);
                }

                $this->success('添加成功！');
            } else {
                $this->error("添加失败，请重试！");
            }
        }
    }

    public function edit_service(){
        if ($this->request->isPost()) {
            $data = input('post.');

            if(!$data['service_id'] || empty($data['money'])){
                $this->error("必填项未填！");
            }

            $old_tracking_no = Db::name("Order")->where("id", $data['id'])->value("tracking_no");

            $data['update_time'] = date("Y-m-d H:i:s");
            $s = Db::name("Order")->strict(false)->save($data);
            if ($s) {
                $order_info = Db::name("Order_info")->field("email, express_email")->where("id", $data['info_id'])->find();
                //修改过tracking_no，给产品订单的邮寄email发送邮寄通知邮件
                if(!empty($order_info['express_email']) && !empty($data['tracking_no']) && $data['tracking_no'] !== $old_tracking_no) {
                    $email_data = [
                        "system_name" => config('app.system_name'),
                        "username" => strstr($order_info['express_email'], '@', true),
                        "tracking_no" => $data['tracking_no'],
                        "tracking_company" => $data['tracking_company'],
                    ];
                    \app\services\MailService::sendEmail("shipment-notification", $order_info['express_email'], $email_data);
                }

                $this->success('修改成功！');
            } else {
                $this->error("修改失败，请重试！");
            }
        } else {
            $id = input('id');
            //后续服务订单信息
            $getone = Db::name("Order")->where("id", $id)->find();

            //父订单
            $order = Db::name("Order")->where("id", $getone['parent_id'])->find();

            //所有服务
            $service = Db::name("Product_relation")->alias("pr")
                ->field("s.id, s.name")
                ->leftjoin("Service s", "pr.related_id=s.id")
                ->where(["pr.product_id"=>$order['product_id'], "pr.type"=>1])
                ->order("pr.sort asc")
                ->select();

            //服务进度列表
            $where = [
                "main_id" => $getone["service_id"],
                "type" => 1,
            ];
            $service_progress = $getone["service_id"]?Db::name("Progress")->where($where)->order("sort asc")->select():[];

            return view("", [
                "getone" => $getone,
                "order" => $order,
                "service" => $service,
                "service_progress" => $service_progress,
            ]);
        }
    }

    public function del_service()
    {
        $id = input('id');
        $s = Db::name("Order")->where("id", $id)->delete();
        if ($s) {
            $this->success("删除成功！");
        } else {
            $this->error("删除失败！");
        }
    }

    //添加保密文件
    public function add_file(){
        if ($this->request->isPost()) {
            $data = input('post.');

            if($data['up_type'] == 'url'){
                //网址
                if(empty($data['url'])){
                    $this->error("必填项未填！");
                }
            } else {
                //文件
                if(!$_FILES['file']['name']){
                    $this->error("必填项未填！");
                }

                if($_FILES['file']['name']) {
                    $file = $this->uploadFile(request()->file("file"), "security");
                    $data['file'] = $file['url'];
                    $data['file_name'] = $file['original_name'];
                }
            }

            $data['file_type'] = 0;  //保密文件
            $data['create_time'] = $data['update_time'] = date("Y-m-d H:i:s");
            $s = Db::name("Order_file")->strict(false)->insertGetId($data);
            if ($s) {
                $this->success('添加成功！');
            } else {
                $this->error("添加失败，请重试！");
            }
        }
    }

    //修改保密文件
    public function edit_file(){
        if ($this->request->isPost()) {
            $data = input('post.');

            if(empty($data['order_id'])){
                $this->error("参数错误！");
            }

            if($data['up_type'] == 'url'){
                //网址
                if(empty($data['url'])){
                    $this->error("必填项未填！");
                }

                $fileRecord = Db::name("Order_file")->find($data['id']);
                if($fileRecord['file']){
                    $urlPath = parse_url($fileRecord['file'], PHP_URL_PATH);
                    $relativePath = preg_replace('#^/storage/#', '', $urlPath);

                    // 删除文件（带不存在检测）
                    if (Filesystem::disk('public')->has($relativePath)) {
                        Filesystem::disk('public')->delete($relativePath);
                    }
                }

                $data['file'] = $data['file_name'] = "";

            } else {
                //文件
                if(!$_FILES['file']['name'] && empty($data['file_path'])){
                    $this->error("必填项未填！");
                }

                if($_FILES['file']['name']) {
                    $file = $this->uploadFile(request()->file("file"), "security");
                    $data['file'] = $file['url'];
                    $data['file_name'] = $file['original_name'];
                } else {
                    $data['file'] = $data['file_path'];
                }

                $data['url'] = "";
            }

            $data['update_time'] = date("Y-m-d H:i:s");
            $s = Db::name("Order_file")->strict(false)->save($data);
            if ($s) {
                $this->success('添加成功！');
            } else {
                $this->error("添加失败，请重试！");
            }

        } else {
            $id = input('id');
            $getone = Db::name("Order_file")->where("id", $id)->find();

            //订单信息
            $order = Db::name("Order")->where("id", $getone['order_id'])->find();
            $order['product_name'] = $order['product_id']?Db::name("Product")->where("id", $order['product_id'])->value("name"):"";
            $order['service_name'] = $order['service_id']?Db::name("Service")->where("id", $order['service_id'])->value("name"):"";

            $order_subs = [];
            if($order['parent_id'] !== 0){
                //子订单列表选项
                $order_subs = Db::name("Order")
                ->where("parent_id", $order['parent_id'])
                ->order("id desc")
                ->select()
                ->each(function ($item) {
                    $item['service_name'] = Db::name("Service")->where("id", $item['service_id'])->value('name');

                    return $item;
                });
            }

            return view("", [
                "getone" => $getone,  //保密文件信息
                "order" => $order,  //订单信息
                "order_subs" => $order_subs,  //子订单列表
            ]);
        }
    }

    //删除保密文件
    public function del_file()
    {
        $id = input('id', 0, 'intval');

        // 查询记录
        $fileRecord = Db::name("Order_file")->find($id);
        if (!$fileRecord) {
            $this->error('文件记录不存在');
        }

        if($fileRecord['file']){
            $urlPath = parse_url($fileRecord['file'], PHP_URL_PATH);
            $relativePath = preg_replace('#^/storage/#', '', $urlPath);

            // 删除文件（带不存在检测）
            if (Filesystem::disk('public')->has($relativePath)) {
                if (!Filesystem::disk('public')->delete($relativePath)) {
                    $this->error('文件删除失败');
                }
            } else {
                $this->error('文件不存在');
            }
        }

        // 删除记录
        $s = Db::name("Order_file")->delete($id);
        if ($s) {
            $this->success("删除成功！");
        } else {
            $this->error("删除失败！");
        }
    }

    //使用试用券
    public function use_coupon(){
        if ($this->request->isPost()) {
            $data = input('post.');

            if(empty($data['order_id']) || empty($data['id'])){
                $this->error("必填项未填！");
            }

            $s = Db::name("User_coupon")->where("id", $data['id'])->update([
                "order_id" => $data['order_id'],  //订单id
                "is_used" => 1,  //已使用
                "update_time" => date("Y-m-d H:i:s")
            ]);

            if ($s) {
                $this->success('操作成功！');
            } else {
                $this->error("操作失败，请重试！");
            }
        }
    }

    //删除使用试用券
    public function cancel_coupon(){
        $id = input('id');

        //修改为未使用
        $s = Db::name("User_coupon")->where("id", $id)->save(["is_used"=>0]);
        if($s){
            $this->success("删除成功");
        }else{
            $this->error("删除失败");
        }
    }

    //咨询列表
    public function quote()
    {
        $params = request()->param();
        $where = [];

        if (!empty($params['keyword'])) {
            $where[] = ["email|first_name|last_name", "like", "%" . $params['keyword'] . "%"];
        }

        $List = Db::name('Quote')
            ->where($where)
            ->order("id desc")
            ->paginate([
                'query'     =>  request()->param(), //url额外参数
                'list_rows' => 20, //每页数量
            ])->each(function ($item) {
                $item['phone'] = Db::name("User")->where("id", $item['user_id'])->value('phone');

                return $item;
            });

        return view("", [
            "List" => $List,
            "params" => $params,
        ]);
    }

    public function view_quote(){
        $id = input('id');
        $getone = Db::name("Quote")->where("id", $id)->find();
        $getone['product_name'] = Db::name("Product")->where("id", $getone['product_id'])->value("name");
        $getone['service_name'] = Db::name("Service")->where("id", $getone['service_id'])->value("name");

        return view("", [
            "getone" => $getone,
        ]);
    }
}
