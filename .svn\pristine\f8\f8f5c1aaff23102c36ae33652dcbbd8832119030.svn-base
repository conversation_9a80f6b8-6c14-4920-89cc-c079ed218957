<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sign in to OpenDEL™</title>
    <link rel="stylesheet" href="__CSS__/style.css">
    </link>
</head>
<style>
    ::-webkit-input-placeholder {
        color: #ddd;
    }

    ::-moz-placeholder {
        color: #ddd;
    }
</style>

<body>
    <div class="w-full md:min-h-lh  md:grid md:grid-cols-2">
        <div class="hidden md:block pr-20 md:h-lvh sticky top-0">
            <img src="__IMG__/backgrounds/login_bj.jpg" class="object-cover w-full h-full" alt="">
        </div>
        <div class="flex flex-col w-11/12 mx-auto py-5 pb-10 md:p-0">
            <header class="mb-7 md:fixed md:left-9 md:top-9">
                <a href="/">
                    <img src="__IMG__/logo.png" alt="Login" class="h-8 md:hidden" />
                    <img src="__IMG__/logo-login.png" alt="Login" class="hidden md:block md:w-auto" />
                </a>
            </header>

            <main class="md:flex md:pl-10 md:pr-36">
                <form action="" method="post" id="registerForm" class="md:w-full md:pt-28 md:pb-10">
                    <h1 class="text-xl Roboto_Bold mb-8
                    md:text-[2.5rem] md:mb-16">
                        Sign Up to OpenDELClub
                    </h1>
                    <div class="flex flex-col gap-y-4 text-sm md:text-xl">
                        <div class="input-item">
                            <label for="email" class="Roboto_Bold">
                                <span class="text-[#ff0000] align-text-top">*</span> Email
                            </label>
                            <div class="relative flex flex-col mt-2">
                                <input type="email" name="email" id="email" placeholder="Please input your email" class="w-full h-[3.125rem] border border-[#d8e2ff] rounded-md bg-white bg-[url(__IMG__/icons/youxiang.png)] bg-no-repeat bg-[12px_center] bg-size-[1.25rem] px-5 pl-12
                                md:h-[3.75rem] md:pl-16 md:bg-auto md:bg-[1.2rem_center] md:pr-32
                                " autocomplete="off" />
                                <button type="button" class="text-white bg-[#f08411] rounded-md top-1/2 -translate-y-1/2 absolute right-2 px-2 py-1.5 cursor-pointer
                                    md:right-4 md:text-base" id="SendOtp">Send OTP</button>
                            </div>
                            <div class=" hidden error text-[#ff0000] mt-2
                            md:text-base
                            ">
                                Please fill in the email information
                            </div>
                        </div>
                        <div class="input-item">
                            <label for="captcha" class="Roboto_Bold">
                                <span class="text-[#ff0000] align-text-top">*</span> Captcha
                            </label>
                            <div class="relative flex flex-col mt-2">
                                <input type="text" name="captcha" id="captcha"
                                    placeholder="Please enter email verification code" class="w-full h-[3.125rem] border border-[#d8e2ff] rounded-md bg-white bg-[url(__IMG__/icons/yanzhengma.png)] bg-no-repeat bg-[12px_center] bg-size-[1.25rem] px-5 pl-12 pr-20
                                    md:h-[3.75rem] md:pl-16 md:bg-auto md:bg-[1.2rem_center]
                                    " autocomplete="off" />
                            </div>
                            <div class=" hidden error text-[#ff0000] mt-2
                            md:text-base
                            ">
                                Please enter the verification information
                            </div>
                        </div>
                        <div class="input-item">
                            <label for="password" class="Roboto_Bold">
                                <span class="text-[#ff0000] align-text-top">*</span> Password
                            </label>
                            <div class="relative flex flex-col mt-2">
                                <input type="password" name="password" id="password"
                                    placeholder="Advise a secure password" class="w-full h-[3.125rem] border border-[#d8e2ff] rounded-md bg-white bg-[url(__IMG__/icons/mima.png)] bg-no-repeat bg-[12px_center] bg-size-[1.25rem] px-5 pl-12 pr-12
                                    md:h-[3.75rem] md:pl-16 md:bg-auto md:bg-[1.2rem_center]
                                    " autocomplete="off" />
                                <button type="button" class="bg-[url(__IMG__/icons/yanjing_yincang_o.png)] bg-no-repeat bg-[12px_center] bg-size-[1.25rem] rounded-md top-1/2 -translate-y-1/2 absolute right-2 w-10 h-full cursor-pointer
                                    md:bg-size-auto" id="passwordEye">
                                </button>
                            </div>
                            <div class="hidden flex error text-[#ff0000] mt-2 items-center gap-x-1.5
                            md:text-base
                            ">
                                <img src="__IMG__/icons/tis.png" alt=""> 8-16
                                characters must contain both digits and letters
                            </div>
                        </div>
                        <div class="input-item">
                            <label for="password_confirm" class="Roboto_Bold">
                                <span class="text-[#ff0000] align-text-top">*</span> Confirm Password
                            </label>
                            <div class="relative flex flex-col mt-2">
                                <input type="password" name="password_confirm" id="password_confirm"
                                    placeholder="Advise a secure password" class="w-full h-[3.125rem] border border-[#d8e2ff] rounded-md bg-white bg-[url(__IMG__/icons/mima.png)] bg-no-repeat bg-[12px_center] bg-size-[1.25rem] px-5 pl-12 pr-12
                                    md:h-[3.75rem] md:pl-16 md:bg-auto md:bg-[1.2rem_center]
                                    " autocomplete="off" />
                                <button type="button" class="bg-[url(__IMG__/icons/yanjing_yincang_o.png)] bg-no-repeat bg-[12px_center] bg-size-[1.25rem] rounded-md top-1/2 -translate-y-1/2 absolute right-2 w-10 h-full cursor-pointer
                                    md:bg-size-auto" id="passwordEye2">
                                </button>
                            </div>
                            <div class=" hidden flex error text-[#ff0000] mt-2 items-center gap-x-1.5
                            md:text-base
                            ">
                                <img src="__IMG__/icons/tis.png" alt=""> 8-16
                                characters must contain both digits and letters
                            </div>
                        </div>
                        <div class="input-item">
                            <label for="country" class="Roboto_Bold">
                                <span class="text-[#ff0000] align-text-top">*</span> Country/Region
                            </label>
                            <div class="relative flex flex-col mt-2">
                                <select name="country" id="country" class="w-full h-[3.125rem] border border-[#d8e2ff] rounded-md bg-transparent bg-[url(__IMG__/icons/news.png)] bg-no-repeat bg-[12px_center] bg-size-[1.25rem] px-5 pl-12 pr-12 appearance-none text-[#ddd] cursor-pointer
                                    md:h-[3.75rem] md:pl-16 md:bg-auto md:bg-[1.2rem_center]
                                    ">
                                    <option value="" disabled selected style="color:#ddd;">
                                        Please select your country/region
                                    </option>
                                    {volist name="country" id="vo"}
                                    <option value="{$vo.en_name}" style="color: #333;">{$vo.en_name} {$vo.cn_name}</option>
                                    {/volist}
                                </select>
                                <button type="button"
                                    class="bg-[url(__IMG__/icons/mti-jiantouyou.png)] bg-no-repeat bg-[12px_center] bg-size-[1.25rem] rounded-md top-1/2 -translate-y-1/2 absolute right-2 w-10 h-full -z-10">
                                </button>
                            </div>
                            <div class=" hidden error text-[#ff0000] mt-2
                            md:text-base
                            ">
                                Please fill in the information.
                            </div>
                        </div>
                        <div class="input-item">
                            <label for="first_name" class="Roboto_Bold">
                                <span class="text-[#ff0000] align-text-top">*</span> First Name
                            </label>
                            <div class="relative flex flex-col mt-2">
                                <input type="text" name="first_name" id="first_name"
                                    placeholder="Please enter your first name" class="w-full h-[3.125rem] border border-[#d8e2ff] rounded-md bg-white bg-[url(__IMG__/icons/yonghu-1.png)] bg-no-repeat bg-[12px_center] bg-size-[1.25rem] px-5 pl-12 pr-20
                                    md:h-[3.75rem] md:pl-16 md:bg-auto md:bg-[1.2rem_center]
                                    " autocomplete="off" />
                            </div>
                            <div class=" hidden error text-[#ff0000] mt-2
                            md:text-base
                            ">
                                Please fill in the information.
                            </div>
                        </div>
                        <div class="input-item">
                            <label for="last_name" class="Roboto_Bold">
                                <span class="text-[#ff0000] align-text-top">*</span> Last Name
                            </label>
                            <div class="relative flex flex-col mt-2">
                                <input type="text" name="last_name" id="last_name"
                                    placeholder="Please enter your last name" class="w-full h-[3.125rem] border border-[#d8e2ff] rounded-md bg-white bg-[url(__IMG__/icons/yonghu-1.png)] bg-no-repeat bg-[12px_center] bg-size-[1.25rem] px-5 pl-12 pr-6
                                    md:h-[3.75rem] md:pl-16 md:bg-auto md:bg-[1.2rem_center]
                                    " autocomplete="off" />
                            </div>
                            <div class=" hidden error text-[#ff0000] mt-2
                            md:text-base
                            ">
                                Please fill in the information.
                            </div>
                        </div>
                        <div class="input-item">
                            <label for="organization" class="Roboto_Bold">
                                <span class="text-[#ff0000] align-text-top">*</span>
                                Organization/Institution/Corporation
                            </label>
                            <div class="relative flex flex-col mt-2">
                                <input type="text" name="organization" id="organization"
                                    placeholder="Please enter your organization/institution/corporation" class="w-full h-[3.125rem] border border-[#d8e2ff] rounded-md bg-white bg-[url(__IMG__/icons/jigouguanli.png)] bg-no-repeat bg-[12px_center] bg-size-[1.25rem] px-5 pl-12 pr-6
                                    md:h-[3.75rem] md:pl-16 md:bg-auto md:bg-[1.2rem_center]
                                    " autocomplete="off" />
                            </div>
                            <div class=" hidden error text-[#ff0000] mt-2
                            md:text-base
                            ">
                                Please fill in the information.
                            </div>
                        </div>
                        <div class="input-item">
                            <label for="title" class="Roboto_Bold">
                                Title:
                            </label>
                            <div class="relative flex flex-col mt-2">
                                <input type="text" name="title" id="title"
                                    placeholder="Please enter your Title" class="w-full h-[3.125rem] border border-[#d8e2ff] rounded-md bg-white bg-[url(__IMG__/icons/zhiweimoban.png)] bg-no-repeat bg-[12px_center] bg-size-[1.25rem] px-5 pl-12 pr-6
                                    md:h-[3.75rem] md:pl-16 md:bg-auto md:bg-[1.2rem_center]
                                    " autocomplete="off" />
                            </div>
                        </div>
                        <div class="input-item mt-3">
                            <label for="agree" class="relative">
                                <input type="checkbox" name="" id="agree"
                                    class="mr-2 w-4 h-4 align-middle border border-[#d8e2ff]">
                                    <span class="text-[#111]  md:text-base">I agree to <a
                                        class="underline text-[#155797] cursor-pointer" id="privacy_link">User Privacy Agreement</a></span>
                                      
                            </label>
                            <div class=" hidden error text-[#ff0000] mt-2
                            md:text-base
                            ">
                                Please fill in the information.
                            </div>
                        </div>
                        <div class="input-item
                        md:mt-3
                        ">
                            <button type="submit" class="bg-[#155797] text-white rounded-full text-base py-2 w-full cursor-pointer Roboto_Bold
                                md:h-[3.75rem]" id="submitBtn">Create Account
                            </button>
                        </div>
                        <div class="input-item text-center ">
                            Already have an account? <a href="/login/" class="text-[#f08411] underline">Sign In</a>
                        </div>
                    </div>
                </form>
            </main>
        </div>
    </div>
    <!-- Terms of Service -->
        <div class="terms-service" id="terms_service" style="display: none;">
            <div
                class="fixed top-0 left-0 bottom-0 right-0 w-full bg-[rgba(21,82,144,.5)] z-[999] flex items-center justify-center">
                <div class="w-11/12 mx-auto md:w-8/12 bg-white h-[80%] md:h-[90%] rounded-md flex flex-col">
                    <div class="terms-service-title relative border-b border-[#c4d7ff] text-center py-4">
                        <h3 class="text-xl font-bold md:text-2xl md:py-3.5">User Privacy Agreement</h3>
                        <span class="absolute right-4 top-1/2 -translate-1/2 cursor-pointer close_terms_service">
                            <img src="__IMG__/icons/cuo.png" alt="" class="w-4 h-4 opacity-50">
                        </span>
                    </div>
                    <div class="flex-1 overflow-y-auto border-b border-[#c4d7ff] p-3 md:p-6">
                        <div class="bg-[#f8fdff] about-container p-3 md:p-6">
                            <h2>
                                Terms of Service
                            </h2>
                            <p>
                                This site (the "Site") is owned, managed and maintained by HitGen Inc. (hereinafter
                                referred to as "the company" or "us"). Please read the following terms carefully before
                                using this website. Using this website indicates that you have known and accepted these
                                terms. If you do not accept these terms, please do not use this website. We maintain
                                this website for information dissemination and communication purposes. This page
                                contains terms of use regarding access and usage rights of this website. If you do not
                                accept these terms of use or comply with their provisions, you must not use this
                                website.
                            </p>
                            <h2>
                                Changes To Terms
                            </h2>
                            <p>
                                We may, at any time, for any reason and without notice, make changes to the Site,
                                including its look, feel, format, and content, as well as the products and services as
                                described in the Site. Any modifications will take effect when posted to the Site.
                                Therefore, each time you access the Site, you need to review the Terms of Use upon which
                                access and use of the Site is conditioned. By your continuing use of the Site after
                                changes are posted, you will be deemed to have accepted such changes.
                            </p>
                            <h2>
                                Jurisdiction
                            </h2>
                            <p>
                                This website is not open to any individual or entity in a specific jurisdiction (due to
                                nationality, residence, citizenship or other reasons). If within this jurisdiction, the
                                publication or existence of this website and its content (including product and
                                services) is unachievable or violates local laws or regulations. If you have such a
                                situation, you have no right to access or use any information on this website. We do not
                                make any statement as to whether the information, opinions, suggestions or other content
                                of this website (collectively referred to as the "content") is applicable outside of
                                China. Users who choose to access this website from other regions are at their own risk
                                and are responsible for complying with local laws.
                            </p>
                            <h2>
                                Scope of Use
                            </h2>
                            <p>
                                You are only authorized to view, use, copy for your records and download small portions
                                of the Content (including without limitation text, graphics, software, audio and video
                                files and photos) of the Site for your informational, non-commercial use, provided that
                                you leave all the copyright notices, including copyright management information, or
                                other proprietary notices intact.
                            </p>
                            <p>
                                You may not store, modify, reproduce, transmit, reverse engineer or distribute a
                                significant portion of the Content on the Site, or the design or layout of the Site or
                                individual sections of it, in any form or media. The systematic retrieval of data from
                                the Site is also prohibited.
                            </p>
                            <p>
                                E-mail submission via the Internet may be insecure and risk being intercepted by a third
                                party. Please consider this before sending any information via email. Please refer to
                                our" privacy policy".
                            </p>
                            <p>
                                You agree not to submit or transmit any e-mails or materials through the Site that: (i)
                                are defamatory, threatening, obscene or harassing, (ii) contain a virus, worm, Trojan
                                horse or any other harmful component, (iii) incorporate copyrighted or other proprietary
                                material of any third party without such party's permission or (iv) otherwise violate
                                any applicable laws. We shall not be subject to any obligations of confidentiality
                                regarding any information or materials that you submit online except as specified in the
                                Terms of Use, or as set forth in any additional terms and conditions relating to
                                specific products or services, or as otherwise specifically agreed or required by law.
                                The commercial use, reproduction, transmission or distribution of any information,
                                software or other material available through the Site without our prior written consent
                                is strictly prohibited.
                            </p>
                        </div>
                    </div>
                    <div class="px-6 py-4 md:py-6">
                        <div class="flex flex-col gap-3 text-sm md:text-base btn-terms text-white mx-auto w-10/12 md:flex-row md:justify-center">
                            <button type="button" class="bg-[#dae9ff] md:min-w-[12.5rem] cursor-pointer" disabled id="btn_agree">Agree and Continue (<span
                                    id="Countdown">5</span>s) </button>
                            <button type="button" class="bg-[#ddd] md:min-w-[12.5rem] cursor-pointer" id="btn_disagree">Disagree and Exit</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
</body>

<script src="__JS__/vendors/jquery-1.8.3.min.js"></script>
<script src="__STATIC__/layer/layer.js"></script>
<script src="__JS__/privacy.js"></script>

<script>
    $(document).ready(function() {
        // 国家选择框样式控制
        var $select = $('#country');
        $select.on('change', function() {
            $(this).css('color', $(this).val() === "" ? "#ddd" : "#333");
        }).trigger('change'); // 初始化时设置颜色

        // 发送邮件验证码
        $('#SendOtp').on('click', function() {
            var $btn = $(this);

            var $input = $btn.parent().find('input');
            var $error = $btn.parent().parent().find('.error');
            var emailReg = /^[\w.-]+@[\w.-]+\.[a-zA-Z]{2,}$/;

            // 先隐藏错误提示
            $error.addClass('hidden');

            if (!$input || !$input.val()) {
                $error.text('Please fill in the email information').removeClass('hidden');
                $input.focus();
                return;
            }

            if (!emailReg.test($input.val())) {
                $error.text('Please enter a valid email address').removeClass('hidden');
                $input.focus();
                return;
            }

            $btn.prop('disabled', true).addClass('opacity-50');

            // 发送AJAX请求
            $.ajax({
                url: '/send_captcha',
                type: 'POST',
                data: {email: $input.val(), type: 'register'},
                dataType: 'json',
                success: function(data) {
                    if (data.code === 1) {
                        //发送成功
                        layer.msg(data.msg, { icon: 1, time: 2000 });
                    } else {
                        //发送失败
                        layer.msg('Error: ' + data.msg, { icon: 2 });
                    }
                },
                error: function(xhr, status, error) {
                    layer.msg('An error occurred: ' + error, { icon: 2 });
                },
                complete: function() {
                    // 无论成功失败，都重新启用按钮
                    $btn.prop('disabled', false).removeClass('opacity-50');
                }
            });
        });

        // 表单提交
        $('#registerForm').on('submit', function(e) {
            //阻止表单提交
            e.preventDefault();

            var valid = true;

            // 隐藏所有错误提示
            $('.error').addClass('hidden');

            // 校验必填项
            ['email', 'captcha', 'password', 'password_confirm', 'country', 'first_name', 'last_name', 'organization'].forEach(function(id) {
                var $input = $('#' + id);
                if (!$input.length || $input.val() === '' || $input.val() === null) {
                    $input.parent().parent().find('.error').removeClass('hidden');
                    valid = false;
                }
            });

            // 邮箱格式校验
            var $emailInput = $('#email');
            var $emailError = $emailInput.parent().parent().find('.error');
            var emailReg = /^[\w.-]+@[\w.-]+\.[a-zA-Z]{2,}$/;
            if ($emailInput.length && $emailInput.val() && !emailReg.test($emailInput.val())) {
                $emailError.text('Please enter a valid email address').removeClass('hidden');
                valid = false;
            }

            // 密码复杂性校验
            var pwd = $('#password').val();
            var $pwdError = $('#password').parent().parent().find('.error');
            var pwdReg = /^(?=.*[A-Za-z])(?=.*\d).{8,16}$/;
            if (!pwdReg.test(pwd)) {
                $pwdError.html('<img src="__IMG__/icons/tis.png" alt=""> 8-16 characters must contain both digits and letters').removeClass('hidden');
                valid = false;
            }

            // 确认密码一致性
            var cpwd = $('#password_confirm').val();
            var $cpwdError = $('#password_confirm').parent().parent().find('.error');
            if (pwd !== cpwd) {
                $cpwdError.text('The passwords entered twice do not match.').removeClass('hidden');
                valid = false;
            }

            // 校验是否勾选同意协议
            var $agree = $('#agree');
            var $agreeError = $agree.parent().parent().find('.error');
            if (!$agree.prop('checked')) {
                $agreeError.text('Please check the box to agree to the User Privacy Policy').removeClass('hidden');
                valid = false;
            }

            if (!valid) {
                return false;
            }

            // 禁用提交按钮
            var $submitBtn = $('#submitBtn');
            $submitBtn.prop('disabled', true).addClass('opacity-50 cursor-not-allowed');

            // 发送AJAX请求
            $.ajax({
                url: '/login/register',
                type: 'POST',
                data: $(this).serialize(),
                dataType: 'json',
                success: function(data) {
                    if (data.code === 1) {
                        //注册成功
                        layer.msg(data.msg, { icon: 1, time: 2000 }, () => {
                            location.href = data.url;
                        });
                    } else {
                        //注册失败
                        layer.msg('Error: ' + data.msg, { icon: 2 });
                    }
                },
                error: function(xhr, status, error) {
                    layer.msg('An error occurred: ' + error, { icon: 2 });
                },
                complete: function() {
                    // 无论成功失败，都重新启用按钮
                    $submitBtn.prop('disabled', false).removeClass('opacity-50 cursor-not-allowed');
                }
            });

        });

        // 密码显示/隐藏及背景切换
        function bindPasswordEye(eyeId) {
            var eye = document.getElementById(eyeId);
            if (eye) {
                eye.addEventListener('click', function () {
                    var input = eye.parentElement.querySelector('input');
                    if (input) {
                        if (input.type === 'password') {
                            input.type = 'text';
                            eye.style.backgroundImage = "url('__IMG__/icons/yanjing_yincang_1.png')";
                        } else {
                            input.type = 'password';
                            eye.style.backgroundImage = "url('__IMG__/icons/yanjing_yincang_o.png')";
                        }
                    }
                });
            }
        }
        bindPasswordEye('passwordEye');
        bindPasswordEye('passwordEye2');
    });
</script>

</html>