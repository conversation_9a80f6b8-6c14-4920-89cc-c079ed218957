//置顶
document.addEventListener('DOMContentLoaded', function () {
    var toTopBtn = document.querySelector('.fixed.right-5.bottom-5');
    if (!toTopBtn) return;
    // 初始隐藏
    toTopBtn.style.display = 'none';
    window.addEventListener('scroll', function () {
        if (window.scrollY > 300) {
            toTopBtn.style.display = 'flex';
        } else {
            toTopBtn.style.display = 'none';
        }
    });
    toTopBtn.addEventListener('click', function () {
        window.scrollTo({ top: 0, behavior: 'smooth' });
    });
});

//关闭robot提示信息
document.addEventListener('DOMContentLoaded', function () {
    var closeBtn = document.getElementById('btn_close');
    if (closeBtn) {
        closeBtn.addEventListener('click', function () {
            var parent = closeBtn.closest('.robot-text');
            if (parent) parent.style.display = 'none';
        });
    }
});

//搜索框
document.addEventListener('DOMContentLoaded', function () {
    var searchBtn = document.getElementById('search_btn');
    var searchForm = document.getElementById('search_form');
    var submitBtn = document.getElementById('submit_btn');
    var input = searchForm ? searchForm.querySelector('input[type="text"]') : null;

    if (searchBtn && searchForm && input) {
        searchBtn.addEventListener('click', function () {
            // searchBtn.style.display = 'absolute';
            searchBtn.classList.add('md:absolute', 'right-0', '-z-10');
            searchForm.classList.remove('hidden');
            input.focus();
        });

        // 只有不是点击提交按钮时才隐藏
        input.addEventListener('blur', function (e) {
            // e.relatedTarget 是下一个获得焦点的元素
            if (e.relatedTarget !== submitBtn) {
                searchForm.classList.add('hidden');
                searchBtn.classList.remove('md:absolute', 'right-0', '-z-10');
            }
        });

        // 让提交按钮可被 input 的 blur 识别
        if (submitBtn) {
            submitBtn.setAttribute('tabindex', '-1');
        }
    }

    if (searchForm && submitBtn && input) {
        submitBtn.addEventListener('click', function (e) {
            if (input.value.trim() === '') {
                e.preventDefault();
                alert('The search cannot be empty');
                input.focus();
            }
        });
    }
});

//移动导航
document.addEventListener('DOMContentLoaded', function () {
    var menuBtn = document.getElementById('menu_btn');
    var navList = document.getElementById('nav_list');header
    var header = document.getElementById('header');
    var body = document.body;
    var isOpen = false;
    if (menuBtn) {
        menuBtn.addEventListener('click', function () {
            isOpen = !isOpen;
            if (isOpen) {
                menuBtn.style.backgroundImage = "url('/static/home/<USER>/icons/cuo.png')";
                menuBtn.style.backgroundSize = '18px';
                body.classList.add('overflow-hidden', 'h-full');
                navList.classList.add('block','animate__fadeInRight');
                navList.classList.remove('animate__fadeOutRight');
                header.classList.add('bg-white');
                navList.classList.remove('hidden');

            } else {
                menuBtn.style.backgroundImage = "url('/static/home/<USER>/icons/menu.png')";
                menuBtn.style.backgroundSize = '26px';
                body.classList.remove('overflow-hidden', 'h-full');
                navList.classList.add('hidden');
                navList.classList.remove('block','animate__fadeInRight');
                navList.classList.add('animate__fadeOutRight');
                header.classList.remove('bg-white');
            }
        });
    }
});

// 提交按钮禁用
function checkAgree(event) {
    var agree = document.getElementById('agree');
    if (!agree.checked) {
        alert('Please check the "I agree" clause first and then submit');
        return false;
    }
    return true;
}

// 消息
function showMessageDrop() {
    if(window.innerWidth > 768) { // PC端
        document.getElementById('message_drop').style.display = 'block';
    }
}

function hideMessageDrop() {
    if(window.innerWidth > 768) { // PC端
        document.getElementById('message_drop').style.display = 'none';
    }
}

function toggleMessageDrop() {
    if(window.innerWidth <= 768) { // 移动端
        const drop = document.getElementById('message_drop');
        drop.style.display = drop.style.display === 'none' ? 'block' : 'none';
    }
}

// 鼠标经过登录
document.addEventListener("DOMContentLoaded", function () {
  const btn = document.querySelector(".btn-name");
  const drop = document.querySelector(".user-name-drop");

  // 检查元素是否存在
  if (!btn || !drop) {
    console.warn("登录按钮或下拉菜单元素未找到");
    return;
  }

  // 判断是否是移动端
  const isMobile = window.matchMedia("(hover: none) and (pointer: coarse)").matches;

  if (isMobile) {
    btn.addEventListener("click", function (e) {
      e.stopPropagation(); // 阻止冒泡，防止冒泡触发关闭
      drop.style.display = drop.style.display === "block" ? "none" : "block";
    });

    // 点击其他区域时关闭下拉
    document.addEventListener("click", function () {
      drop.style.display = "none";
    });
  }
});
