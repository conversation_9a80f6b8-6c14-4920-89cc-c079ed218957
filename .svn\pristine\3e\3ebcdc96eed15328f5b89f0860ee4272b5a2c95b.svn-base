
document.addEventListener('DOMContentLoaded', function () {
    const termsService = document.getElementById('terms_service');
    const countdownElement = document.getElementById('Countdown');
    const agreeButton = document.getElementById('btn_agree');
    const closeButton = document.querySelector('.close_terms_service');
    const agree_privacy = document.getElementById('agree');
    const privacyLink = document.getElementById('privacy_link');
    const btnDisagree = document.getElementById('btn_disagree');
    let seconds = 5;
    let countdown;
    let hasAgreed = localStorage.getItem('hasAgreed') === 'true';

    // 默认隐藏条款弹窗
    termsService.style.display = 'none';

    // 点击隐私协议链接时弹出条款弹窗
    privacyLink.addEventListener('click', function (e) {
        // 阻止链接的默认行为
        e.preventDefault();
        termsService.style.display = 'block';
        startCountdown(); // 点击隐私条款按钮后开始倒计时
    });

    // 直接点击checkbox时弹出条款弹窗，阻止直接勾选
    agree_privacy.addEventListener('click', function (e) {
        // 阻止checkbox的默认勾选行为
        e.preventDefault();
        // 如果还没有同意过，则弹出条款弹窗
        if (!hasAgreed) {
            termsService.style.display = 'block';
            startCountdown();
        }
    });

    function startCountdown() {
        if (hasAgreed) {
            agreeButton.textContent = 'Agreed';
            agreeButton.disabled = true;
            agreeButton.classList.remove('active');
            countdownElement.style.display = 'none';
            return;
        }

        seconds = 5;
        countdownElement.textContent = seconds;
        countdownElement.style.display = 'inline';
        agreeButton.disabled = true;
        agreeButton.classList.remove('active');
        agreeButton.textContent = 'Agree and Continue (' + seconds + 's)';

        countdown = setInterval(() => {
            seconds--;
            countdownElement.textContent = seconds;
            agreeButton.textContent = 'Agree and Continue (' + seconds + 's)';

            if (seconds <= 0) {
                clearInterval(countdown);
                countdownElement.style.display = 'none';
                agreeButton.disabled = false;
                agreeButton.classList.add('active');
                agreeButton.textContent = 'Agree and Continue';
            }
        }, 1000);
    }

    // 点击同意按钮时，勾选隐私协议checkbox
    agreeButton.addEventListener('click', function () {
        clearInterval(countdown);
        agreeButton.textContent = 'Agreed';
        agreeButton.disabled = true;
        agreeButton.classList.remove('active');
        hasAgreed = true;
        localStorage.setItem('hasAgreed', 'true');

        // 勾选隐私协议checkbox
        agree_privacy.checked = true;

        // 关闭弹窗
        termsService.style.display = 'none';
    });

    // 关闭弹窗时，只关闭弹窗，不取消勾选
    closeButton.addEventListener('click', function () {
        clearInterval(countdown);
        termsService.style.display = 'none';
        // 移除取消勾选的逻辑，只关闭弹窗
    });

    // 点击不同意按钮时，取消勾选并关闭弹窗
    btnDisagree.addEventListener('click', function () {
        clearInterval(countdown);
        termsService.style.display = 'none';

        // 取消勾选隐私协议checkbox
        agree_privacy.checked = false;
        // 重置同意状态
        hasAgreed = false;
        localStorage.removeItem('hasAgreed');
    });
});