<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <title>HitGen OpenDEL™</title>
    <!-- SEO 信息 -->
    <meta name="generator" content="HitGen OpenDEL™" />
    <meta name="keywords" content="HitGen OpenDEL™" />
    <meta name="description" content="HitGen OpenDEL™" />

    <link rel="shortcut icon" href="/favicon.ico" />
    <link href="__CSS__/style.css" rel="stylesheet" />
</head>
<style>
    ::-webkit-input-placeholder {
        color: #ddd;
    }

    ::-moz-placeholder {
        color: #ddd;
    }
</style>

<body>
    <section class="bg-white">
        {include file="public:header"}
    </section>

    <main class="w-full bg-[#fafbff] pb-10
    md:pb-20
    ">
        <div class="w-11/12 mx-auto flex gap-4 flex-col pt-4
        md:w-10/12
        ">
            <a href="" class="border border-[#e0eaff] rounded-lg block py-3 text-center bg-[url(__IMG__/icons/changjiantou-zuoshang1.png)] bg-no-repeat bg-[1.3rem_center] bg-size-[1.25rem] bg-white
                md:hidden
                ">
                Return
            </a>
            <div class="border border-[#e0eaff] rounded-xl overflow-hidden bg-white">
                <header class="flex items-center justify-between border-b border-[#e0eaff] bg-white px-5 py-3
                md:px-10 md:py-5
                ">
                    <h1 class="text-[#f08411] text-xl Roboto_Bold
                    md:text-3xl">
                        Quote
                    </h1>
                    <a href="javascript:history.back(-1);" class="border border-[#e0eaff] rounded-lg py-3 text-center bg-[url(__IMG__/icons/changjiantou-zuoshang1.png)] bg-no-repeat bg-[2rem_center] bg-size-[1.25rem] bg-white hidden Roboto_Bold
                    md:block md:text-xl md:p-4 md:bg-[1.5rem_center] md:bg-size-[1.25rem] md:w-[15.625rem]
                    ">
                        Return
                    </a>
                </header>
                <section class="p-5
                md:max-w-5xl md:mx-auto
                ">
                    <form action="" id="quoteForm" method="post" class="md:w-full md:pt-10 md:pb-20">
                        <div class="flex flex-col gap-y-4 text-sm md:text-xl md:gap-y-7">

                            <div class="input-item flex flex-col">
                                <label for="ConfirmPassword" class="Roboto_Bold">
                                    Interested products/service
                                </label>
                                <div class="flex flex-col gap-y-4 mt-2
                                md:flex-row md:gap-x-4 md:items-center
                                ">
                                    <div class="relative flex flex-col z-10
                                    md:flex-1
                                    ">
                                        <select name="product_id" id="product" class="country w-full h-[3.125rem] border border-[#d8e2ff] rounded-md bg-transparent bg-[url(__IMG__/icons/sanweimoxing.png)] bg-no-repeat bg-[.75rem_center] bg-size-[1.25rem] px-5 pl-12 pr-12 appearance-none text-[#ddd] cursor-pointer
                                            md:h-[3.75rem] md:pl-16 md:bg-size-[1.8rem] md:bg-[1.2rem_center]
                                            ">
                                            <option value="" disabled selected style="color:#ddd;"> products</option>
                                            {volist name="product" id="vo"}
                                            <option value="{$vo.id}" style="color: #333;" {if $product_id==$vo.id}selected{/if}>{$vo.name}</option>
                                            {/volist}
                                        </select>
                                        <button type="button"
                                            class="bg-[url(__IMG__/icons/mti-jiantouyou.png)] bg-no-repeat bg-[.75rem_center] bg-size-[1.25rem] rounded-md top-1/2 -translate-y-1/2 absolute right-2 w-10 h-full -z-[1]">
                                        </button>
                                    </div>
                                    <div class="text-center md:m-4">
                                        <img src="__IMG__/icons/lianjie.png" alt=""
                                            class="mx-auto w-[1.5rem] md:w-auto" />
                                    </div>
                                    <div class="relative flex flex-col z-10
                                    md:flex-1">
                                        <select name="service_id" id="service" class="country w-full h-[3.125rem] border border-[#d8e2ff] rounded-md bg-transparent bg-[url(__IMG__/icons/fuwu.png)] bg-no-repeat bg-[.75rem_center] bg-size-[1.25rem] px-5 pl-12 pr-12 appearance-none text-[#ddd] cursor-pointer
                                            md:h-[3.75rem] md:pl-16 md:bg-size-[1.8rem] md:bg-[1.2rem_center]
                                            ">
                                            <option value="" disabled selected style="color:#ddd;">service</option>
                                            {volist name="product_services" id="vo"}
                                            <option value="{$vo.id}" style="color: #333;" {if $service_id==$vo.id}selected{/if}>{$vo.name}</option>
                                            {/volist}
                                        </select>
                                        <button type="button"
                                            class="bg-[url(__IMG__/icons/mti-jiantouyou.png)] bg-no-repeat bg-[.75rem_center] bg-size-[1.25rem] rounded-md top-1/2 -translate-y-1/2 absolute right-2 w-10 h-full -z-[1]">
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <div class="input-item">
                                <label for="title" class="Roboto_Bold">
                                    message board:
                                </label>
                                <div class="relative flex flex-col mt-2">
                                    <textarea name="" id="" placeholder="Please leave a message..." class="w-full h-[7.75rem] border border-[#d8e2ff] rounded-md bg-white bg-[url(__IMG__/icons/jurassic_message.png)] bg-no-repeat bg-[.75rem_.75rem] bg-size-[1.25rem] py-[.75rem] pl-12 pr-6
                                        md:pl-16 md:bg-size-[1.8rem] md:bg-[.75rem_.75rem]
                                        " autocomplete="off"></textarea>
                                </div>
                            </div>

                            <div class="input-item mt-3 md:mt-10">
                                <label for="agree" class="flex md:items-center cursor-pointer">
                                    <input type="checkbox" name="agree" id="agree"
                                        class="mr-2 w-4 h-4 align-middle border border-[#d8e2ff]">
                                    <span class="text-[#111]  md:text-base">
                                        I consent to receiving emails and communications about OpenDEL products,
                                        services, and events.
                                    </span>
                                </label>
                                <div class=" hidden error text-[#ff0000] mt-2
                                md:text-base
                                ">
                                    Please fill in the information.
                                </div>
                            </div>
                            <div class="input-item
                            md:mt-5
                            ">
                                <button type="submit" class="bg-[#f08411] text-white rounded-xl text-base py-2 w-full cursor-pointer Roboto_Bold
                                    md:h-[3.75rem] md:inline-block md:w-[25rem]" id="submitBtn">Submit</button>
                            </div>
                        </div>
                    </form>
                </section>
            </div>
        </div>
    </main>

    {include file="public:footer"}

    {include file="public:foot"}

    <script>
        $(document).ready(function() {
            $('select.country').each(function() {
                // 监听 change 事件
                $(this).on('change', function() {
                    $(this).css('color', $(this).val() === "" ? "#ddd" : "#333");
                });

                // 初始化时设置颜色
                $(this).css('color', $(this).val() === "" ? "#ddd" : "#333");
            });

            $('#quoteForm').on('submit', function(e) {
                e.preventDefault();

                var valid = true;
                var firstInvalidInput = null;

                // 仅校验同意协议
                var $agree = $('#agree');
                var $agreeError = $agree.closest('.input-item').find('.error');
                if (!$agree.is(':checked')) {
                    $agreeError.removeClass('hidden');
                    if (!firstInvalidInput) firstInvalidInput = $agree[0];
                    valid = false;
                } else {
                    $agreeError.addClass('hidden');
                }

                if (!valid) {
                    if (firstInvalidInput) {
                        firstInvalidInput.focus();
                    }

                    return false;
                }

                // 禁用提交按钮
                var $submitBtn = $('#submitBtn');
                $submitBtn.prop('disabled', true).addClass('opacity-50 cursor-not-allowed');

                // 发送AJAX请求
                $.ajax({
                    url: '/quote/logged',
                    type: 'POST',
                    data: $(this).serialize(),
                    dataType: 'json',
                    success: function(data) {
                        if (data.code === 1) {
                            //提交成功
                            layer.msg(data.msg, { icon: 1, time: 2000 }, () => {
                                $('#quoteForm')[0].reset(); // 重置表单
                            });
                        } else {
                            //提交失败
                            layer.msg('Error: ' + data.msg, { icon: 2 });
                        }
                    },
                    error: function(xhr, status, error) {
                        layer.msg('An error occurred: ' + error, { icon: 2 });
                    },
                    complete: function() {
                        // 无论成功失败，都重新启用按钮
                        $submitBtn.prop('disabled', false).removeClass('opacity-50 cursor-not-allowed');
                    }
                });
            });

            // 监听 product 变化
            $('#product').on('change', function() {
                var productId = $(this).val();
                if (!productId) return; // 如果没有选择 product，直接返回

                // 发送 AJAX 请求获取 services
                $.ajax({
                    url: '/getServices/' + productId,
                    type: 'GET',
                    dataType: 'json',
                    success: function(response) {
                        if (response.code === 1) {
                            var $serviceSelect = $('#service');
                            $serviceSelect.empty(); // 清空现有选项

                            // 添加默认选项
                            $serviceSelect.append('<option value="" disabled selected>service</option>');

                            // 动态添加 service 选项
                            $.each(response.data, function(index, service) {
                                $serviceSelect.append(
                                    '<option value="' + service.id + '">' + service.name + '</option>'
                                );
                            });

                            // 更新 UI 颜色（如果之前有样式逻辑）
                            $serviceSelect.css('color', '#333');
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error("AJAX 请求失败:", error);
                        $('#service').html('<option value="">加载失败，请重试</option>');
                    }
                });
            });
        });
    </script>
</body>


</html>