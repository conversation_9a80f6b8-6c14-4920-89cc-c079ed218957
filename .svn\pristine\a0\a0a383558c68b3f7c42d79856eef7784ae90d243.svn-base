<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <title>Message - HitGen OpenDEL™</title>
    <link rel="shortcut icon" href="/favicon.ico" />
    <link href="__CSS__/vendors/aos.css" rel="stylesheet">
    <link href="__CSS__/style.css" rel="stylesheet" />
</head>

<body>
    <section class="bg-[url(__IMG__/backgrounds/m_bj.jpg)] bg-no-repeat pb-10 bg-size-[100%] md:bg-[url(__IMG__/backgrounds/pc_bj.jpg)] md:bg-size-[100%] md:pb-20">

        {include file="public:header"}

        <div class="w-11/12 mx-auto md:w-10/12">
            <div class="mt-5 Navigation mb-3.5 md:mb-14">
                <ul class="text-[#999] flex items-center text-md md:text-xl">
                    <li>
                        <a href="/" class="text-[#155797]">
                            Home
                        </a>
                    </li>
                    <li class="max-w-[12.5rem] line-clamp-1 md:max-w-2xs">
                        Personal Center
                    </li>
                </ul>
            </div>
            <!-- data-aos="fade-up" -->
            <div class="md:flex md:flex-row md:gap-x-4">

                {include file="user:left"}

                <div class="bg-white border border-[var(--border-color)] rounded-lg shadow-2xs
                md:w-2/3 md:rounded-2xl
                ">
                    {include file="user:iCommunity_top"}

                    <div class="user-profile">
                        <div class="profile-item">
                            <div class="py-4 md:pt-[1.875rem]">
                                <div class="message-list">
                                    <ul role="list">
                                        {volist name="conversations" id="vo"}
                                        <li class="px-4 md:px-[3.125rem] group">
                                            <div class="message-img relative">
                                                {if $vo.unread_count>0}
                                                <span class="absolute w-2 h-2 md:w-3 md:h-3 rounded-full bg-[#ff1919]"></span>
                                                {/if}
                                                <a href="/iCommunity/message?user={$vo.other_user.first_name}_{$vo.other_user.last_name}">
                                                    <img src="{$vo.other_user.avatar ?? '__IMG__/user-1.jpg'}" alt="" class="object-cover rounded-full w-full h-full">
                                                </a>
                                            </div>
                                            <div class="message-user flex gap-x-2 flex-1">
                                                <div class="flex flex-col flex-1">
                                                    <div class="message-info mb-2 text-sm md:text-xl text-[#999] flex items-center gap-x-2">
                                                        <time datetime="">{:date('F j, Y, g:i A', strtotime($vo['update_time'] ?? ''))}</time>
                                                        <!-- 删除按钮 -->
                                                         <button type="button" class="delete-message-btn cursor-pointer block md:hidden md:group-hover:block">
                                                            <svg t="1752648994860" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="5864" width="20" height="20"><path d="M512 512m-512 0a512 512 0 1 0 1024 0 512 512 0 1 0-1024 0Z" fill="#F5222D" p-id="5865"></path><path d="M471.338667 666.709333V423.594667a25.642667 25.642667 0 0 0-51.285334 0v243.114666a25.642667 25.642667 0 0 0 51.285334 0z m239.658666-268.16a25.642667 25.642667 0 0 0-25.6 25.6v330.965334c0 10.24-8.362667 18.56-18.602666 18.56H357.333333a18.56 18.56 0 0 1-18.56-18.56V423.253333a25.685333 25.685333 0 0 0-51.285333 0v331.861334c0 38.528 31.317333 69.845333 69.845333 69.845333h309.461334c38.528 0 69.845333-31.317333 69.845333-69.845333V424.149333c0-14.122667-11.52-25.6-25.6-25.6z m-107.050666 268.16V423.594667a25.642667 25.642667 0 0 0-51.285334 0v243.114666a25.642667 25.642667 0 0 0 51.285334 0z m195.413333-357.12h-106.965333v-40.704c0-38.528-31.146667-69.845333-69.376-69.845333H401.493333c-38.528 0-69.845333 31.317333-69.845333 69.845333v40.661334H224.682667a25.642667 25.642667 0 0 0 0 51.285333h574.677333a25.685333 25.685333 0 0 0 0-51.285333z m-158.293333 0h-258.133334v-40.704c0-10.24 8.32-18.602667 18.56-18.602666h221.525334c10.112 0 18.048 8.149333 18.048 18.602666v40.661334z" fill="#FFFFFF" p-id="5866"></path></svg>
                                                        </button>
                                                    </div>
                                                     <div class="message-content text-wrap text-sm md:text-xl break-all">
                                                         <a href="/iCommunity/message?user={$vo.other_user.first_name}_{$vo.other_user.last_name}" class="wrap-anywhere text-[#666666] line-clamp-2">
                                                            {$vo['last_message']['content'] ?? ''}
                                                         </a>
                                                     </div>
                                                </div>
                                            </div>
                                        </li>
                                        {/volist}
                                    </ul>
                                    <div class="text-sm p-3 md:py-[40px] md:px-[57px] md:text-xl">
                                        <button type="button"
                                            class="show-more-activity-btn rounded-md bg-[#155797] text-white py-3.5 w-full cursor-pointer md:h-[80px]">
                                            SHOW MORE ACTIVITY
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>

        </div>
    </section>

    {include file="public:footer"}

    {include file="public:foot"}

    <script>
        //显示更多
        setupShowMoreActivity(
            '.message-list',   // 外层容器
            'ul[role="list"]',            // ul选择器
            '.show-more-activity-btn',                     // 按钮选择器
            4                             // 默认显示4个
        );
        // 删除私信
        $('.delete-message-btn').on('click', function () {
            layer.confirm('Are you sure you want to delete it?',{title:'Information',btn: ['Yes', 'No']}, function (index) {
                layer.close(index);
                $(this).closest('li').remove();
                let count = $('.message-list li').length;
                if (count == 0) {
                    $('.message-list').append('<p class="text-center text-[#666]">No messages</p>');
                }
            }.bind(this));


        });
    </script>

</body>

</html>