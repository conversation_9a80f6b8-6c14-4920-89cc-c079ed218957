<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <title>FAQ - HitGen OpenDEL™</title>
    <link rel="shortcut icon" href="/favicon.ico" />
    <link href="__CSS__/vendors/aos.css" rel="stylesheet">
    <link href="__CSS__/style.css" rel="stylesheet" />
</head>

<body>
    <section class="bg-[url(__IMG__/backgrounds/m_bj.jpg)] bg-no-repeat pb-10 bg-size-[100%_100%] md:bg-[url(__IMG__/backgrounds/pc_bj.jpg)] md:bg-size-[100%_100%] md:pb-20">

        {include file="public:header"}

        <div class="w-11/12 mx-auto md:w-10/12">
            <div class="mt-5 Navigation mb-7 md:mb-14">
                <ul class="text-[#999] flex items-center text-md md:text-xl">
                    <li>
                        <a href="/" class="text-[#155797]">
                            Home
                        </a>
                    </li>
                    <li class="max-w-[12.5rem] line-clamp-1 md:max-w-2xs">
                        FAQ
                    </li>
                </ul>
            </div>
            <!-- data-aos="fade-up" -->
            <div class="flex flex-col gap-5" data-aos="fade-up">
                <div class="">
                    <h1 class="text-xl Roboto_Bold mb-2 md:text-4xl md:mb-5">
                        FAQ
                    </h1>
                    <div class="h-[12.5rem] bg-[url(__IMG__/faq/bg_m.jpg)] bg-no-repeat bg-cover rounded-tl-xl rounded-tr-xl shadow-lg
                        md:h-[31.25rem] md:bg-[url(__IMG__/faq/bg_pc.jpg)">
                        <div class="h-full w-10/12 mx-auto flex flex-col items-center justify-center">
                            <div class="text-white text-2xl mb-5
                            md:text-5xl md:mb-10
                            ">
                                Find the answers you want anytime, anywhere.
                            </div>
                            <form action="/faq/result" method="get" class="relative w-full">
                                <div class="relative w-full">
                                    <input name="q" type="text" placeholder="Search frequently asked questions" class="bg-white rounded-lg h-[3.125rem] w-full py-2 pl-3 pr-10
                                        md:h-[5.625rem] md:text-xl md:pl-[2.5rem] md:pr-[6.25rem]" />
                                    <button type="submit" class="absolute right-[.425rem] top-1/2 -translate-y-1/2 w-8 h-8 bg-[url(__IMG__/icons/sousuo.png)] bg-no-repeat bg-center bg-size-[1rem] md:bg-auto cursor-pointer
                                        md:w-10 md:h-10 md:right-10" aria-label="搜索"> </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <main class="w-full">
        <div class="w-11/12 mx-auto py-6 md:w-10/12 md:py-20" data-aos="fade-up">
            <div class="mb-5 md:mb-10">
                <div class="w-full overflow-x-auto mb-5">
                    <div class="flex flex-nowrap min-w-max gap-x-4 pb-2 Roboto_Bold md:text-3xl">
                        {volist name="category" id="vo" key="k"}
                        <div data-tab="{$k}" class="item-btn px-10 py-3 text-[#999] bg-white rounded-lg border border-[#e0eaff] whitespace-nowrap hover:bg-[#155290] transition-colors hover:text-white cursor-pointer md:px-15 md:py-6">
                            {$vo.name}
                        </div>
                        {/volist}
                    </div>
                </div>

                <div class="flex flex-col gap-y-4">
                    {volist name="child_category" id="category" key="t"}
                    <div class="item-content flex flex-col gap-y-4" data-tab="{$t}">
                        {volist name="category" id="vo" }
                        <div  class="bg-[#f8fdff] border border-[#e0eaff] rounded-xl p-4 md:p-10">
                            <div class="border-b border-[#bdcbe9] mb-3 pb-4 pt-1 px-2 md:pb-10 md:pt-5">
                                <h3 class="text-xl Roboto_Bold md:text-3xl">
                                    {$vo.name} (<span class="text-[#155797]">{$vo.faq|count}</span>)
                                </h3>
                            </div>
                            <div class="px-2 pb-4 md:text-3xl md:p-0 md:pt-7">
                                <ul class="flex flex-col gap-y-2 mb-2 faq-list md:gap-y-5 md:mb-5">
                                    {volist name="vo.faq" id="v"}
                                    <li class="underline underline-offset-4 md:underline-offset-8">
                                        <a href="/faq/{$v.seo_url}" class="text-[#155797]">
                                            <span>{$v.question}</span>
                                            <img src="__IMG__/icons/changjiantou-r.png" class="w-5 inline-block ml-2.5 md:w-auto md:ml-3.5" alt="" />
                                        </a>
                                    </li>
                                    {/volist}
                                </ul>
                                <!-- 超过4条才会显示,默认不显示 -->
                                <div class=" underline show-more md:mt-10 hidden">
                                    <span class="cursor-pointer">Show More</span>
                                </div>
                            </div>
                        </div>
                        {/volist}
                    </div>
                    {/volist}
                </div>
            </div>

            <div class=" cursor-pointer text-[#111] border border-[#155797] rounded-xl h-[3.125rem] bg-white flex
                                                            justify-center items-center gap-x-3 text-lg md:h-[6.25rem]
                                                            md:text-2xl " id="ask_here">
                <img src="__IMG__/faq/add.png" class="w-4 md:w-auto md:mr-3.5" alt="" />
                <span>Need help? Ask here</span>
            </div>
        </div>
    </main>

    {include file="public:footer"}

    <!-- 添加咨询弹窗 -->
    <div id="ask_modal" class="modal-container fixed top-0 left-0 w-full h-full bg-[rgba(21,87,151,.5)] z-50 hidden">
        <form id="questionForm">
            <section class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white rounded-xl overflow-hidden min-w-[90%] flex flex-col justify-center items-center py-10 px-5
            md:min-w-[43.75rem] md:min-h-[28.125rem]
            md:px-10
            ">
                    <div class="mb-6">
                        <h3 class="text-2xl Roboto_bold md:text-3xl">
                            Submit new questions
                        </h3>
                    </div>
                    <div class="mb-7 flex flex-col gap-y-4 w-full">
                        <textarea name="question" id="ask_textarea" placeholder="Please leave a message..." class="w-full h-[7.75rem] border border-[#d8e2ff] rounded-md bg-white bg-[url(__IMG__/icons/wenti.png)] bg-no-repeat bg-[.75rem_.75rem] bg-size-[1.25rem] py-[.75rem] pl-10 pr-6
                        md:pl-16 md:bg-auto md:bg-[.75rem_.75rem]
                        " autocomplete="off"></textarea>
                        <p class="text-red-500 hidden error">
                            Information cannot be empty
                        </p>
                    </div>
                    <button type="submit" class="bg-[#f08411] text-[#fff] text-lg py-3 w-full rounded-md cursor-pointer"
                        id="ask_submit">
                        Submit
                    </button>
                <div class="close_btn absolute w-8 h-8 rounded-full top-2 right-3 cursor-pointer bg-[url(__IMG__/icons/close.png)] bg-no-repeat bg-cover"  data-close-modal></div>
            </section>
        </form>
    </div>

    {include file="public:foot"}
    <script src="__JS__/TabSwitch.js"></script>

    <script>
        //添加问题
        var $askHere = $('#ask_here');
        var $askModal = $('#ask_modal');
        if ($askHere.length && $askModal.length) {
            $askHere.on('click', function() {
                $askModal.removeClass('hidden').addClass('block');
            });
        }

        // 提交问题
        $('#questionForm').on('submit', function(e) {
            //阻止表单提交
            e.preventDefault();

            var $textarea = $('#ask_textarea');
            var $error = $textarea.parent().find('.error');

            if ($.trim($textarea.val()) === '') {
                if ($error.length) $error.show();
                return false
            } else {
                if ($error.length) $error.hide();
            }

            var $submitBtn = $('#ask_submit');
            $submitBtn.prop('disabled', true).addClass('opacity-50 cursor-not-allowed');

            // 发送AJAX请求
            $.ajax({
                url: '/ask-question',
                type: 'POST',
                data: $(this).serialize(),
                dataType: 'json',
                success: function(data) {
                    if (data.code === 1) {
                        //提交成功
                        layer.msg(data.msg, { icon: 1, time: 2000 }, () => {
                            $('#questionForm')[0].reset(); // 重置表单
                            $askModal.removeClass('block').addClass('hidden');
                        });
                    } else {
                        //提交失败
                        layer.msg('Error: ' + data.msg, { icon: 2 });
                    }
                },
                error: function(xhr, status, error) {
                    layer.msg('An error occurred: ' + error, { icon: 2 });
                },
                complete: function() {
                    // 无论成功失败，都重新启用按钮
                    $submitBtn.prop('disabled', false).removeClass('opacity-50 cursor-not-allowed');
                }
            });
        });
    </script>

    <script>
        //展开更多
        document.addEventListener('DOMContentLoaded', function () {
            const faqLists = document.querySelectorAll('.faq-list');
            faqLists.forEach(list => {
                const items = list.querySelectorAll('li');
                if (items.length > 4) {
                    for (let i = 4; i < items.length; i++) {
                        items[i].style.display = 'none';
                    }
                    const showMore = list.parentElement.querySelector('.show-more');
                    if (showMore) {
                        showMore.classList.remove('hidden');
                        showMore.classList.add('block');
                        showMore.addEventListener('click', function () {
                            items.forEach(item => {
                                item.style.display = 'list-item';
                            });
                            showMore.style.display = 'none';
                        });
                    }
                }
            });
        });

        initTabSwitch('.item-btn', '.item-content');

    </script>

</body>

</html>