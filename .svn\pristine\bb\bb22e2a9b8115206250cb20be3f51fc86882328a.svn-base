<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <title>Tag-Search - HitGen OpenDEL™</title>
    <link rel="shortcut icon" href="/favicon.ico" />
    <link href="__CSS__/vendors/aos.css" rel="stylesheet">
    <link href="__CSS__/style.css" rel="stylesheet" />
</head>

<body>
    <section class="bg-[url(__IMG__/backgrounds/m_bj.jpg)] bg-no-repeat pb-5 bg-size-[100%] md:bg-[url(__IMG__/backgrounds/pc_bj.jpg)] md:bg-size-[100%] md:pb-8">

        {include file="public:header"}

        <div class="w-11/12 mx-auto md:w-10/12">
            <div class="mt-5 Navigation mb-3.5 md:mb-14">
                <ul class="text-[#999] flex items-center text-md md:text-xl">
                    <li>
                        <a href="/" class="text-[#155797]">
                            Home
                        </a>
                    </li>
                    <li class="max-w-[12.5rem] line-clamp-1 md:max-w-2xs">
                        Search
                    </li>
                </ul>
            </div>
            <!-- data-aos="fade-up" -->
            <div class="contact" data-aos="fade-up">
                <form action="/iCommunity/tag" method="get" class="relative w-full">
                    <div class="browsing-search-box relative">
                        <input type="text" name="q" value="{$keyword}" placeholder="Search Opendel resources" class="w-full bg-[#f8fdff] h-10 rounded-md border border-[#e0eaff] p-2 pl-4 text-xs pr-10 md:w-full md:h-[5rem] md:text-xl md:pr-[5.625rem] md:rounded-xl md:pl-[2.5rem]">

                        <button type="button" class="w-10 h-10 bg-[url(__IMG__/icons/sousuo.png)] bg-no-repeat bg-center bg-size-[1rem] rounded-full cursor-pointer absolute right-0 top-1/2 -translate-y-1/2 md:w-[5rem] md:h-[5rem] md:bg-auto">
                            <span class="sr-only">Search</span>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </section>

    <main class="w-full">
        <div class="bg-[#f8fdff] pb-7">
            <div class="w-11/12 mx-auto py-6 md:w-10/12 md:pb-20  z-50" data-aos="fade-up"
                data-aos-delay="100">
                <div class="flex flex-col gap-y-5 md:flex-row md:gap-x-14 md:gap-y-0">
                    <div class="tag-search-left mb-5 md:w-4/12 md:mb-0">
                        <h1 class="text-xl Roboto_Bold mb-5 md:text-2xl">
                            Tag
                        </h1>
                        <div class="tag-list">
                            <ul class="flex flex-wrap gap-2.5 md:gap-3">
                                {volist name="tags" id="vo"}
                                <li class="tag-btn"  data-tag="{$vo.name}">
                                    {$vo.name} <span>({$vo.count})</span>
                                </li>
                                {/volist}
                            </ul>
                        </div>
                    </div>
                    <div class="tag-search-right md:w-8/12">
                        <div class="tag-search-right-header mb-5 md:mb-10">
                            <ul id="selected-tags-container" class="flex gap-x-2.5 flex-wrap">
                                <!-- 动态显示选中的标签 -->
                            </ul>
                        </div>

                        <div class="search-results">
                            <h2 id="posts-count" class="text-xl font-bold mb-5">{$all_post|count|number_format} Posts</h2>
                            <div class="bg-white border border-[#c4d7ff] rounded-md">
                                <div data-tab="discussion" class="iCommunity-content-item">
                                    <ul class="grid grid-cols-1 mb-2" role="list">
                                        {volist name="all_post" id="vo"}
                                        <li data-tags="{$vo.tags}">
                                            <div class="iCommunity-left w-[2.5rem] h-[2.5rem] rounded-md flex-shrink-0 relative md:w-[5rem] md:h-[5rem] border border-[#dae9ff]">
                                                <div class="w-full h-full cursor-pointer">
                                                    <img src="{$vo.user.avatar ?? '__IMG__/user-1.jpg'}" alt="" class="w-full h-full rounded-md object-cover">
                                                </div>
                                                <div class="iCommunity-left-info absolute left-0 top-full z-10" style="display: none;">
                                                    <div class="bg-[#fafbff] rounded-xl" style="box-shadow: 0 0 1.25rem -0.125rem rgba(84, 111, 138, 0.3);">
                                                        <div class="flex items-start gap-x-2 mb-3 md:mb-0 md:gap-x-8 p-4 md:p-10">
                                                            <div class="w-[2.5rem] h-[2.5rem] md:w-[8.125rem] md:h-[8.125rem] flex-shrink-0">
                                                                <img src="{$vo.user.avatar ?? '__IMG__/user-1.jpg'}" alt="" class="w-full h-full rounded-md object-cover">
                                                            </div>
                                                            <!-- 右侧个人信息 -->
                                                            <div class="flex flex-col gap-y-2 md:flex-1">
                                                                <!-- 名称、经验、私信 -->
                                                                <div class="flex w-full gap-x-2.5 md:gap-x-8 items-end">
                                                                    <div class="name-info">
                                                                        <a href="/iCommunity/user/{$vo.user.id}" class="text-base md:text-3xl Roboto_Bold ">{$vo.user.first_name} {$vo.user.last_name}</a>
                                                                        <p class="text-xs text-nowrap md:text-xl text-[#999]">
                                                                            {$vo.user.role_name}
                                                                        </p>
                                                                    </div>
                                                                    <!-- 私信 -->
                                                                    {if $vo.user.user.id != session('userId')}
                                                                    <div class="message-btn">
                                                                        <a href="/iCommunity/message?user={$vo.user.first_name}_{$vo.user.last_name}" class="text-sm px-2 py-1 md:text-xl bg-[#155797] text-white text-nowrap rounded-md md:px-4 md:py-2 ">
                                                                            Private message
                                                                        </a>
                                                                    </div>
                                                                    {/if}
                                                                </div>
                                                                <!-- 发帖数量等 -->
                                                                <div class="mt-3 px-2 md:px-0 md:mt-5">
                                                                    <ul class="text-sm justify-between md:text-2xl flex items-center gap-x-5 md:gap-x-10 text-center md:justify-start">
                                                                        <li>
                                                                            <span>{$vo.user.question_count}</span>
                                                                            <p class="text-[#155797]">Questions</p>
                                                                        </li>
                                                                        <li>
                                                                            <span>{$vo.user.posting_count}</span>
                                                                            <p class="text-[#155797]">Posts</p>
                                                                        </li>
                                                                        <li>
                                                                            <span>{$vo.user.reply_count}</span>
                                                                            <p class="text-[#155797]">Reply</p>
                                                                        </li>
                                                                    </ul>
                                                                </div>
                                                            </div>
                                                        </div>

                                                        <div class="badge-about">
                                                            <!-- 徽章-关于 tab按钮 -->
                                                            <div class="flex items-center gap-x-10 border-b border-[#dae9ff] px-10">
                                                                <div class="badge-about-btn active">
                                                                    Badge
                                                                </div>
                                                                <div class="badge-about-btn">
                                                                    About
                                                                </div>
                                                            </div>
                                                            <!-- tab详情 -->
                                                            <div class="tab-content p-5 md:p-10">
                                                                <div class="tab-content-item flex gap-x-10">
                                                                    <!-- 没有徽章的时候显示 -->
                                                                    <div class="tab-content-item-no-badge text-sm md:text-2xl" style="display: {$vo.user.role_id == 1 || $vo.user.role_id == 2 || $vo.user.role_id == 3 ? 'block' : 'none'};">
                                                                        {$vo.user.first_name} {$vo.user.last_name} did not receive any badges yet.
                                                                    </div>
                                                                    <!-- 有徽章的时候 -->
                                                                    <div class="tab-content-item-badge" style="display: {$vo.user.role_id == 4 || $vo.user.role_id == 5 || $vo.user.role_id == 6 ? 'block' : 'none'};">
                                                                        <div class="flex items-center gap-x-2.5 md:gap-x-10 badge-item ">
                                                                            <div class=" flex flex-col gap-y-2 items-center">
                                                                                <div class="w-[2.5rem] md:w-[4.6875rem] md:h-[5.0625rem] flex-shrink-0 bg-white">
                                                                                    <img src="__IMG__/iCommunity/icon_1.png" alt="" class="w-full h-full object-cover">
                                                                                </div>

                                                                                <div class="text-sm md:text-xl text-[#999]">
                                                                                    <p>Junior Badge</p>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <div class="tab-content-item-badge" style="display: {$vo.user.role_id == 5 || $vo.user.role_id == 6 ? 'block' : 'none'};">
                                                                        <div class="flex items-center gap-x-2.5 md:gap-x-10 badge-item">
                                                                            <div class=" flex flex-col gap-y-2 items-center">
                                                                                <div class="w-[2.5rem] md:w-[4.6875rem] md:h-[5.0625rem] flex-shrink-0 bg-white">
                                                                                    <img src="__IMG__/iCommunity/icon_2.png" alt="" class="w-full h-full object-cover">
                                                                                </div>

                                                                                <div class="text-sm md:text-xl text-[#999]">
                                                                                    <p>Intermediate Badge</p>
                                                                                </div>
                                                                            </div>
                                                                        </div>

                                                                    </div>
                                                                    <div class="tab-content-item-badge" style="display: {$vo.user.role_id == 6 ? 'block' : 'none'};">
                                                                        <div class="flex items-center gap-x-2.5 md:gap-x-10 badge-item">
                                                                            <div class=" flex flex-col gap-y-2 items-center">
                                                                                <div class="w-[2.5rem] md:w-[4.6875rem] md:h-[5.0625rem] flex-shrink-0 bg-white">
                                                                                    <img src="__IMG__/iCommunity/icon_3.png" alt="" class="w-full h-full object-cover">
                                                                                </div>

                                                                                <div class="text-sm md:text-xl text-[#999]">
                                                                                    <p>Senior Badge</p>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>

                                                                <div class="tab-content-item flex flex-col gap-x-2.5 md:gap-x-10 gap-y-3 md:gap-y-5 text-sm md:text-xl" style="display: none;">
                                                                    <div  class="about-item flex flex-col md:flex-row md:items-center justify-between gap-y-2 md:gap-y-0 md:gap-x-10">
                                                                        <div class="about-item-left flex-1/2">
                                                                            Registration Date
                                                                        </div>
                                                                        <div class="about-item-right Roboto_Bold flex-1/2">
                                                                            <p>{:date('j F Y', strtotime($vo.user.create_time))}</p>
                                                                        </div>
                                                                    </div>
                                                                    <div class="about-item flex flex-col md:flex-row md:items-center justify-between gap-y-2 md:gap-y-0 md:gap-x-10">
                                                                        <div class="about-item-left flex-1/2">
                                                                            Organization/Institution/Corporation
                                                                        </div>
                                                                        <div class="about-item-right Roboto_Bold flex-1/2">
                                                                            <p>{$vo.user.organization}</p>
                                                                        </div>
                                                                    </div>
                                                                    <div class="about-item flex flex-col md:flex-row md:items-center justify-between gap-y-2 md:gap-y-0 md:gap-x-10">
                                                                        <div class="about-item-left flex-1/2">
                                                                            Title
                                                                        </div>
                                                                        <div class="about-item-right Roboto_Bold flex-1/2">
                                                                            <p>{$vo.user.title}</p>
                                                                        </div>
                                                                    </div>
                                                                    <div class="about-item flex flex-col md:flex-row md:items-center justify-between gap-y-2 md:gap-y-0 md:gap-x-10">
                                                                        <div class="about-item-left flex-1/2">
                                                                            Location (City, Country, Earth)
                                                                        </div>
                                                                        <div class="about-item-right Roboto_Bold flex-1/2">
                                                                            <p>{$vo.user.country}</p>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>

                                                    </div>
                                                </div>
                                            </div>
                                            <div class="iCommunity-right md:pt-5">
                                                <div class="iCommunity-right-info flex md:flex-row flex-col gap-x-2 mb-2 text-sm md:gap-x-3.5 md:text-xl md:mb-5">
                                                    <div class="iCommunity-right-title-name">
                                                        <a href="/iCommunity/user/{$vo.user.id}" class="text-[#155797]">{$vo.user.first_name} {$vo.user.last_name}</a>
                                                    </div>
                                                    <div class="iCommunity-right-title-time text-[#999]">
                                                        {$vo.user.role_name} | Published in
                                                        <a href="/iCommunity/topic/{$vo.topic}" class="underline text-[#999]">{$vo.topic}</a>
                                                    </div>
                                                </div>
                                                <div class="iCommunity-right-content mb-2 md:mb-5">
                                                    <div class="iCommunity-right-content-title mb-2">
                                                        <a href="/iCommunity/{$vo.item_url}/{$vo.id}" class="text-base Roboto_Bold line-clamp-1 text-[#999] md:text-2xl">
                                                            {$vo.title}
                                                        </a>
                                                    </div>
                                                    <div class="iCommunity-right-content-info text-sm line-clamp-3 md:text-xl">
                                                        {$vo.content|strip_tags}
                                                    </div>
                                                </div>
                                                <div class="iCommunity-right-time text-[#999] text-sm flex items-center justify-between md:justify-start md:gap-x-5 md:text-xl">
                                                    <div class="iCommunity-right-time-left">
                                                        {:date('F j, Y, g:i A', strtotime($vo.create_time))}
                                                    </div>
                                                    <div class="iCommunity-right-time-right">
                                                        <a href="/iCommunity/{$vo.item_url}/{$vo.id}" class="text-[#999] flex items-center gap-x-1 md:gap-x-2">
                                                            <img src="__IMG__/icons/pinglun.png" alt="" class="w-[1rem] md:w-[1.5rem]">
                                                            <span>{$vo.reply_count}</span>
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                        </li>
                                        {/volist}
                                    </ul>
                                    <div class="text-sm p-3 md:py-[2.5rem] md:px-[3.5625rem] md:text-xl">
                                        <button type="button"
                                            class="show-more-activity-btn rounded-md bg-[#155797] text-white py-3.5 w-full cursor-pointer md:h-[5rem]">
                                            SHOW MORE ACTIVITY
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>

            </div>
        </div>
    </main>

    {include file="public:footer"}

    {include file="public:foot"}

    <script>
        $('.iCommunity-left').hover(function () {
            $(this).find('.iCommunity-left-info').show();
        }, function () {
            $(this).find('.iCommunity-left-info').hide();
        });

        $('.badge-about').each(function () {
            var $badgeAbout = $(this);
            $badgeAbout.find('.badge-about-btn').each(function (index) {
                $(this).click(function () {
                    // 当前tab高亮，兄弟去除高亮
                    $(this).addClass('active').siblings().removeClass('active');
                    // 只切换当前区块下的tab内容
                    $badgeAbout.find('.tab-content-item').eq(index).show().siblings('.tab-content-item').hide();
                });
            });
        });

        //显示更多
        setupShowMoreActivity(
            '.iCommunity-content-item',   // 外层容器
            'ul[role="list"]',            // ul选择器
            '.show-more-activity-btn',                     // 按钮选择器
            4                             // 默认显示4个
        );
    </script>
    <script>
        // 获取URL中的标签参数
        function getSelectedTagsFromURL() {
            const urlParams = new URLSearchParams(window.location.search);
            const tags = urlParams.get('tags');
            return tags ? tags.split(',') : [];
        }

        // 更新URL参数
        function updateURL(selectedTags) {
            const url = new URL(window.location);
            if (selectedTags.length > 0) {
                url.searchParams.set('tags', selectedTags.join(','));
            } else {
                url.searchParams.delete('tags');
            }
            window.history.replaceState({}, '', url);
        }

        // 更新选中标签显示
        function updateSelectedTagsDisplay() {
            const container = document.getElementById('selected-tags-container');
            container.innerHTML = '';

            selectedTags.forEach(tag => {
                const li = document.createElement('li');
                li.className = 'selected-tag-item';
                li.innerHTML = `
                    <span>${tag}</span>
                    <div class="selected-tag-remove" data-tag="${tag}">
                        <svg width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </div>
                `;
                container.appendChild(li);
            });

            // 为取消按钮添加事件监听
            container.querySelectorAll('.selected-tag-remove').forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.stopPropagation();
                    const tagToRemove = this.dataset.tag;
                    removeTag(tagToRemove);
                });
            });
        }

        // 移除标签
        function removeTag(tagToRemove) {
            selectedTags = selectedTags.filter(tag => tag !== tagToRemove);

            // 更新左侧标签按钮状态
            const btn = document.querySelector(`[data-tag="${tagToRemove}"]`);
            if (btn) btn.classList.remove('active');

            // 更新URL和显示
            updateURL(selectedTags);
            updateSelectedTagsDisplay();
            filterContent();
        }

        // 存储原始的帖子总数
        let originalPostsCount = null;

        // 更新帖子数量显示
        function updatePostsCount() {
            const postsCountElement = document.getElementById('posts-count');
            if (!postsCountElement) return;

            // 如果还没有存储原始数量，先存储
            if (originalPostsCount === null) {
                const originalText = postsCountElement.textContent;
                const match = originalText.match(/^([\d,]+)/);
                if (match) {
                    originalPostsCount = parseInt(match[1].replace(/,/g, ''));
                }
            }

            if (selectedTags.length === 0) {
                // 没有筛选时恢复原始数量显示
                if (originalPostsCount !== null) {
                    postsCountElement.textContent = originalPostsCount.toLocaleString() + ' Posts';
                }
            } else {
                // 有筛选时计算符合条件的数量
                const listItems = document.querySelectorAll('ul[role="list"] li');
                let visibleCount = 0;

                listItems.forEach(item => {
                    if (!item.hasAttribute('data-filter-hidden')) {
                        visibleCount++;
                    }
                });

                postsCountElement.textContent = visibleCount.toLocaleString() + ' Posts';
            }
        }

        // 筛选内容
        function filterContent() {
            if (selectedTags.length === 0) {
                // URL没有参数时，重置所有筛选状态，让原始的setupShowMoreActivity生效
                const listItems = document.querySelectorAll('ul[role="list"] li');
                listItems.forEach(item => {
                    item.removeAttribute('data-filter-hidden');
                    item.style.display = ''; // 清除内联样式，让原始逻辑控制
                });

                // 重新执行原始的setupShowMoreActivity
                setupShowMoreActivity(
                    '.iCommunity-content-item',
                    'ul[role="list"]',
                    '.show-more-activity-btn',
                    4
                );
            } else {
                // 有筛选条件时，执行筛选程序
                const listItems = document.querySelectorAll('ul[role="list"] li');

                // 首先重置所有项目的显示状态
                listItems.forEach(item => {
                    item.removeAttribute('data-filter-hidden');
                });

                // 应用标签筛选
                listItems.forEach(item => {
                    const itemTagsStr = item.dataset.tags || '';
                    const itemTags = itemTagsStr.trim() ? itemTagsStr.split(',') : [];

                    let shouldShow = false;

                    // 有选中标签时，只有包含标签的内容才参与筛选
                    if (itemTags.length === 0 || itemTagsStr.trim() === '') {
                        // 如果该项没有标签，在筛选时隐藏
                        shouldShow = false;
                    } else {
                        // 检查是否包含任一选中的标签
                        shouldShow = selectedTags.some(tag => itemTags.includes(tag.trim()));
                    }

                    if (!shouldShow) {
                        item.setAttribute('data-filter-hidden', 'true');
                        item.style.display = 'none';
                    } else {
                        item.style.display = 'flex';
                    }
                });

                // 在筛选结果中应用数量限制
                reapplyShowMoreActivity();
                rebindShowMoreButton();
            }

            // 更新帖子数量显示
            updatePostsCount();
        }

        // 重新应用显示更多功能
        function reapplyShowMoreActivity() {
            const container = document.querySelector('.iCommunity-content-item');
            const ul = container.querySelector('ul[role="list"]');
            const button = container.querySelector('.show-more-activity-btn');
            const defaultShowCount = 5; // 与原设置保持一致

            // 获取所有未被筛选隐藏的项目
            const visibleItems = Array.from(ul.querySelectorAll('li')).filter(item =>
                !item.hasAttribute('data-filter-hidden')
            );

            // 重置所有项目的显示状态
            visibleItems.forEach((item, index) => {
                if (index < defaultShowCount) {
                    item.style.display = 'flex';
                } else {
                    item.style.display = 'none';
                }
            });

            // 控制按钮显示
            if (visibleItems.length > defaultShowCount) {
                button.style.display = 'block';
            } else {
                button.style.display = 'none';
            }
        }

        let selectedTags = getSelectedTagsFromURL(); // 从URL获取初始状态

        // 标签点击事件
        document.querySelectorAll('.tag-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const tag = this.dataset.tag;

                // 切换标签选中状态
                if (selectedTags.includes(tag)) {
                    selectedTags = selectedTags.filter(t => t !== tag);
                    this.classList.remove('active');
                } else {
                    selectedTags.push(tag);
                    this.classList.add('active');
                }

                // 更新URL、显示和筛选结果
                updateURL(selectedTags);
                updateSelectedTagsDisplay();
                filterContent();
            });
        });

        // 重新绑定显示更多按钮事件
        function rebindShowMoreButton() {
            const button = document.querySelector('.show-more-activity-btn');
            if (button) {
                // 移除旧的事件监听器（通过克隆节点）
                const newButton = button.cloneNode(true);
                button.parentNode.replaceChild(newButton, button);

                // 添加新的事件监听器
                newButton.addEventListener('click', function() {
                    const ul = document.querySelector('ul[role="list"]');
                    const visibleItems = Array.from(ul.querySelectorAll('li')).filter(item =>
                        !item.hasAttribute('data-filter-hidden')
                    );

                    // 显示所有符合筛选条件的项目
                    visibleItems.forEach(item => {
                        item.style.display = 'flex';
                    });

                    // 隐藏按钮
                    this.style.display = 'none';
                });
            }
        }

        // 页面加载时初始化状态
        document.addEventListener('DOMContentLoaded', function() {
            // 先初始化原始数量存储
            updatePostsCount();

            // 根据URL参数设置初始选中状态
            selectedTags.forEach(tag => {
                const btn = document.querySelector(`[data-tag="${tag}"]`);
                if (btn) btn.classList.add('active');
            });

            // 更新选中标签显示
            updateSelectedTagsDisplay();

            // 只有在有筛选条件时才执行筛选，否则让原始的setupShowMoreActivity生效
            if (selectedTags.length > 0) {
                filterContent();
                rebindShowMoreButton();
            }
        });
    </script>
</body>

</html>