<?php

namespace app\home\controller;

use app\home\controller\Common;

use think\facade\Db;
use think\Request;

class Forum extends Common
{
    public function index(Request $request)
    {
        $keyword = $request->param('q', '');

        //新闻
        $where = [
            ["category_id", "=", 1],
        ];
        if($keyword) {
            $where[] = ['title|content', 'like', "%{$keyword}%"];
        }
        $news = Db::name("News")
            ->where($where)
            ->order("publish_date desc")
            ->select()
            ->each(function ($item) {
                $item['user'] = $this->getUserInfo($item['user_id']);

                $where = [
                    "post_id" => $item['id'],
                    "parent_id" => 0,
                    "type" => 1,  //新闻/活动
                    "status" => 1,  //审核通过
                ];
                $item['reply_count'] = Db::name("Forum_reply")->where($where)->count();

                return $item;
            });

        //活动
        $where = [
            ["category_id", "=", 3],
        ];
        if($keyword) {
            $where[] = ['title|content', 'like', "%{$keyword}%"];
        }
        $activity = Db::name("News")
            ->where($where)
            ->order("publish_date desc")
            ->select()
            ->each(function ($item) {
                $item['user'] = $this->getUserInfo($item['user_id']);

                $where = [
                    "post_id" => $item['id'],
                    "parent_id" => 0,
                    "type" => 1,  //新闻/活动
                    "status" => 1,  //审核通过
                ];
                $item['reply_count'] = Db::name("Forum_reply")->where($where)->count();

                return $item;
            });

        //帖子
        $where = [
            ["status", "=", 1],  //审核通过
        ];
        if($keyword) {
            $where[] = ['title|content', 'like', "%{$keyword}%"];
        }
        $post = Db::name("Forum_posts")
            ->where($where)
            ->order("create_time desc")
            ->select()
            ->each(function ($item) {
                $item['user'] = $this->getUserInfo($item['user_id']);

                $where = [
                    "post_id" => $item['id'],
                    "parent_id" => 0,
                    "type" => 0,  //帖子
                    "status" => 1,  //审核通过
                ];
                $item['reply_count'] = Db::name("Forum_reply")->where($where)->count();

                return $item;
            });

        $news_count = Db::name("News")->whereIn("category_id", [1, 3])->count();
        $post_count = Db::name("Forum_posts")->where("status", 1)->count();
        $post_count = $news_count + $post_count;
        $reply_count = Db::name("Forum_reply")->where("status", 1)->count();
        $user_count = Db::name("User")->where("status", 1)->count();

        return view("", [
            "news" => $news,
            "activity" => $activity,
            "post" => $post,
            "post_count" => $post_count,
            "reply_count" => $reply_count,
            "user_count" => $user_count,
            "keyword" => $keyword,
        ]);
    }

    private function getUserInfo($user_id) {
        $where = [
            "id" => $user_id,
            "status" => 1,
        ];
        $user = Db::name("User")->where($where)->find();
        $user['role_name'] = Db::name("Roles")->where("id", $user['role_id'])->value("name");

        //用户question数量
        $where = [
            "user_id" => $user_id,
            "post_type" => 0,  //question
            "status" => 1,  //审核通过
        ];
        $user['question_count'] = Db::name("Forum_posts")->where($where)->count();

        //用户posting数量
        $where = [
            "user_id" => $user_id,
            "post_type" => 1,  //posting
            "status" => 1,  //审核通过
        ];
        $user['posting_count'] = Db::name("Forum_posts")->where($where)->count();

        //用户reply数量
        $where = [
            "user_id" => $user_id,
            "status" => 1,
        ];
        $user['reply_count'] = Db::name("Forum_reply")->where($where)->count();

        return $user;
    }

    //发布帖子
    public function post()
    {
        if ($this->request->isPost()) {
            if(!session("userId")){
                $this->error("Login first！", "/login");
            }

            //发布帖子
            $data = input('post.');

            if(!$data['title'] || empty($data['content'])){
                $this->error("Required fields are not filled in!");
            }

            $data['user_id'] = session('userId');

            //内部角色不需要审核
            $role_is_admin = $this->getUserRole(session('userId'));
            if($role_is_admin == 1) {
                //是后台角色
                $data['status'] = 1;  //审核通过
            } else {
                //注册角色
                $data['status'] = 0;  //审核中
            }

            $data['create_time'] = date("Y-m-d H:i:s");

            $s = Db::name("Forum_posts")->strict(false)->insertGetId($data);
            if ($s) {
                //标签
                if($data['tags']) {
                    $tags = explode(',', $data['tags']);
                    foreach($tags as $index=>$val) {
                        Db::name("Forum_posts_tags")->insert([
                            "post_id" => $s,
                            "tag_name" => $val,
                            "type" => 0,
                            "sort" => $index
                        ]);
                    }
                }

                //获取帖子内容中的提及用户列表
                $mentionedUserIds = $data['mentioned_users']; // 逗号分隔的ID字符串
                $userIdsArray = explode(',', $mentionedUserIds);
                // 处理每个被提及的用户
                foreach ($userIdsArray as $userId) {
                    if (!empty($userId)) {
                        // 给被提及用户发送通知
                        Db::name("User_message")->insertGetId([
                            "user_id" => $userId,
                            "sender_id" => session("userId"),
                            "content" => "The post mentioned you",
                            "main_id" => $s,
                            "type" => 4,
                            "create_time" => date("Y-m-d H:i:s")
                        ]);
                    }
                }

                if($role_is_admin == 1) {
                    //是后台角色
                    $this->success('Submitted successfully!');
                } else {
                    //注册角色

                    //给产品经理发送站内消息和邮件
                    $where = [
                        "role_id" => 2,  //产品经理角色
                        "status" => 1
                    ];
                    $product_manager = Db::name("User")->where($where)->column("id, email");
                    if(!empty($product_manager)) {
                        $sender = Db::name("User")->field("id, email, first_name, last_name")->where("id", $data['user_id'])->find();
                        foreach ($product_manager as $val){
                            //发送站内消息
                            $message_data = [
                                "user_id" => $val['id'],  //产品经理id
                                "sender_id" => $sender['id'],
                                "content" => "You have a post waiting for review",
                                "main_id" => $s,
                                "type" => 9,  //帖子审核通知
                                "create_time" => date('Y-m-d H:i:s')
                            ];
                            Db::name("User_message")->insert($message_data);

                            //发送通知邮件
                            $email_data = [
                                "system_name" => config('app.system_name'),
                                "name" => $sender['first_name']." ".$sender['last_name'],
                                "email" => $sender['email'],
                                "create_time" => $data['create_time'],
                                "post_title" => $data['title'],
                                "post_content" => $data['content'],
                                "login_link" => config('app.site_url')."admin",
                            ];
                            \app\services\MailService::sendEmail("send-examine", $val['email'], $email_data);
                        }
                    }

                    $this->success('Submitted successfully, can only be displayed after approval!');
                }

            } else {
                $this->error("Submission failed, please try again!");
            }
        } else {
            if(!session("userId")){
                return view('error/login_error');
            }

            //topic
            $topic = Db::name("Forum_topic")->order("sort asc")->select();

            //tags
            $tags = Db::name("Forum_tags")->order("sort asc")->select();
            // 转换为前端需要的格式
            $tagsOptions = [];
            foreach ($tags as $tag) {
                $tagsOptions[] = [
                    'value' => $tag['name'],  // 使用id作为value
                    'label' => $tag['name'],  // 使用name作为label
                ];
            }

            return view("", [
                "topic" => $topic,
                "tagsOptions" => json_encode($tagsOptions),
            ]);
        }
    }

    //修改帖子
    public function edit_post()
    {
        if ($this->request->isPost()) {
            if(!session("userId")){
                $this->error("Login first！", "/login");
            }

            //发布帖子
            $data = input('post.');

            if(!$data['title'] || empty($data['content'])){
                $this->error("Required fields are not filled in!");
            }

            $user_id = session("userId");
            $where = [
                ["id", "=", $data['id']],
                ["user_id", "=", $user_id],
                ["status", "=", 2],  //审核不通过
            ];
            $post = Db::name("Forum_posts")->where($where)->find();
            if(empty($post)) {
                $this->error("The post does not exist");
            }

            //内部角色不需要审核
            $role_is_admin = $this->getUserRole(session('userId'));
            if($role_is_admin == 1) {
                //是后台角色
                $data['status'] = 1;  //审核通过
            } else {
                //注册角色
                $data['status'] = 0;  //审核中
            }

            $data['update_time'] = date("Y-m-d H:i:s");
            $s = Db::name("Forum_posts")->strict(false)->update($data);
            if ($s) {
                //标签
                Db::name("Forum_posts_tags")->where([
                    "post_id" => $s,
                    "type" => 0,
                ])
                ->delete();
                if($data['tags']) {
                    $tags = explode(',', $data['tags']);
                    foreach($tags as $index=>$val) {
                        Db::name("Forum_posts_tags")->insert([
                            "post_id" => $s,
                            "tag_name" => $val,
                            "type" => 0,
                            "sort" => $index
                        ]);
                    }
                }

                //获取帖子内容中的提及用户列表
                $mentionedUserIds = $data['mentioned_users']; // 逗号分隔的ID字符串
                $userIdsArray = explode(',', $mentionedUserIds);
                // 处理每个被提及的用户
                foreach ($userIdsArray as $userId) {
                    if (!empty($userId)) {
                        // 给被提及用户发送通知
                        Db::name("User_message")->insertGetId([
                            "user_id" => $userId,
                            "sender_id" => session("userId"),
                            "content" => "The post mentioned you",
                            "main_id" => $s,
                            "type" => 4,
                            "create_time" => date("Y-m-d H:i:s")
                        ]);
                    }
                }

                if($role_is_admin == 1) {
                    //是后台角色
                    $this->success('Submitted successfully!');
                } else {
                    //注册角色

                    //给产品经理发送站内消息和邮件
                    $where = [
                        "role_id" => 2,  //产品经理角色
                        "status" => 1
                    ];
                    $product_manager = Db::name("User")->where($where)->column("id, email");
                    if(!empty($product_manager)) {
                        $sender = Db::name("User")->field("id, email, first_name, last_name")->where("id", $user_id)->find();
                        foreach ($product_manager as $val){
                            //发送站内消息
                            $message_data = [
                                "user_id" => $val['id'],  //产品经理id
                                "sender_id" => $sender['id'],
                                "content" => "You have a post waiting for review",
                                "main_id" => $s,
                                "type" => 9,  //帖子审核通知
                                "create_time" => date('Y-m-d H:i:s')
                            ];
                            Db::name("User_message")->insert($message_data);

                            //发送通知邮件
                            $email_data = [
                                "system_name" => config('app.system_name'),
                                "name" => $sender['first_name']." ".$sender['last_name'],
                                "email" => $sender['email'],
                                "create_time" => date('Y-m-d H:i:s'),
                                "post_title" => $data['title'],
                                "post_content" => $data['content'],
                                "login_link" => config('app.site_url')."admin",
                            ];
                            \app\services\MailService::sendEmail("send-examine", $val['email'], $email_data);
                        }
                    }

                    $this->success('Submitted successfully, can only be displayed after approval!', "/user/post");
                }

            } else {
                $this->error("Submission failed, please try again!");
            }
        } else {
            if(!session("userId")){
                return view('error/login_error');
            }

            $id = input("id");

            $user_id = session("userId");
            $where = [
                ["id", "=", $id],
                ["user_id", "=", $user_id],
                ["status", "=", 2],  //审核不通过
            ];
            $post = Db::name("Forum_posts")->where($where)->find();
            if(empty($post)) {
                return view('error/404');
            }

            $where = [
                "post_id" => $id,
                "type" => 0
            ];
            $selectedTags = Db::name("Forum_posts_tags")->where($where)->order("sort asc")->column("tag_name");

            //topic
            $topic = Db::name("Forum_topic")->order("sort asc")->select();

            //tags
            $tags = Db::name("Forum_tags")->order("sort asc")->select();
            // 转换为前端需要的格式
            $tagsOptions = [];
            foreach ($tags as $tag) {
                $tagsOptions[] = [
                    'value' => $tag['name'],  // 使用id作为value
                    'label' => $tag['name'],  // 使用name作为label
                ];
            }

            return view("", [
                "post" => $post,
                "topic" => $topic,
                "tagsOptions" => json_encode($tagsOptions),
                "selectedTags" => json_encode($selectedTags),
            ]);
        }
    }

    //帖子详情
    public function post_detail() {
        $id = input("id");
        $type =request()->rule()->getName();

        if($type == "news") {
            $where = [
                "id" => $id,
            ];
            $post = Db::name("News")->where($where)->find();

            $where = [
                "post_id" => $id,
                "type" => 1,
            ];
            $tags = Db::name("Forum_posts_tags")->where($where)->order("sort asc")->select();
        } else {
            $where = [
                "id" => $id,
                "status" => 1,  //审核通过
            ];
            $post = Db::name("Forum_posts")->where($where)->find();

            $where = [
                "post_id" => $id,
                "type" => 0,
            ];
            $tags = Db::name("Forum_posts_tags")->where($where)->order("sort asc")->select();
        }

        if(empty($post)) {
            return view('error/404');
        }

        $user = $this->getUserInfo($post['user_id']);

        $type = $type=="news" ? 1 : 0;  //用于提交主评论

        //帖子评论
        $where = [
            "post_id" => $id,
            "parent_id" => 0,  //主评论
            "type" => $type,
            "status" => 1,  //审核通过
        ];
        $replys = Db::name("Forum_reply")
            ->where($where)
            ->order("create_time desc")
            ->select()
            ->each(function ($item) {
                $item['user'] = $this->getUserInfo($item['user_id']);

                //子评论
                $where = [
                    "parent_id" => $item['id'],
                    "status" => 1,  //审核通过
                ];
                $item['replys'] = Db::name("Forum_reply")
                    ->where($where)
                    ->select()
                    ->order("create_time asc")
                    ->each(function ($i) {
                        $i['user'] = $this->getUserInfo($i['user_id']);

                        //被评论用户
                        $where = [
                            "id" => $i['reply_to_user'],
                            "status" => 1,
                        ];
                        $i['reply_to_user'] = Db::name("User")->where($where)->find();

                        return $i;
                    });

                return $item;
            });

        return view("", [
            "post" => $post,
            "user" => $user,
            "tags" => $tags,
            "type" => $type,
            "replys" => $replys,
        ]);
    }

    //帖子评论
    public function reply() {
        if ($this->request->isPost()) {
            if(!session("userId")){
                $this->error("Login first！", "/login");
            }

            $data = input('post.');

            if(empty($data['content'])){
                $this->error("The content is required!");
            }

            if(!empty($data['parent_id'])) {
                //子评论
                $parent_reply = Db::name("Forum_reply")->where("id", $data['parent_id'])->find();
                $data['post_id'] = $parent_reply['post_id'];   //帖子id
                $data['reply_to_user'] = $parent_reply['user_id'];   //被评论用户id
                $data['type'] = $parent_reply['type'];  //帖子类型：0帖子 1新闻/活
            } else {
                //主评论
                if(empty($data['post_id'])){
                    $this->error("Parameter error!");
                }

                $data['parent_id'] = 0;  //主评论
                $data['reply_to_user'] = Db::name("Forum_posts")->where("id", $data['post_id'])->value("user_id");  //被评论用户id
            }

            $data['user_id'] = session('userId');

            //内部角色不需要审核
            $role_is_admin = $this->getUserRole(session('userId'));
            if($role_is_admin == 1) {
                //后台角色
                $data['status'] = 1;  //审核通过
            } else {
                //注册角色
                $data['status'] = 0;  //审核中
            }

            $data['create_time'] = date("Y-m-d H:i:s");

            $s = Db::name("Forum_reply")->strict(false)->insertGetId($data);
            if ($s) {
                //获取评论内容中的提及用户列表
                $mentionedUserIds = $data['mentioned_users'];
                $userIdsArray = explode(',', $mentionedUserIds);
                // 处理每个被提及的用户
                foreach ($userIdsArray as $userId) {
                    if (!empty($userId)) {
                        // 给被提及用户发送通知
                        Db::name("User_message")->insertGetId([
                            "user_id" => $userId,
                            "sender_id" => session("userId"),
                            "content" => "The post comment mentioned you",
                            "main_id" => $data['post_id'],
                            "type" => 5,
                            "create_time" => date("Y-m-d H:i:s")
                        ]);
                    }
                }

                if($role_is_admin == 1) {
                    //后台角色，给被评论用户发送通知
                    $user = Db::name("User")->field("first_name, last_name")->where("id", session('userId'))->find();
                    Db::name("User_message")->insertGetId([
                        "user_id" => $data['reply_to_user'],
                        "sender_id" => session("userId"),
                        "content" => "The user ".$user['first_name']." ".$user['last_name']." commented on you",
                        "main_id" => $data['post_id'],
                        "type" => 3,  //帖子回复
                        "create_time" => date("Y-m-d H:i:s")
                    ]);
                }

                if($role_is_admin == 1) {
                    //是后台角色
                    $this->success('Submitted successfully!');
                } else {
                    //注册角色

                    //给产品经理发送站内消息和邮件
                    $where = [
                        "role_id" => 2,  //产品经理角色
                        "status" => 1
                    ];
                    $product_manager = Db::name("User")->where($where)->column("id, email");
                    if(!empty($product_manager)) {
                        $sender = Db::name("User")->field("id, email, first_name, last_name")->where("id", $data['user_id'])->find();
                        foreach ($product_manager as $val){
                            //发送站内消息
                            $message_data = [
                                "user_id" => $val['id'],  //产品经理id
                                "sender_id" => $sender['id'],
                                "content" => "You have a post comment waiting for review",
                                "main_id" => $data['post_id'],
                                "type" => 9,  //帖子审核通知
                                "create_time" => date('Y-m-d H:i:s')
                            ];
                            Db::name("User_message")->insert($message_data);

                            //发送通知邮件
                            $email_data = [
                                "system_name" => config('app.system_name'),
                                "name" => $sender['first_name']." ".$sender['last_name'],
                                "email" => $sender['email'],
                                "create_time" => $data['create_time'],
                                "post_title" => "",
                                "post_content" => $data['content'],
                                "login_link" => config('app.site_url')."admin",
                            ];
                            \app\services\MailService::sendEmail("send-examine", $val['email'], $email_data);
                        }
                    }

                    $this->success('Submitted successfully, can only be displayed after approval!');
                }
            } else {
                $this->error("Submission failed, please try again!");
            }
        }
    }

    //修改评论
    public function edit_reply() {
        if ($this->request->isPost()) {
            if(!session("userId")){
                $this->error("Login first！", "/login");
            }

            $data = input('post.');

            if(empty($data['content'])){
                $this->error("The content is required!");
            }

            $user_id = session("userId");
            $where = [
                ["id", "=", $data['id']],
                ["user_id", "=", $user_id],
                ["status", "=", 2],  //审核不通过
            ];
            $post = Db::name("Forum_reply")->where($where)->find();
            if(empty($post)) {
                $this->error("Comment does not exist!");
            }

            //内部角色不需要审核
            $role_is_admin = $this->getUserRole(session('userId'));
            if($role_is_admin == 1) {
                //后台角色
                $data['status'] = 1;  //审核通过
            } else {
                //注册角色
                $data['status'] = 0;  //审核中
            }

            $data['update_time'] = date("Y-m-d H:i:s");
            $s = Db::name("Forum_reply")->strict(false)->update($data);
            if ($s) {
                //获取评论内容中的提及用户列表
                $mentionedUserIds = $data['mentioned_users'];
                $userIdsArray = explode(',', $mentionedUserIds);
                // 处理每个被提及的用户
                foreach ($userIdsArray as $userId) {
                    if (!empty($userId)) {
                        // 给被提及用户发送通知
                        Db::name("User_message")->insertGetId([
                            "user_id" => $userId,
                            "sender_id" => session("userId"),
                            "content" => "The post comment mentioned you",
                            "main_id" => $post['post_id'],
                            "type" => 5,
                            "create_time" => date("Y-m-d H:i:s")
                        ]);
                    }
                }

                if($role_is_admin == 1) {
                    //后台角色，给被评论用户发送通知
                    $user = Db::name("User")->field("first_name, last_name")->where("id", session('userId'))->find();
                    Db::name("User_message")->insertGetId([
                        "user_id" => $post['reply_to_user'],
                        "sender_id" => session("userId"),
                        "content" => "The user ".$user['first_name']." ".$user['last_name']." commented on you",
                        "main_id" => $post['post_id'],
                        "type" => 3,  //帖子回复
                        "create_time" => date("Y-m-d H:i:s")
                    ]);
                }

                if($role_is_admin == 1) {
                    //是后台角色
                    $this->success('Submitted successfully!');
                } else {
                    //注册角色

                    //给产品经理发送站内消息和邮件
                    $where = [
                        "role_id" => 2,  //产品经理角色
                        "status" => 1
                    ];
                    $product_manager = Db::name("User")->where($where)->column("id, email");
                    if(!empty($product_manager)) {
                        $sender = Db::name("User")->field("id, email, first_name, last_name")->where("id", $user_id)->find();
                        foreach ($product_manager as $val){
                            //发送站内消息
                            $message_data = [
                                "user_id" => $val['id'],  //产品经理id
                                "sender_id" => $sender['id'],
                                "content" => "You have a post comment waiting for review",
                                "main_id" => $post['post_id'],
                                "type" => 9,  //帖子审核通知
                                "create_time" => date('Y-m-d H:i:s')
                            ];
                            Db::name("User_message")->insert($message_data);

                            //发送通知邮件
                            $email_data = [
                                "system_name" => config('app.system_name'),
                                "name" => $sender['first_name']." ".$sender['last_name'],
                                "email" => $sender['email'],
                                "create_time" => date('Y-m-d H:i:s'),
                                "post_title" => "",
                                "post_content" => $data['content'],
                                "login_link" => config('app.site_url')."admin",
                            ];
                            \app\services\MailService::sendEmail("send-examine", $val['email'], $email_data);
                        }
                    }

                    $this->success('Submitted successfully, can only be displayed after approval!', "/user/comment");
                }
            } else {
                $this->error("Submission failed, please try again!");
            }
        } else {
            if(!session("userId")){
                return view('error/login_error');
            }

            $id = input("id");

            $user_id = session("userId");
            $where = [
                ["id", "=", $id],
                ["user_id", "=", $user_id],
                ["status", "=", 2],  //审核不通过
            ];
            $post = Db::name("Forum_reply")->where($where)->find();
            if(empty($post)) {
                return view('error/404');
            }

            return view("", [
                "post" => $post,
            ]);
        }
    }

    private function getUserRole($user_id) {
        $role_id = Db::name("User")->where("id", $user_id)->value("role_id");
        $role_is_admin = Db::name("Roles")->where("id", $role_id)->value("is_admin");

        return $role_is_admin;
    }

    public function user()
    {
        $id = input("id");  //用户id
        if(empty($id)) {
            return view("error/404")->code(404);
        }

        $where = [
            "id" => $id,
            "status" => 1,
        ];
        $user = Db::name("User")->where($where)->find();
        if(empty($user)) {
            return view("error/404")->code(404);
        }

        $role = Db::name("Roles")->where("id", $user['role_id'])->find();

        $where = [
            "user_id" => $id,
            "post_type" => 0,
            "status" => 1,
        ];
        $question_count = Db::name("Forum_posts")->where($where)->count();

        $where = [
            "user_id" => $id,
            "post_type" => 1,
            "status" => 1,
        ];
        $posts_count = Db::name("Forum_posts")->where($where)->count();

        $where = [
            "user_id" => $id,
            "status" => 1,
        ];
        $reply_count = Db::name("Forum_reply")->where($where)->count();

        //审核通过的post
        $where = [
            "user_id" => $id,
            "status" => 1,
        ];
        $posts = Db::name("Forum_posts")->where($where)->select();

        return view("", [
            "user" => $user,
            "role" => $role,
            "question_count" => $question_count,
            "posts_count" => $posts_count,
            "reply_count" => $reply_count,
            "posts" => $posts,
        ]);
    }

    public function tag(Request $request)
    {
        //所有tag
        $tags = Db::name("Forum_tags")
            ->order("sort asc")
            ->select()
            ->each(function ($item) {
                //标签下的帖子数
                $item['count'] = Db::name("Forum_posts_tags")->where("tag_name", $item['name'])->count();

                return $item;
            });

        $keyword = $request->param('q', '');

        $where = [];
        if($keyword) {
            $where[] = ['title|content', 'like', "%{$keyword}%"];
        }

        $news = Db::name("News")
            ->where($where)
            ->order("publish_date desc")
            ->select()
            ->each(function ($item) {
                $item['user'] = $this->getUserInfo($item['user_id']);

                $item['item_url'] = 'news'; // 添加类型标识
                $item['post_type'] = 1;  //非问答

                $where = [
                    "post_id" => $item['id'],
                    "type" => 1,  //新闻
                ];
                $item_tags = Db::name("Forum_posts_tags")->where($where)->column("tag_name");
                $item['tags'] = implode(",", $item_tags);

                $where = [
                    "post_id" => $item['id'],
                    "parent_id" => 0,
                    "type" => 1,
                    "status" => 1,  //审核通过
                ];
                $item['reply_count'] = Db::name("Forum_reply")->where($where)->count();

                return $item;
            });

        $where = [
            ["status", "=", 1],  //审核通过
        ];
        if($keyword) {
            $where[] = ['title|content', 'like', "%{$keyword}%"];
        }
        $post = Db::name("Forum_posts")
            ->where($where)
            ->order("create_time desc")
            ->select()
            ->each(function ($item) {
                $item['user'] = $this->getUserInfo($item['user_id']);

                $item['item_url'] = 'post'; // 添加类型标识

                $where = [
                    "post_id" => $item['id'],
                    "type" => 0,  //帖子
                ];
                $item_tags = Db::name("Forum_posts_tags")->where($where)->column("tag_name");
                $item['tags'] = implode(",", $item_tags);

                $where = [
                    "post_id" => $item['id'],
                    "parent_id" => 0,
                    "type" => 0,  //帖子
                    "status" => 1,  //审核通过
                ];
                $item['reply_count'] = Db::name("Forum_reply")->where($where)->count();

                return $item;
            });

        // 合并两个数据集
        $all_post = array_merge($news->toArray(), $post->toArray());

        // 按时间倒序排序
        usort($all_post, function ($a, $b) {
            return strtotime($b['create_time']) - strtotime($a['create_time']);
        });

        return view("", [
            "tags" => $tags,
            "all_post" => $all_post,
            "keyword" => $keyword
        ]);
    }

    public function topic(Request $request)
    {
        //话题名称
        $name = input("name");

        $getone = Db::name("Forum_topic")->where("name", $name)->find();
        if(empty($getone)) {
            return view("error/404")->code(404);
        }

        $keyword = $request->param('q', '');
        $where = [
            ["topic", "=", $name],
        ];

        if($keyword) {
            $where[] = ['title|content', 'like', "%{$keyword}%"];
        }

        //当前话题下所有的帖子和新闻
        $news = Db::name("News")
            ->where($where)
            ->order("publish_date desc")
            ->select()
            ->each(function ($item) {
                $item['user'] = $this->getUserInfo($item['user_id']);

                $item['item_url'] = 'news'; // 添加类型标识
                $item['post_type'] = 1;  //非问答

                $where = [
                    "post_id" => $item['id'],
                    "parent_id" => 0,
                    "type" => 1,
                    "status" => 1,  //审核通过
                ];
                $item['reply_count'] = Db::name("Forum_reply")->where($where)->count();

                return $item;
            });

        $where = [
            ["status", "=", 1],  //审核通过
            ["topic", "=", $name]
        ];
        if($keyword) {
            $where[] = ['title|content', 'like', "%{$keyword}%"];
        }
        $post = Db::name("Forum_posts")
            ->where($where)
            ->order("create_time desc")
            ->select()
            ->each(function ($item) {
                $item['user'] = $this->getUserInfo($item['user_id']);

                $item['item_url'] = 'post'; // 添加类型标识

                $where = [
                    "post_id" => $item['id'],
                    "parent_id" => 0,
                    "type" => 0,  //帖子
                    "status" => 1,  //审核通过
                ];
                $item['reply_count'] = Db::name("Forum_reply")->where($where)->count();

                return $item;
            });

        // 合并两个数据集
        $all_post = array_merge($news->toArray(), $post->toArray());

        // 按时间倒序排序
        usort($all_post, function ($a, $b) {
            return strtotime($b['create_time']) - strtotime($a['create_time']);
        });

        $reply_count = array_reduce($all_post, function ($carry, $item) {
            return $carry + ($item['reply_count'] ?? 0);
        }, 0);

        return view("", [
            "name" => $name,
            "all_post" => $all_post,
            "reply_count" => $reply_count,
            "keyword" => $keyword
        ]);
    }

}
