/*! tailwindcss v4.1.10 | MIT License | https://tailwindcss.com */
@layer properties;
@layer theme, base, components, utilities;
@layer theme {
  :root, :host {
    --font-sans: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji",
      "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
    --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono",
      "Courier New", monospace;
    --color-red-500: oklch(63.7% 0.237 25.331);
    --color-gray-200: oklch(92.8% 0.006 264.531);
    --color-white: #fff;
    --spacing: 0.25rem;
    --container-3xs: 16rem;
    --container-2xs: 18rem;
    --container-2xl: 42rem;
    --container-3xl: 48rem;
    --container-5xl: 64rem;
    --text-xs: 0.75rem;
    --text-xs--line-height: calc(1 / 0.75);
    --text-sm: 0.875rem;
    --text-sm--line-height: calc(1.25 / 0.875);
    --text-base: 1rem;
    --text-base--line-height: calc(1.5 / 1);
    --text-lg: 1.125rem;
    --text-lg--line-height: calc(1.75 / 1.125);
    --text-xl: 1.25rem;
    --text-xl--line-height: calc(1.75 / 1.25);
    --text-2xl: 1.5rem;
    --text-2xl--line-height: calc(2 / 1.5);
    --text-3xl: 1.875rem;
    --text-3xl--line-height: calc(2.25 / 1.875);
    --text-4xl: 2.25rem;
    --text-4xl--line-height: calc(2.5 / 2.25);
    --text-5xl: 3rem;
    --text-5xl--line-height: 1;
    --text-6xl: 3.75rem;
    --text-6xl--line-height: 1;
    --text-8xl: 6rem;
    --text-8xl--line-height: 1;
    --text-9xl: 8rem;
    --text-9xl--line-height: 1;
    --font-weight-bold: 700;
    --radius-sm: 0.25rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;
    --radius-2xl: 1rem;
    --radius-3xl: 1.5rem;
    --radius-4xl: 2rem;
    --animate-ping: ping 1s cubic-bezier(0, 0, 0.2, 1) infinite;
    --default-transition-duration: 150ms;
    --default-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    --default-font-family: var(--font-sans);
    --default-mono-font-family: var(--font-mono);
  }
}
@layer base {
  *, ::after, ::before, ::backdrop, ::file-selector-button {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    border: 0 solid;
  }
  html, :host {
    line-height: 1.5;
    -webkit-text-size-adjust: 100%;
    tab-size: 4;
    font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji");
    font-feature-settings: var(--default-font-feature-settings, normal);
    font-variation-settings: var(--default-font-variation-settings, normal);
    -webkit-tap-highlight-color: transparent;
  }
  hr {
    height: 0;
    color: inherit;
    border-top-width: 1px;
  }
  abbr:where([title]) {
    -webkit-text-decoration: underline dotted;
    text-decoration: underline dotted;
  }
  h1, h2, h3, h4, h5, h6 {
    font-size: inherit;
    font-weight: inherit;
  }
  a {
    color: inherit;
    -webkit-text-decoration: inherit;
    text-decoration: inherit;
  }
  b, strong {
    font-weight: bolder;
  }
  code, kbd, samp, pre {
    font-family: var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace);
    font-feature-settings: var(--default-mono-font-feature-settings, normal);
    font-variation-settings: var(--default-mono-font-variation-settings, normal);
    font-size: 1em;
  }
  small {
    font-size: 80%;
  }
  sub, sup {
    font-size: 75%;
    line-height: 0;
    position: relative;
    vertical-align: baseline;
  }
  sub {
    bottom: -0.25em;
  }
  sup {
    top: -0.5em;
  }
  table {
    text-indent: 0;
    border-color: inherit;
    border-collapse: collapse;
  }
  :-moz-focusring {
    outline: auto;
  }
  progress {
    vertical-align: baseline;
  }
  summary {
    display: list-item;
  }
  ol, ul, menu {
    list-style: none;
  }
  img, svg, video, canvas, audio, iframe, embed, object {
    display: block;
    vertical-align: middle;
  }
  img, video {
    max-width: 100%;
    height: auto;
  }
  button, input, select, optgroup, textarea, ::file-selector-button {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    border-radius: 0;
    background-color: transparent;
    opacity: 1;
  }
  :where(select:is([multiple], [size])) optgroup {
    font-weight: bolder;
  }
  :where(select:is([multiple], [size])) optgroup option {
    padding-inline-start: 20px;
  }
  ::file-selector-button {
    margin-inline-end: 4px;
  }
  ::placeholder {
    opacity: 1;
  }
  @supports (not (-webkit-appearance: -apple-pay-button))  or (contain-intrinsic-size: 1px) {
    ::placeholder {
      color: currentcolor;
      @supports (color: color-mix(in lab, red, red)) {
        color: color-mix(in oklab, currentcolor 50%, transparent);
      }
    }
  }
  textarea {
    resize: vertical;
  }
  ::-webkit-search-decoration {
    -webkit-appearance: none;
  }
  ::-webkit-date-and-time-value {
    min-height: 1lh;
    text-align: inherit;
  }
  ::-webkit-datetime-edit {
    display: inline-flex;
  }
  ::-webkit-datetime-edit-fields-wrapper {
    padding: 0;
  }
  ::-webkit-datetime-edit, ::-webkit-datetime-edit-year-field, ::-webkit-datetime-edit-month-field, ::-webkit-datetime-edit-day-field, ::-webkit-datetime-edit-hour-field, ::-webkit-datetime-edit-minute-field, ::-webkit-datetime-edit-second-field, ::-webkit-datetime-edit-millisecond-field, ::-webkit-datetime-edit-meridiem-field {
    padding-block: 0;
  }
  :-moz-ui-invalid {
    box-shadow: none;
  }
  button, input:where([type="button"], [type="reset"], [type="submit"]), ::file-selector-button {
    appearance: button;
  }
  ::-webkit-inner-spin-button, ::-webkit-outer-spin-button {
    height: auto;
  }
  [hidden]:where(:not([hidden="until-found"])) {
    display: none !important;
  }
}
@layer utilities {
  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border-width: 0;
  }
  .absolute {
    position: absolute;
  }
  .fixed {
    position: fixed;
  }
  .relative {
    position: relative;
  }
  .static {
    position: static;
  }
  .sticky {
    position: sticky;
  }
  .-top-1 {
    top: calc(var(--spacing) * -1);
  }
  .-top-2 {
    top: calc(var(--spacing) * -2);
  }
  .top-0 {
    top: calc(var(--spacing) * 0);
  }
  .top-1\/2 {
    top: calc(1/2 * 100%);
  }
  .top-2 {
    top: calc(var(--spacing) * 2);
  }
  .top-2\.5 {
    top: calc(var(--spacing) * 2.5);
  }
  .top-96 {
    top: calc(var(--spacing) * 96);
  }
  .top-full {
    top: 100%;
  }
  .-right-\[\.525rem\] {
    right: calc(.525rem * -1);
  }
  .right-0 {
    right: calc(var(--spacing) * 0);
  }
  .right-2 {
    right: calc(var(--spacing) * 2);
  }
  .right-2\.5 {
    right: calc(var(--spacing) * 2.5);
  }
  .right-3 {
    right: calc(var(--spacing) * 3);
  }
  .right-4 {
    right: calc(var(--spacing) * 4);
  }
  .right-5 {
    right: calc(var(--spacing) * 5);
  }
  .right-7 {
    right: calc(var(--spacing) * 7);
  }
  .right-\[\.425rem\] {
    right: .425rem;
  }
  .right-\[110\%\] {
    right: 110%;
  }
  .right-\[120\%\] {
    right: 120%;
  }
  .bottom-0 {
    bottom: calc(var(--spacing) * 0);
  }
  .bottom-3 {
    bottom: calc(var(--spacing) * 3);
  }
  .bottom-5 {
    bottom: calc(var(--spacing) * 5);
  }
  .-left-6 {
    left: calc(var(--spacing) * -6);
  }
  .-left-\[50\%\] {
    left: calc(50% * -1);
  }
  .left-0 {
    left: calc(var(--spacing) * 0);
  }
  .left-1\/2 {
    left: calc(1/2 * 100%);
  }
  .left-4 {
    left: calc(var(--spacing) * 4);
  }
  .left-\[10\%\] {
    left: 10%;
  }
  .-z-10 {
    z-index: calc(10 * -1);
  }
  .-z-\[1\] {
    z-index: calc(1 * -1);
  }
  .z-9 {
    z-index: 9;
  }
  .z-10 {
    z-index: 10;
  }
  .z-20 {
    z-index: 20;
  }
  .z-30 {
    z-index: 30;
  }
  .z-50 {
    z-index: 50;
  }
  .z-\[2\] {
    z-index: 2;
  }
  .z-\[999\] {
    z-index: 999;
  }
  .z-\[9999\] {
    z-index: 9999;
  }
  .z-\[99999\] {
    z-index: 99999;
  }
  .order-1 {
    order: 1;
  }
  .order-2 {
    order: 2;
  }
  .float-right {
    float: right;
  }
  .container {
    width: 100%;
    @media (width >= 40rem) {
      max-width: 40rem;
    }
    @media (width >= 48rem) {
      max-width: 48rem;
    }
    @media (width >= 64rem) {
      max-width: 64rem;
    }
    @media (width >= 80rem) {
      max-width: 80rem;
    }
    @media (width >= 96rem) {
      max-width: 96rem;
    }
  }
  .mx-1\.5 {
    margin-inline: calc(var(--spacing) * 1.5);
  }
  .mx-auto {
    margin-inline: auto;
  }
  .my-4 {
    margin-block: calc(var(--spacing) * 4);
  }
  .mt-0\.5 {
    margin-top: calc(var(--spacing) * 0.5);
  }
  .mt-2 {
    margin-top: calc(var(--spacing) * 2);
  }
  .mt-2\.5 {
    margin-top: calc(var(--spacing) * 2.5);
  }
  .mt-3 {
    margin-top: calc(var(--spacing) * 3);
  }
  .mt-4 {
    margin-top: calc(var(--spacing) * 4);
  }
  .mt-5 {
    margin-top: calc(var(--spacing) * 5);
  }
  .mt-7 {
    margin-top: calc(var(--spacing) * 7);
  }
  .mt-8 {
    margin-top: calc(var(--spacing) * 8);
  }
  .mt-\[\.625rem\] {
    margin-top: .625rem;
  }
  .mt-\[1\.875rem\] {
    margin-top: 1.875rem;
  }
  .mr-1 {
    margin-right: calc(var(--spacing) * 1);
  }
  .mr-1\.5 {
    margin-right: calc(var(--spacing) * 1.5);
  }
  .mr-2 {
    margin-right: calc(var(--spacing) * 2);
  }
  .mr-5 {
    margin-right: calc(var(--spacing) * 5);
  }
  .mb-0\.5 {
    margin-bottom: calc(var(--spacing) * 0.5);
  }
  .mb-1 {
    margin-bottom: calc(var(--spacing) * 1);
  }
  .mb-1\.5 {
    margin-bottom: calc(var(--spacing) * 1.5);
  }
  .mb-2 {
    margin-bottom: calc(var(--spacing) * 2);
  }
  .mb-2\.5 {
    margin-bottom: calc(var(--spacing) * 2.5);
  }
  .mb-3 {
    margin-bottom: calc(var(--spacing) * 3);
  }
  .mb-3\.5 {
    margin-bottom: calc(var(--spacing) * 3.5);
  }
  .mb-4 {
    margin-bottom: calc(var(--spacing) * 4);
  }
  .mb-5 {
    margin-bottom: calc(var(--spacing) * 5);
  }
  .mb-6 {
    margin-bottom: calc(var(--spacing) * 6);
  }
  .mb-7 {
    margin-bottom: calc(var(--spacing) * 7);
  }
  .mb-8 {
    margin-bottom: calc(var(--spacing) * 8);
  }
  .mb-10 {
    margin-bottom: calc(var(--spacing) * 10);
  }
  .ml-1 {
    margin-left: calc(var(--spacing) * 1);
  }
  .ml-2 {
    margin-left: calc(var(--spacing) * 2);
  }
  .ml-2\.5 {
    margin-left: calc(var(--spacing) * 2.5);
  }
  .line-clamp-1 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
  }
  .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }
  .block {
    display: block;
  }
  .contents {
    display: contents;
  }
  .flex {
    display: flex;
  }
  .grid {
    display: grid;
  }
  .hidden {
    display: none;
  }
  .inline {
    display: inline;
  }
  .inline-block {
    display: inline-block;
  }
  .inline-flex {
    display: inline-flex;
  }
  .list-item {
    display: list-item;
  }
  .table {
    display: table;
  }
  .h-3 {
    height: calc(var(--spacing) * 3);
  }
  .h-4 {
    height: calc(var(--spacing) * 4);
  }
  .h-5 {
    height: calc(var(--spacing) * 5);
  }
  .h-8 {
    height: calc(var(--spacing) * 8);
  }
  .h-10 {
    height: calc(var(--spacing) * 10);
  }
  .h-14 {
    height: calc(var(--spacing) * 14);
  }
  .h-80 {
    height: calc(var(--spacing) * 80);
  }
  .h-\[\.3125rem\] {
    height: .3125rem;
  }
  .h-\[\.5625rem\] {
    height: .5625rem;
  }
  .h-\[1\.25rem\] {
    height: 1.25rem;
  }
  .h-\[1\.0625rem\] {
    height: 1.0625rem;
  }
  .h-\[1\.875rem\] {
    height: 1.875rem;
  }
  .h-\[1rem\] {
    height: 1rem;
  }
  .h-\[2\.5rem\] {
    height: 2.5rem;
  }
  .h-\[3\.75rem\] {
    height: 3.75rem;
  }
  .h-\[3\.125rem\] {
    height: 3.125rem;
  }
  .h-\[4\.375rem\] {
    height: 4.375rem;
  }
  .h-\[4rem\] {
    height: 4rem;
  }
  .h-\[6\.25rem\] {
    height: 6.25rem;
  }
  .h-\[7\.75rem\] {
    height: 7.75rem;
  }
  .h-\[9\.375rem\] {
    height: 9.375rem;
  }
  .h-\[9px\] {
    height: 9px;
  }
  .h-\[10rem\] {
    height: 10rem;
  }
  .h-\[12\.5rem\] {
    height: 12.5rem;
  }
  .h-\[18\.75rem\] {
    height: 18.75rem;
  }
  .h-\[20rem\] {
    height: 20rem;
  }
  .h-\[25rem\] {
    height: 25rem;
  }
  .h-\[50px\] {
    height: 50px;
  }
  .h-\[80\%\] {
    height: 80%;
  }
  .h-\[calc\(100\%-3\.125rem\)\] {
    height: calc(100% - 3.125rem);
  }
  .h-\[calc\(100\%-50px\)\] {
    height: calc(100% - 50px);
  }
  .h-full {
    height: 100%;
  }
  .max-h-2\/3 {
    max-height: calc(2/3 * 100%);
  }
  .max-h-\[18\.75rem\] {
    max-height: 18.75rem;
  }
  .max-h-\[86\%\] {
    max-height: 86%;
  }
  .min-h-\[1rem\] {
    min-height: 1rem;
  }
  .min-h-\[5\.625rem\] {
    min-height: 5.625rem;
  }
  .min-h-\[6\.25rem\] {
    min-height: 6.25rem;
  }
  .min-h-lvh {
    min-height: 100lvh;
  }
  .w-1\/3 {
    width: calc(1/3 * 100%);
  }
  .w-2\/3 {
    width: calc(2/3 * 100%);
  }
  .w-3 {
    width: calc(var(--spacing) * 3);
  }
  .w-3\.5 {
    width: calc(var(--spacing) * 3.5);
  }
  .w-4 {
    width: calc(var(--spacing) * 4);
  }
  .w-5 {
    width: calc(var(--spacing) * 5);
  }
  .w-8 {
    width: calc(var(--spacing) * 8);
  }
  .w-10 {
    width: calc(var(--spacing) * 10);
  }
  .w-10\/12 {
    width: calc(10/12 * 100%);
  }
  .w-11\/12 {
    width: calc(11/12 * 100%);
  }
  .w-12 {
    width: calc(var(--spacing) * 12);
  }
  .w-14 {
    width: calc(var(--spacing) * 14);
  }
  .w-24 {
    width: calc(var(--spacing) * 24);
  }
  .w-80 {
    width: calc(var(--spacing) * 80);
  }
  .w-\[\.875rem\] {
    width: .875rem;
  }
  .w-\[0\.8rem\] {
    width: 0.8rem;
  }
  .w-\[1\.5rem\] {
    width: 1.5rem;
  }
  .w-\[1\.25rem\] {
    width: 1.25rem;
  }
  .w-\[1\.125rem\] {
    width: 1.125rem;
  }
  .w-\[1\.375rem\] {
    width: 1.375rem;
  }
  .w-\[1\.0625rem\] {
    width: 1.0625rem;
  }
  .w-\[1\.875rem\] {
    width: 1.875rem;
  }
  .w-\[1rem\] {
    width: 1rem;
  }
  .w-\[2\.1875rem\] {
    width: 2.1875rem;
  }
  .w-\[3\.75rem\] {
    width: 3.75rem;
  }
  .w-\[3\.125rem\] {
    width: 3.125rem;
  }
  .w-\[4\.375rem\] {
    width: 4.375rem;
  }
  .w-\[4rem\] {
    width: 4rem;
  }
  .w-\[6\.25rem\] {
    width: 6.25rem;
  }
  .w-\[8\.5rem\] {
    width: 8.5rem;
  }
  .w-\[9\.375rem\] {
    width: 9.375rem;
  }
  .w-\[12\.5rem\] {
    width: 12.5rem;
  }
  .w-\[13rem\] {
    width: 13rem;
  }
  .w-\[16px\] {
    width: 16px;
  }
  .w-\[150px\] {
    width: 150px;
  }
  .w-\[\=28\.125rem\] {
    width: =28.125rem;
  }
  .w-full {
    width: 100%;
  }
  .max-w-2\/3 {
    max-width: calc(2/3 * 100%);
  }
  .max-w-2xs {
    max-width: var(--container-2xs);
  }
  .max-w-\[12\.5rem\] {
    max-width: 12.5rem;
  }
  .max-w-\[70\%\] {
    max-width: 70%;
  }
  .max-w-\[80\%\] {
    max-width: 80%;
  }
  .max-w-\[86\%\] {
    max-width: 86%;
  }
  .min-w-3xs {
    min-width: var(--container-3xs);
  }
  .min-w-8 {
    min-width: calc(var(--spacing) * 8);
  }
  .min-w-12 {
    min-width: calc(var(--spacing) * 12);
  }
  .min-w-20 {
    min-width: calc(var(--spacing) * 20);
  }
  .min-w-36 {
    min-width: calc(var(--spacing) * 36);
  }
  .min-w-\[1rem\] {
    min-width: 1rem;
  }
  .min-w-\[12\.5rem\] {
    min-width: 12.5rem;
  }
  .min-w-\[13\.5rem\] {
    min-width: 13.5rem;
  }
  .min-w-\[15\.625rem\] {
    min-width: 15.625rem;
  }
  .min-w-\[16\.25rem\] {
    min-width: 16.25rem;
  }
  .min-w-\[90\%\] {
    min-width: 90%;
  }
  .min-w-max {
    min-width: max-content;
  }
  .flex-1 {
    flex: 1;
  }
  .flex-1\/2 {
    flex: calc(1/2 * 100%);
  }
  .flex-2\/3 {
    flex: calc(2/3 * 100%);
  }
  .-translate-1\/2 {
    --tw-translate-x: calc(calc(1/2 * 100%) * -1);
    --tw-translate-y: calc(calc(1/2 * 100%) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .-translate-x-1\/2 {
    --tw-translate-x: calc(calc(1/2 * 100%) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .-translate-y-1\/2 {
    --tw-translate-y: calc(calc(1/2 * 100%) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .scale-75 {
    --tw-scale-x: 75%;
    --tw-scale-y: 75%;
    --tw-scale-z: 75%;
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }
  .rotate-0 {
    rotate: 0deg;
  }
  .transform {
    transform: var(--tw-rotate-x,) var(--tw-rotate-y,) var(--tw-rotate-z,) var(--tw-skew-x,) var(--tw-skew-y,);
  }
  .animate-ping {
    animation: var(--animate-ping);
  }
  .cursor-default {
    cursor: default;
  }
  .cursor-not-allowed {
    cursor: not-allowed;
  }
  .cursor-pointer {
    cursor: pointer;
  }
  .resize {
    resize: both;
  }
  .resize-none {
    resize: none;
  }
  .appearance-none {
    appearance: none;
  }
  .grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
  .grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
  .flex-col {
    flex-direction: column;
  }
  .flex-nowrap {
    flex-wrap: nowrap;
  }
  .flex-wrap {
    flex-wrap: wrap;
  }
  .items-baseline {
    align-items: baseline;
  }
  .items-center {
    align-items: center;
  }
  .items-start {
    align-items: flex-start;
  }
  .justify-around {
    justify-content: space-around;
  }
  .justify-between {
    justify-content: space-between;
  }
  .justify-center {
    justify-content: center;
  }
  .justify-end {
    justify-content: flex-end;
  }
  .justify-start {
    justify-content: flex-start;
  }
  .gap-1\.5 {
    gap: calc(var(--spacing) * 1.5);
  }
  .gap-2 {
    gap: calc(var(--spacing) * 2);
  }
  .gap-2\.5 {
    gap: calc(var(--spacing) * 2.5);
  }
  .gap-3 {
    gap: calc(var(--spacing) * 3);
  }
  .gap-3\.5 {
    gap: calc(var(--spacing) * 3.5);
  }
  .gap-4 {
    gap: calc(var(--spacing) * 4);
  }
  .gap-5 {
    gap: calc(var(--spacing) * 5);
  }
  .gap-x-1 {
    column-gap: calc(var(--spacing) * 1);
  }
  .gap-x-1\.5 {
    column-gap: calc(var(--spacing) * 1.5);
  }
  .gap-x-2 {
    column-gap: calc(var(--spacing) * 2);
  }
  .gap-x-2\.5 {
    column-gap: calc(var(--spacing) * 2.5);
  }
  .gap-x-3 {
    column-gap: calc(var(--spacing) * 3);
  }
  .gap-x-4 {
    column-gap: calc(var(--spacing) * 4);
  }
  .gap-x-5 {
    column-gap: calc(var(--spacing) * 5);
  }
  .gap-x-10 {
    column-gap: calc(var(--spacing) * 10);
  }
  .gap-y-2 {
    row-gap: calc(var(--spacing) * 2);
  }
  .gap-y-3 {
    row-gap: calc(var(--spacing) * 3);
  }
  .gap-y-4 {
    row-gap: calc(var(--spacing) * 4);
  }
  .gap-y-5 {
    row-gap: calc(var(--spacing) * 5);
  }
  .gap-y-6 {
    row-gap: calc(var(--spacing) * 6);
  }
  .gap-y-20 {
    row-gap: calc(var(--spacing) * 20);
  }
  .overflow-auto {
    overflow: auto;
  }
  .overflow-hidden {
    overflow: hidden;
  }
  .overflow-x-auto {
    overflow-x: auto;
  }
  .overflow-y-auto {
    overflow-y: auto;
  }
  .rounded-2xl {
    border-radius: var(--radius-2xl);
  }
  .rounded-3xl {
    border-radius: var(--radius-3xl);
  }
  .rounded-4xl {
    border-radius: var(--radius-4xl);
  }
  .rounded-full {
    border-radius: calc(infinity * 1px);
  }
  .rounded-lg {
    border-radius: var(--radius-lg);
  }
  .rounded-md {
    border-radius: var(--radius-md);
  }
  .rounded-sm {
    border-radius: var(--radius-sm);
  }
  .rounded-xl {
    border-radius: var(--radius-xl);
  }
  .rounded-tl-xl {
    border-top-left-radius: var(--radius-xl);
  }
  .rounded-tr-3xl {
    border-top-right-radius: var(--radius-3xl);
  }
  .rounded-tr-xl {
    border-top-right-radius: var(--radius-xl);
  }
  .rounded-br-none {
    border-bottom-right-radius: 0;
  }
  .rounded-bl-none {
    border-bottom-left-radius: 0;
  }
  .border {
    border-style: var(--tw-border-style);
    border-width: 1px;
  }
  .border-0 {
    border-style: var(--tw-border-style);
    border-width: 0px;
  }
  .border-t {
    border-top-style: var(--tw-border-style);
    border-top-width: 1px;
  }
  .border-t-2 {
    border-top-style: var(--tw-border-style);
    border-top-width: 2px;
  }
  .border-r {
    border-right-style: var(--tw-border-style);
    border-right-width: 1px;
  }
  .border-b {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 1px;
  }
  .border-l {
    border-left-style: var(--tw-border-style);
    border-left-width: 1px;
  }
  .border-dashed {
    --tw-border-style: dashed;
    border-style: dashed;
  }
  .border-\[\#2873bf\] {
    border-color: #2873bf;
  }
  .border-\[\#155290\] {
    border-color: #155290;
  }
  .border-\[\#155797\] {
    border-color: #155797;
  }
  .border-\[\#b5cee6\] {
    border-color: #b5cee6;
  }
  .border-\[\#bdcbe9\] {
    border-color: #bdcbe9;
  }
  .border-\[\#c4d7ff\] {
    border-color: #c4d7ff;
  }
  .border-\[\#d8e2ff\] {
    border-color: #d8e2ff;
  }
  .border-\[\#dae9ff\] {
    border-color: #dae9ff;
  }
  .border-\[\#dfe7ff\] {
    border-color: #dfe7ff;
  }
  .border-\[\#e0e0ff\] {
    border-color: #e0e0ff;
  }
  .border-\[\#e0eaff\] {
    border-color: #e0eaff;
  }
  .border-\[\#e6eeff\] {
    border-color: #e6eeff;
  }
  .border-\[\#f08411\] {
    border-color: #f08411;
  }
  .border-\[var\(--border-color\)\] {
    border-color: var(--border-color);
  }
  .border-white {
    border-color: var(--color-white);
  }
  .bg-\[\#105eb3\] {
    background-color: #105eb3;
  }
  .bg-\[\#2479ff\] {
    background-color: #2479ff;
  }
  .bg-\[\#155290\] {
    background-color: #155290;
  }
  .bg-\[\#155797\] {
    background-color: #155797;
  }
  .bg-\[\#dae9ff\] {
    background-color: #dae9ff;
  }
  .bg-\[\#ddd\] {
    background-color: #ddd;
  }
  .bg-\[\#e0eaff\] {
    background-color: #e0eaff;
  }
  .bg-\[\#e6e6e6\] {
    background-color: #e6e6e6;
  }
  .bg-\[\#ec8613\] {
    background-color: #ec8613;
  }
  .bg-\[\#eeeeee\] {
    background-color: #eeeeee;
  }
  .bg-\[\#f5f9ff\] {
    background-color: #f5f9ff;
  }
  .bg-\[\#f6f9ff\] {
    background-color: #f6f9ff;
  }
  .bg-\[\#f8fdff\] {
    background-color: #f8fdff;
  }
  .bg-\[\#f08411\] {
    background-color: #f08411;
  }
  .bg-\[\#fafbff\] {
    background-color: #fafbff;
  }
  .bg-\[\#ff0000\] {
    background-color: #ff0000;
  }
  .bg-\[\#ffefdd\] {
    background-color: #ffefdd;
  }
  .bg-\[\#fff\] {
    background-color: #fff;
  }
  .bg-\[\#ffffff\] {
    background-color: #ffffff;
  }
  .bg-\[rgba\(21\,82\,144\,\.5\)\] {
    background-color: rgba(21,82,144,.5);
  }
  .bg-\[rgba\(21\,82\,144\,0\.5\)\] {
    background-color: rgba(21,82,144,0.5);
  }
  .bg-\[rgba\(21\,87\,151\,\.5\)\] {
    background-color: rgba(21,87,151,.5);
  }
  .bg-\[rgba\(21\,87\,151\,0\.1\)\] {
    background-color: rgba(21,87,151,0.1);
  }
  .bg-\[rgba\(102\,102\,102\,0\.1\)\] {
    background-color: rgba(102,102,102,0.1);
  }
  .bg-\[rgba\(208\,221\,234\,0\.2\)\] {
    background-color: rgba(208,221,234,0.2);
  }
  .bg-\[rgba\(255\,255\,255\,\.5\)\] {
    background-color: rgba(255,255,255,.5);
  }
  .bg-\[rgba\(255\,255\,255\,0\.3\)\] {
    background-color: rgba(255,255,255,0.3);
  }
  .bg-transparent {
    background-color: transparent;
  }
  .bg-white {
    background-color: var(--color-white);
  }
  .bg-\[url\(\'__IMG__\/icons\/changjiantou-zuoshang2\.png\'\)\] {
    background-image: url('__IMG__/icons/changjiantou-zuoshang2.png');
  }
  .bg-\[url\(\/images\/backgrounds\/m_bj\.jpg\)\] {
    background-image: url(/images/backgrounds/m_bj.jpg);
  }
  .bg-\[url\(\/images\/faq\/bg_m\.jpg\)\] {
    background-image: url(/images/faq/bg_m.jpg);
  }
  .bg-\[url\(\/images\/icons\/close\.png\)\] {
    background-image: url(/images/icons/close.png);
  }
  .bg-\[url\(\/images\/icons\/dow\.png\)\] {
    background-image: url(/images/icons/dow.png);
  }
  .bg-\[url\(\/images\/icons\/menu\.png\)\] {
    background-image: url(/images/icons/menu.png);
  }
  .bg-\[url\(\/images\/icons\/sousuo\.png\)\] {
    background-image: url(/images/icons/sousuo.png);
  }
  .bg-\[url\(\/images\/icons\/wenti\.png\)\] {
    background-image: url(/images/icons/wenti.png);
  }
  .bg-\[url\(\/images\/icons\/yonghu\.png\)\] {
    background-image: url(/images/icons/yonghu.png);
  }
  .bg-\[url\(\/static\/home\/<USER>\/backgrounds\/bg_01\.jpg\)\] {
    background-image: url(/static/home/<USER>/backgrounds/bg_01.jpg);
  }
  .bg-\[url\(\/static\/home\/<USER>\/backgrounds\/m_bj\.jpg\)\] {
    background-image: url(/static/home/<USER>/backgrounds/m_bj.jpg);
  }
  .bg-\[url\(\/static\/home\/<USER>\/backgrounds\/m_case_01_bj\.jpg\)\] {
    background-image: url(/static/home/<USER>/backgrounds/m_case_01_bj.jpg);
  }
  .bg-\[url\(\/static\/home\/<USER>\/backgrounds\/points-bg\.png\)\] {
    background-image: url(/static/home/<USER>/backgrounds/points-bg.png);
  }
  .bg-\[url\(\/static\/home\/<USER>\/icons\/banner_l\.png\)\] {
    background-image: url(/static/home/<USER>/icons/banner_l.png);
  }
  .bg-\[url\(\/static\/home\/<USER>\/icons\/banner_r\.png\)\] {
    background-image: url(/static/home/<USER>/icons/banner_r.png);
  }
  .bg-\[url\(\/static\/home\/<USER>\/icons\/chengshi\.png\)\] {
    background-image: url(/static/home/<USER>/icons/chengshi.png);
  }
  .bg-\[url\(\/static\/home\/<USER>\/icons\/close\.png\)\] {
    background-image: url(/static/home/<USER>/icons/close.png);
  }
  .bg-\[url\(\/static\/home\/<USER>\/icons\/dow\.png\)\] {
    background-image: url(/static/home/<USER>/icons/dow.png);
  }
  .bg-\[url\(\/static\/home\/<USER>\/icons\/icon_address\.png\)\] {
    background-image: url(/static/home/<USER>/icons/icon_address.png);
  }
  .bg-\[url\(\/static\/home\/<USER>\/icons\/jigouguanli\.png\)\] {
    background-image: url(/static/home/<USER>/icons/jigouguanli.png);
  }
  .bg-\[url\(\/static\/home\/<USER>\/icons\/menu\.png\)\] {
    background-image: url(/static/home/<USER>/icons/menu.png);
  }
  .bg-\[url\(\/static\/home\/<USER>\/icons\/mima\.png\)\] {
    background-image: url(/static/home/<USER>/icons/mima.png);
  }
  .bg-\[url\(\/static\/home\/<USER>\/icons\/mti-jiantouyou\.png\)\] {
    background-image: url(/static/home/<USER>/icons/mti-jiantouyou.png);
  }
  .bg-\[url\(\/static\/home\/<USER>\/icons\/news\.png\)\] {
    background-image: url(/static/home/<USER>/icons/news.png);
  }
  .bg-\[url\(\/static\/home\/<USER>\/icons\/phone\.png\)\] {
    background-image: url(/static/home/<USER>/icons/phone.png);
  }
  .bg-\[url\(\/static\/home\/<USER>\/icons\/shanchu\.png\)\] {
    background-image: url(/static/home/<USER>/icons/shanchu.png);
  }
  .bg-\[url\(\/static\/home\/<USER>\/icons\/sousuo\.png\)\] {
    background-image: url(/static/home/<USER>/icons/sousuo.png);
  }
  .bg-\[url\(\/static\/home\/<USER>\/icons\/yanjing_yincang_o\.png\)\] {
    background-image: url(/static/home/<USER>/icons/yanjing_yincang_o.png);
  }
  .bg-\[url\(\/static\/home\/<USER>\/icons\/yanzhengma\.png\)\] {
    background-image: url(/static/home/<USER>/icons/yanzhengma.png);
  }
  .bg-\[url\(\/static\/home\/<USER>\/icons\/yonghu-1\.png\)\] {
    background-image: url(/static/home/<USER>/icons/yonghu-1.png);
  }
  .bg-\[url\(\/static\/home\/<USER>\/icons\/yonghu\.png\)\] {
    background-image: url(/static/home/<USER>/icons/yonghu.png);
  }
  .bg-\[url\(\/static\/home\/<USER>\/icons\/youxiang\.png\)\] {
    background-image: url(/static/home/<USER>/icons/youxiang.png);
  }
  .bg-\[url\(\/static\/home\/<USER>\/icons\/youzhengbianma\.png\)\] {
    background-image: url(/static/home/<USER>/icons/youzhengbianma.png);
  }
  .bg-\[url\(\/static\/home\/<USER>\/icons\/zhiweimoban\.png\)\] {
    background-image: url(/static/home/<USER>/icons/zhiweimoban.png);
  }
  .bg-\[url\(\/static\/home\/<USER>\/img_01\.jpg\)\] {
    background-image: url(/static/home/<USER>/img_01.jpg);
  }
  .bg-\[url\(__IMG__\/backgrounds\/bg_01\.jpg\)\] {
    background-image: url(__IMG__/backgrounds/bg_01.jpg);
  }
  .bg-\[url\(__IMG__\/backgrounds\/m_bj\.jpg\)\] {
    background-image: url(__IMG__/backgrounds/m_bj.jpg);
  }
  .bg-\[url\(__IMG__\/backgrounds\/m_case_01_bj\.jpg\)\] {
    background-image: url(__IMG__/backgrounds/m_case_01_bj.jpg);
  }
  .bg-\[url\(__IMG__\/backgrounds\/points-bg\.png\)\] {
    background-image: url(__IMG__/backgrounds/points-bg.png);
  }
  .bg-\[url\(__IMG__\/faq\/bg_m\.jpg\)\] {
    background-image: url(__IMG__/faq/bg_m.jpg);
  }
  .bg-\[url\(__IMG__\/icons\/banner_l\.png\)\] {
    background-image: url(__IMG__/icons/banner_l.png);
  }
  .bg-\[url\(__IMG__\/icons\/banner_r\.png\)\] {
    background-image: url(__IMG__/icons/banner_r.png);
  }
  .bg-\[url\(__IMG__\/icons\/changjiantou-zuoshang1\.png\)\] {
    background-image: url(__IMG__/icons/changjiantou-zuoshang1.png);
  }
  .bg-\[url\(__IMG__\/icons\/chengshi\.png\)\] {
    background-image: url(__IMG__/icons/chengshi.png);
  }
  .bg-\[url\(__IMG__\/icons\/close\.png\)\] {
    background-image: url(__IMG__/icons/close.png);
  }
  .bg-\[url\(__IMG__\/icons\/dow\.png\)\] {
    background-image: url(__IMG__/icons/dow.png);
  }
  .bg-\[url\(__IMG__\/icons\/fangda\.png\)\] {
    background-image: url(__IMG__/icons/fangda.png);
  }
  .bg-\[url\(__IMG__\/icons\/fuwu\.png\)\] {
    background-image: url(__IMG__/icons/fuwu.png);
  }
  .bg-\[url\(__IMG__\/icons\/icon_address\.png\)\] {
    background-image: url(__IMG__/icons/icon_address.png);
  }
  .bg-\[url\(__IMG__\/icons\/jigouguanli\.png\)\] {
    background-image: url(__IMG__/icons/jigouguanli.png);
  }
  .bg-\[url\(__IMG__\/icons\/jurassic_message\.png\)\] {
    background-image: url(__IMG__/icons/jurassic_message.png);
  }
  .bg-\[url\(__IMG__\/icons\/menu\.png\)\] {
    background-image: url(__IMG__/icons/menu.png);
  }
  .bg-\[url\(__IMG__\/icons\/mima\.png\)\] {
    background-image: url(__IMG__/icons/mima.png);
  }
  .bg-\[url\(__IMG__\/icons\/mti-jiantouyou\.png\)\] {
    background-image: url(__IMG__/icons/mti-jiantouyou.png);
  }
  .bg-\[url\(__IMG__\/icons\/mti-jiantouyou1\.png\)\] {
    background-image: url(__IMG__/icons/mti-jiantouyou1.png);
  }
  .bg-\[url\(__IMG__\/icons\/news\.png\)\] {
    background-image: url(__IMG__/icons/news.png);
  }
  .bg-\[url\(__IMG__\/icons\/phone\.png\)\] {
    background-image: url(__IMG__/icons/phone.png);
  }
  .bg-\[url\(__IMG__\/icons\/sanweimoxing\.png\)\] {
    background-image: url(__IMG__/icons/sanweimoxing.png);
  }
  .bg-\[url\(__IMG__\/icons\/shanchu\.png\)\] {
    background-image: url(__IMG__/icons/shanchu.png);
  }
  .bg-\[url\(__IMG__\/icons\/sousuo\.png\)\] {
    background-image: url(__IMG__/icons/sousuo.png);
  }
  .bg-\[url\(__IMG__\/icons\/wenti\.png\)\] {
    background-image: url(__IMG__/icons/wenti.png);
  }
  .bg-\[url\(__IMG__\/icons\/yanjing_yincang_o\.png\)\] {
    background-image: url(__IMG__/icons/yanjing_yincang_o.png);
  }
  .bg-\[url\(__IMG__\/icons\/yanzhengma\.png\)\] {
    background-image: url(__IMG__/icons/yanzhengma.png);
  }
  .bg-\[url\(__IMG__\/icons\/yonghu-1\.png\)\] {
    background-image: url(__IMG__/icons/yonghu-1.png);
  }
  .bg-\[url\(__IMG__\/icons\/yonghu\.png\)\] {
    background-image: url(__IMG__/icons/yonghu.png);
  }
  .bg-\[url\(__IMG__\/icons\/youxiang\.png\)\] {
    background-image: url(__IMG__/icons/youxiang.png);
  }
  .bg-\[url\(__IMG__\/icons\/youzhengbianma\.png\)\] {
    background-image: url(__IMG__/icons/youzhengbianma.png);
  }
  .bg-\[url\(__IMG__\/icons\/zhiweimoban\.png\)\] {
    background-image: url(__IMG__/icons/zhiweimoban.png);
  }
  .bg-\[url\(__IMG__\/img_01\.jpg\)\] {
    background-image: url(__IMG__/img_01.jpg);
  }
  .bg-cover {
    background-size: cover;
  }
  .bg-size-\[\.95rem\] {
    background-size: .95rem;
  }
  .bg-size-\[\.875rem\] {
    background-size: .875rem;
  }
  .bg-size-\[0\.5rem_0\.75rem\] {
    background-size: 0.5rem 0.75rem;
  }
  .bg-size-\[1\.25rem\] {
    background-size: 1.25rem;
  }
  .bg-size-\[1\.125rem\] {
    background-size: 1.125rem;
  }
  .bg-size-\[1rem\] {
    background-size: 1rem;
  }
  .bg-size-\[1rem_1rem\] {
    background-size: 1rem 1rem;
  }
  .bg-size-\[13px\] {
    background-size: 13px;
  }
  .bg-size-\[16px\] {
    background-size: 16px;
  }
  .bg-size-\[26px\] {
    background-size: 26px;
  }
  .bg-size-\[40\%\] {
    background-size: 40%;
  }
  .bg-size-\[100\%\] {
    background-size: 100%;
  }
  .bg-size-\[100\%_100\%\] {
    background-size: 100% 100%;
  }
  .bg-\[\.75rem_\.75rem\] {
    background-position: .75rem .75rem;
  }
  .bg-\[\.75rem_center\] {
    background-position: .75rem center;
  }
  .bg-\[1\.3rem_center\] {
    background-position: 1.3rem center;
  }
  .bg-\[2rem_center\] {
    background-position: 2rem center;
  }
  .bg-\[12px_center\] {
    background-position: 12px center;
  }
  .bg-\[85\%_center\] {
    background-position: 85% center;
  }
  .bg-\[90\%_bottom\] {
    background-position: 90% bottom;
  }
  .bg-center {
    background-position: center;
  }
  .bg-no-repeat {
    background-repeat: no-repeat;
  }
  .object-cover {
    object-fit: cover;
  }
  .p-1 {
    padding: calc(var(--spacing) * 1);
  }
  .p-2 {
    padding: calc(var(--spacing) * 2);
  }
  .p-3 {
    padding: calc(var(--spacing) * 3);
  }
  .p-4 {
    padding: calc(var(--spacing) * 4);
  }
  .p-5 {
    padding: calc(var(--spacing) * 5);
  }
  .p-6 {
    padding: calc(var(--spacing) * 6);
  }
  .p-8 {
    padding: calc(var(--spacing) * 8);
  }
  .p-\[1\.25rem\] {
    padding: 1.25rem;
  }
  .p-\[1\.875rem\] {
    padding: 1.875rem;
  }
  .px-1 {
    padding-inline: calc(var(--spacing) * 1);
  }
  .px-1\.5 {
    padding-inline: calc(var(--spacing) * 1.5);
  }
  .px-2 {
    padding-inline: calc(var(--spacing) * 2);
  }
  .px-2\.5 {
    padding-inline: calc(var(--spacing) * 2.5);
  }
  .px-3 {
    padding-inline: calc(var(--spacing) * 3);
  }
  .px-4 {
    padding-inline: calc(var(--spacing) * 4);
  }
  .px-5 {
    padding-inline: calc(var(--spacing) * 5);
  }
  .px-6 {
    padding-inline: calc(var(--spacing) * 6);
  }
  .px-8 {
    padding-inline: calc(var(--spacing) * 8);
  }
  .px-10 {
    padding-inline: calc(var(--spacing) * 10);
  }
  .px-\[\.625rem\] {
    padding-inline: .625rem;
  }
  .px-\[\.9375rem\] {
    padding-inline: .9375rem;
  }
  .py-0\.5 {
    padding-block: calc(var(--spacing) * 0.5);
  }
  .py-1 {
    padding-block: calc(var(--spacing) * 1);
  }
  .py-1\.5 {
    padding-block: calc(var(--spacing) * 1.5);
  }
  .py-2 {
    padding-block: calc(var(--spacing) * 2);
  }
  .py-2\.5 {
    padding-block: calc(var(--spacing) * 2.5);
  }
  .py-3 {
    padding-block: calc(var(--spacing) * 3);
  }
  .py-3\.5 {
    padding-block: calc(var(--spacing) * 3.5);
  }
  .py-4 {
    padding-block: calc(var(--spacing) * 4);
  }
  .py-5 {
    padding-block: calc(var(--spacing) * 5);
  }
  .py-6 {
    padding-block: calc(var(--spacing) * 6);
  }
  .py-8 {
    padding-block: calc(var(--spacing) * 8);
  }
  .py-10 {
    padding-block: calc(var(--spacing) * 10);
  }
  .py-\[\.25rem\] {
    padding-block: .25rem;
  }
  .py-\[\.75rem\] {
    padding-block: .75rem;
  }
  .py-\[\.375rem\] {
    padding-block: .375rem;
  }
  .py-\[\.625rem\] {
    padding-block: .625rem;
  }
  .pt-1 {
    padding-top: calc(var(--spacing) * 1);
  }
  .pt-3 {
    padding-top: calc(var(--spacing) * 3);
  }
  .pt-4 {
    padding-top: calc(var(--spacing) * 4);
  }
  .pt-5 {
    padding-top: calc(var(--spacing) * 5);
  }
  .pt-8 {
    padding-top: calc(var(--spacing) * 8);
  }
  .pr-2 {
    padding-right: calc(var(--spacing) * 2);
  }
  .pr-6 {
    padding-right: calc(var(--spacing) * 6);
  }
  .pr-10 {
    padding-right: calc(var(--spacing) * 10);
  }
  .pr-12 {
    padding-right: calc(var(--spacing) * 12);
  }
  .pr-20 {
    padding-right: calc(var(--spacing) * 20);
  }
  .pr-\[\.625rem\] {
    padding-right: .625rem;
  }
  .pb-2 {
    padding-bottom: calc(var(--spacing) * 2);
  }
  .pb-3 {
    padding-bottom: calc(var(--spacing) * 3);
  }
  .pb-4 {
    padding-bottom: calc(var(--spacing) * 4);
  }
  .pb-5 {
    padding-bottom: calc(var(--spacing) * 5);
  }
  .pb-6 {
    padding-bottom: calc(var(--spacing) * 6);
  }
  .pb-8 {
    padding-bottom: calc(var(--spacing) * 8);
  }
  .pb-10 {
    padding-bottom: calc(var(--spacing) * 10);
  }
  .pb-11 {
    padding-bottom: calc(var(--spacing) * 11);
  }
  .pl-3 {
    padding-left: calc(var(--spacing) * 3);
  }
  .pl-5 {
    padding-left: calc(var(--spacing) * 5);
  }
  .pl-6 {
    padding-left: calc(var(--spacing) * 6);
  }
  .pl-10 {
    padding-left: calc(var(--spacing) * 10);
  }
  .pl-12 {
    padding-left: calc(var(--spacing) * 12);
  }
  .text-center {
    text-align: center;
  }
  .text-left {
    text-align: left;
  }
  .align-middle {
    vertical-align: middle;
  }
  .align-text-top {
    vertical-align: text-top;
  }
  .text-2xl {
    font-size: var(--text-2xl);
    line-height: var(--tw-leading, var(--text-2xl--line-height));
  }
  .text-3xl {
    font-size: var(--text-3xl);
    line-height: var(--tw-leading, var(--text-3xl--line-height));
  }
  .text-4xl {
    font-size: var(--text-4xl);
    line-height: var(--tw-leading, var(--text-4xl--line-height));
  }
  .text-6xl {
    font-size: var(--text-6xl);
    line-height: var(--tw-leading, var(--text-6xl--line-height));
  }
  .text-9xl {
    font-size: var(--text-9xl);
    line-height: var(--tw-leading, var(--text-9xl--line-height));
  }
  .text-base {
    font-size: var(--text-base);
    line-height: var(--tw-leading, var(--text-base--line-height));
  }
  .text-lg {
    font-size: var(--text-lg);
    line-height: var(--tw-leading, var(--text-lg--line-height));
  }
  .text-sm {
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
  }
  .text-xl {
    font-size: var(--text-xl);
    line-height: var(--tw-leading, var(--text-xl--line-height));
  }
  .text-xs {
    font-size: var(--text-xs);
    line-height: var(--tw-leading, var(--text-xs--line-height));
  }
  .text-\[\.75rem\] {
    font-size: .75rem;
  }
  .text-\[0\.75rem\] {
    font-size: 0.75rem;
  }
  .leading-6 {
    --tw-leading: calc(var(--spacing) * 6);
    line-height: calc(var(--spacing) * 6);
  }
  .leading-\[1\.5\] {
    --tw-leading: 1.5;
    line-height: 1.5;
  }
  .leading-\[3\.125rem\] {
    --tw-leading: 3.125rem;
    line-height: 3.125rem;
  }
  .leading-\[50px\] {
    --tw-leading: 50px;
    line-height: 50px;
  }
  .font-bold {
    --tw-font-weight: var(--font-weight-bold);
    font-weight: var(--font-weight-bold);
  }
  .text-wrap {
    text-wrap: wrap;
  }
  .whitespace-nowrap {
    white-space: nowrap;
  }
  .text-\[\#000\] {
    color: #000;
  }
  .text-\[\#1f1f1f\] {
    color: #1f1f1f;
  }
  .text-\[\#111\] {
    color: #111;
  }
  .text-\[\#333\] {
    color: #333;
  }
  .text-\[\#666\] {
    color: #666;
  }
  .text-\[\#999\] {
    color: #999;
  }
  .text-\[\#030000\] {
    color: #030000;
  }
  .text-\[\#111111\] {
    color: #111111;
  }
  .text-\[\#155290\] {
    color: #155290;
  }
  .text-\[\#155797\] {
    color: #155797;
  }
  .text-\[\#333333\] {
    color: #333333;
  }
  .text-\[\#999999\] {
    color: #999999;
  }
  .text-\[\#ddd\] {
    color: #ddd;
  }
  .text-\[\#f01111\] {
    color: #f01111;
  }
  .text-\[\#f08411\] {
    color: #f08411;
  }
  .text-\[\#feffff\] {
    color: #feffff;
  }
  .text-\[\#ff0000\] {
    color: #ff0000;
  }
  .text-\[\#fff\] {
    color: #fff;
  }
  .text-red-500 {
    color: var(--color-red-500);
  }
  .text-transparent {
    color: transparent;
  }
  .text-white {
    color: var(--color-white);
  }
  .uppercase {
    text-transform: uppercase;
  }
  .underline {
    text-decoration-line: underline;
  }
  .underline-offset-4 {
    text-underline-offset: 4px;
  }
  .opacity-0 {
    opacity: 0%;
  }
  .opacity-50 {
    opacity: 50%;
  }
  .opacity-75 {
    opacity: 75%;
  }
  .shadow-2xl {
    --tw-shadow: 0 25px 50px -12px var(--tw-shadow-color, rgb(0 0 0 / 0.25));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-2xs {
    --tw-shadow: 0 1px var(--tw-shadow-color, rgb(0 0 0 / 0.05));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-lg {
    --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 4px 6px -4px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .transition-all {
    transition-property: all;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-colors {
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .group-hover\:right-2 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        right: calc(var(--spacing) * 2);
      }
    }
  }
  .group-hover\:right-5 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        right: calc(var(--spacing) * 5);
      }
    }
  }
  .group-hover\:block {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        display: block;
      }
    }
  }
  .group-hover\:rotate-180 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        rotate: 180deg;
      }
    }
  }
  .group-hover\:opacity-45 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        opacity: 45%;
      }
    }
  }
  .group-hover\:opacity-100 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        opacity: 100%;
      }
    }
  }
  .hover\:bg-\[\#2479ff\] {
    &:hover {
      @media (hover: hover) {
        background-color: #2479ff;
      }
    }
  }
  .hover\:bg-\[\#155290\] {
    &:hover {
      @media (hover: hover) {
        background-color: #155290;
      }
    }
  }
  .hover\:bg-gray-200 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-gray-200);
      }
    }
  }
  .hover\:text-white {
    &:hover {
      @media (hover: hover) {
        color: var(--color-white);
      }
    }
  }
  .focus\:border-\[\#dae9ff\] {
    &:focus {
      border-color: #dae9ff;
    }
  }
  .md\:absolute {
    @media (width >= 48rem) {
      position: absolute;
    }
  }
  .md\:fixed {
    @media (width >= 48rem) {
      position: fixed;
    }
  }
  .md\:relative {
    @media (width >= 48rem) {
      position: relative;
    }
  }
  .md\:top-0 {
    @media (width >= 48rem) {
      top: calc(var(--spacing) * 0);
    }
  }
  .md\:top-1\/2 {
    @media (width >= 48rem) {
      top: calc(1/2 * 100%);
    }
  }
  .md\:top-1\/3 {
    @media (width >= 48rem) {
      top: calc(1/3 * 100%);
    }
  }
  .md\:top-5 {
    @media (width >= 48rem) {
      top: calc(var(--spacing) * 5);
    }
  }
  .md\:top-9 {
    @media (width >= 48rem) {
      top: calc(var(--spacing) * 9);
    }
  }
  .md\:top-14 {
    @media (width >= 48rem) {
      top: calc(var(--spacing) * 14);
    }
  }
  .md\:top-24 {
    @media (width >= 48rem) {
      top: calc(var(--spacing) * 24);
    }
  }
  .md\:right-0 {
    @media (width >= 48rem) {
      right: calc(var(--spacing) * 0);
    }
  }
  .md\:right-1\/12 {
    @media (width >= 48rem) {
      right: calc(1/12 * 100%);
    }
  }
  .md\:right-2 {
    @media (width >= 48rem) {
      right: calc(var(--spacing) * 2);
    }
  }
  .md\:right-3 {
    @media (width >= 48rem) {
      right: calc(var(--spacing) * 3);
    }
  }
  .md\:right-4 {
    @media (width >= 48rem) {
      right: calc(var(--spacing) * 4);
    }
  }
  .md\:right-5 {
    @media (width >= 48rem) {
      right: calc(var(--spacing) * 5);
    }
  }
  .md\:right-10 {
    @media (width >= 48rem) {
      right: calc(var(--spacing) * 10);
    }
  }
  .md\:right-16 {
    @media (width >= 48rem) {
      right: calc(var(--spacing) * 16);
    }
  }
  .md\:right-\[4\.375rem\] {
    @media (width >= 48rem) {
      right: 4.375rem;
    }
  }
  .md\:right-\[5px\] {
    @media (width >= 48rem) {
      right: 5px;
    }
  }
  .md\:bottom-7 {
    @media (width >= 48rem) {
      bottom: calc(var(--spacing) * 7);
    }
  }
  .md\:bottom-10 {
    @media (width >= 48rem) {
      bottom: calc(var(--spacing) * 10);
    }
  }
  .md\:bottom-16 {
    @media (width >= 48rem) {
      bottom: calc(var(--spacing) * 16);
    }
  }
  .md\:-left-10 {
    @media (width >= 48rem) {
      left: calc(var(--spacing) * -10);
    }
  }
  .md\:-left-\[100\%\] {
    @media (width >= 48rem) {
      left: calc(100% * -1);
    }
  }
  .md\:-left-full {
    @media (width >= 48rem) {
      left: -100%;
    }
  }
  .md\:left-1\/2 {
    @media (width >= 48rem) {
      left: calc(1/2 * 100%);
    }
  }
  .md\:left-9 {
    @media (width >= 48rem) {
      left: calc(var(--spacing) * 9);
    }
  }
  .md\:left-10 {
    @media (width >= 48rem) {
      left: calc(var(--spacing) * 10);
    }
  }
  .md\:left-full {
    @media (width >= 48rem) {
      left: 100%;
    }
  }
  .md\:-z-20 {
    @media (width >= 48rem) {
      z-index: calc(20 * -1);
    }
  }
  .md\:order-1 {
    @media (width >= 48rem) {
      order: 1;
    }
  }
  .md\:order-2 {
    @media (width >= 48rem) {
      order: 2;
    }
  }
  .md\:col-span-2 {
    @media (width >= 48rem) {
      grid-column: span 2 / span 2;
    }
  }
  .md\:float-right {
    @media (width >= 48rem) {
      float: right;
    }
  }
  .md\:m-0 {
    @media (width >= 48rem) {
      margin: calc(var(--spacing) * 0);
    }
  }
  .md\:m-4 {
    @media (width >= 48rem) {
      margin: calc(var(--spacing) * 4);
    }
  }
  .md\:mx-0 {
    @media (width >= 48rem) {
      margin-inline: calc(var(--spacing) * 0);
    }
  }
  .md\:mx-auto {
    @media (width >= 48rem) {
      margin-inline: auto;
    }
  }
  .md\:my-10 {
    @media (width >= 48rem) {
      margin-block: calc(var(--spacing) * 10);
    }
  }
  .md\:mt-0 {
    @media (width >= 48rem) {
      margin-top: calc(var(--spacing) * 0);
    }
  }
  .md\:mt-2\.5 {
    @media (width >= 48rem) {
      margin-top: calc(var(--spacing) * 2.5);
    }
  }
  .md\:mt-3 {
    @media (width >= 48rem) {
      margin-top: calc(var(--spacing) * 3);
    }
  }
  .md\:mt-5 {
    @media (width >= 48rem) {
      margin-top: calc(var(--spacing) * 5);
    }
  }
  .md\:mt-10 {
    @media (width >= 48rem) {
      margin-top: calc(var(--spacing) * 10);
    }
  }
  .md\:mt-\[1\.875rem\] {
    @media (width >= 48rem) {
      margin-top: 1.875rem;
    }
  }
  .md\:mt-\[2\.5rem\] {
    @media (width >= 48rem) {
      margin-top: 2.5rem;
    }
  }
  .md\:mt-\[3\.75rem\] {
    @media (width >= 48rem) {
      margin-top: 3.75rem;
    }
  }
  .md\:mr-0 {
    @media (width >= 48rem) {
      margin-right: calc(var(--spacing) * 0);
    }
  }
  .md\:mr-3\.5 {
    @media (width >= 48rem) {
      margin-right: calc(var(--spacing) * 3.5);
    }
  }
  .md\:mr-5 {
    @media (width >= 48rem) {
      margin-right: calc(var(--spacing) * 5);
    }
  }
  .md\:mr-\[2\.1875rem\] {
    @media (width >= 48rem) {
      margin-right: 2.1875rem;
    }
  }
  .md\:mb-0 {
    @media (width >= 48rem) {
      margin-bottom: calc(var(--spacing) * 0);
    }
  }
  .md\:mb-3 {
    @media (width >= 48rem) {
      margin-bottom: calc(var(--spacing) * 3);
    }
  }
  .md\:mb-5 {
    @media (width >= 48rem) {
      margin-bottom: calc(var(--spacing) * 5);
    }
  }
  .md\:mb-6 {
    @media (width >= 48rem) {
      margin-bottom: calc(var(--spacing) * 6);
    }
  }
  .md\:mb-7 {
    @media (width >= 48rem) {
      margin-bottom: calc(var(--spacing) * 7);
    }
  }
  .md\:mb-8 {
    @media (width >= 48rem) {
      margin-bottom: calc(var(--spacing) * 8);
    }
  }
  .md\:mb-10 {
    @media (width >= 48rem) {
      margin-bottom: calc(var(--spacing) * 10);
    }
  }
  .md\:mb-11 {
    @media (width >= 48rem) {
      margin-bottom: calc(var(--spacing) * 11);
    }
  }
  .md\:mb-12 {
    @media (width >= 48rem) {
      margin-bottom: calc(var(--spacing) * 12);
    }
  }
  .md\:mb-14 {
    @media (width >= 48rem) {
      margin-bottom: calc(var(--spacing) * 14);
    }
  }
  .md\:mb-16 {
    @media (width >= 48rem) {
      margin-bottom: calc(var(--spacing) * 16);
    }
  }
  .md\:mb-20 {
    @media (width >= 48rem) {
      margin-bottom: calc(var(--spacing) * 20);
    }
  }
  .md\:mb-\[1\.25rem\] {
    @media (width >= 48rem) {
      margin-bottom: 1.25rem;
    }
  }
  .md\:mb-\[1\.875rem\] {
    @media (width >= 48rem) {
      margin-bottom: 1.875rem;
    }
  }
  .md\:mb-\[1\.5625rem\] {
    @media (width >= 48rem) {
      margin-bottom: 1.5625rem;
    }
  }
  .md\:mb-\[2\.5rem\] {
    @media (width >= 48rem) {
      margin-bottom: 2.5rem;
    }
  }
  .md\:mb-\[3\.75rem\] {
    @media (width >= 48rem) {
      margin-bottom: 3.75rem;
    }
  }
  .md\:mb-\[4\.0625rem\] {
    @media (width >= 48rem) {
      margin-bottom: 4.0625rem;
    }
  }
  .md\:ml-2 {
    @media (width >= 48rem) {
      margin-left: calc(var(--spacing) * 2);
    }
  }
  .md\:ml-2\.5 {
    @media (width >= 48rem) {
      margin-left: calc(var(--spacing) * 2.5);
    }
  }
  .md\:ml-3\.5 {
    @media (width >= 48rem) {
      margin-left: calc(var(--spacing) * 3.5);
    }
  }
  .md\:line-clamp-6 {
    @media (width >= 48rem) {
      overflow: hidden;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 6;
    }
  }
  .md\:block {
    @media (width >= 48rem) {
      display: block;
    }
  }
  .md\:flex {
    @media (width >= 48rem) {
      display: flex;
    }
  }
  .md\:grid {
    @media (width >= 48rem) {
      display: grid;
    }
  }
  .md\:hidden {
    @media (width >= 48rem) {
      display: none;
    }
  }
  .md\:inline-block {
    @media (width >= 48rem) {
      display: inline-block;
    }
  }
  .md\:h-6 {
    @media (width >= 48rem) {
      height: calc(var(--spacing) * 6);
    }
  }
  .md\:h-7 {
    @media (width >= 48rem) {
      height: calc(var(--spacing) * 7);
    }
  }
  .md\:h-10 {
    @media (width >= 48rem) {
      height: calc(var(--spacing) * 10);
    }
  }
  .md\:h-12 {
    @media (width >= 48rem) {
      height: calc(var(--spacing) * 12);
    }
  }
  .md\:h-14 {
    @media (width >= 48rem) {
      height: calc(var(--spacing) * 14);
    }
  }
  .md\:h-16 {
    @media (width >= 48rem) {
      height: calc(var(--spacing) * 16);
    }
  }
  .md\:h-20 {
    @media (width >= 48rem) {
      height: calc(var(--spacing) * 20);
    }
  }
  .md\:h-24 {
    @media (width >= 48rem) {
      height: calc(var(--spacing) * 24);
    }
  }
  .md\:h-28 {
    @media (width >= 48rem) {
      height: calc(var(--spacing) * 28);
    }
  }
  .md\:h-\[\.625rem\] {
    @media (width >= 48rem) {
      height: .625rem;
    }
  }
  .md\:h-\[1\.25rem\] {
    @media (width >= 48rem) {
      height: 1.25rem;
    }
  }
  .md\:h-\[1\.375rem\] {
    @media (width >= 48rem) {
      height: 1.375rem;
    }
  }
  .md\:h-\[1\.1875rem\] {
    @media (width >= 48rem) {
      height: 1.1875rem;
    }
  }
  .md\:h-\[1\.5625rem\] {
    @media (width >= 48rem) {
      height: 1.5625rem;
    }
  }
  .md\:h-\[2\.5rem\] {
    @media (width >= 48rem) {
      height: 2.5rem;
    }
  }
  .md\:h-\[2\.375rem\] {
    @media (width >= 48rem) {
      height: 2.375rem;
    }
  }
  .md\:h-\[3\.75rem\] {
    @media (width >= 48rem) {
      height: 3.75rem;
    }
  }
  .md\:h-\[3\.125rem\] {
    @media (width >= 48rem) {
      height: 3.125rem;
    }
  }
  .md\:h-\[4\.375rem\] {
    @media (width >= 48rem) {
      height: 4.375rem;
    }
  }
  .md\:h-\[5\.625rem\] {
    @media (width >= 48rem) {
      height: 5.625rem;
    }
  }
  .md\:h-\[5rem\] {
    @media (width >= 48rem) {
      height: 5rem;
    }
  }
  .md\:h-\[6\.25rem\] {
    @media (width >= 48rem) {
      height: 6.25rem;
    }
  }
  .md\:h-\[8\.75rem\] {
    @media (width >= 48rem) {
      height: 8.75rem;
    }
  }
  .md\:h-\[9\.375rem\] {
    @media (width >= 48rem) {
      height: 9.375rem;
    }
  }
  .md\:h-\[10rem\] {
    @media (width >= 48rem) {
      height: 10rem;
    }
  }
  .md\:h-\[11\.25rem\] {
    @media (width >= 48rem) {
      height: 11.25rem;
    }
  }
  .md\:h-\[12\.5rem\] {
    @media (width >= 48rem) {
      height: 12.5rem;
    }
  }
  .md\:h-\[16\.25rem\] {
    @media (width >= 48rem) {
      height: 16.25rem;
    }
  }
  .md\:h-\[28\.875rem\] {
    @media (width >= 48rem) {
      height: 28.875rem;
    }
  }
  .md\:h-\[31\.25rem\] {
    @media (width >= 48rem) {
      height: 31.25rem;
    }
  }
  .md\:h-\[37\.4375rem\] {
    @media (width >= 48rem) {
      height: 37.4375rem;
    }
  }
  .md\:h-\[38px\] {
    @media (width >= 48rem) {
      height: 38px;
    }
  }
  .md\:h-\[42\.5rem\] {
    @media (width >= 48rem) {
      height: 42.5rem;
    }
  }
  .md\:h-\[51\.625rem\] {
    @media (width >= 48rem) {
      height: 51.625rem;
    }
  }
  .md\:h-\[60px\] {
    @media (width >= 48rem) {
      height: 60px;
    }
  }
  .md\:h-\[62\.5rem\] {
    @media (width >= 48rem) {
      height: 62.5rem;
    }
  }
  .md\:h-\[80px\] {
    @media (width >= 48rem) {
      height: 80px;
    }
  }
  .md\:h-\[90\%\] {
    @media (width >= 48rem) {
      height: 90%;
    }
  }
  .md\:h-auto {
    @media (width >= 48rem) {
      height: auto;
    }
  }
  .md\:h-full {
    @media (width >= 48rem) {
      height: 100%;
    }
  }
  .md\:h-lvh {
    @media (width >= 48rem) {
      height: 100lvh;
    }
  }
  .md\:max-h-\[25rem\] {
    @media (width >= 48rem) {
      max-height: 25rem;
    }
  }
  .md\:min-h-\[1\.875rem\] {
    @media (width >= 48rem) {
      min-height: 1.875rem;
    }
  }
  .md\:min-h-\[12\.5rem\] {
    @media (width >= 48rem) {
      min-height: 12.5rem;
    }
  }
  .md\:min-h-\[28\.125rem\] {
    @media (width >= 48rem) {
      min-height: 28.125rem;
    }
  }
  .md\:min-h-\[30rem\] {
    @media (width >= 48rem) {
      min-height: 30rem;
    }
  }
  .md\:min-h-\[35rem\] {
    @media (width >= 48rem) {
      min-height: 35rem;
    }
  }
  .md\:min-h-\[45\.375rem\] {
    @media (width >= 48rem) {
      min-height: 45.375rem;
    }
  }
  .md\:min-h-auto {
    @media (width >= 48rem) {
      min-height: auto;
    }
  }
  .md\:min-h-lh {
    @media (width >= 48rem) {
      min-height: 1lh;
    }
  }
  .md\:w-1\/3 {
    @media (width >= 48rem) {
      width: calc(1/3 * 100%);
    }
  }
  .md\:w-2\/3 {
    @media (width >= 48rem) {
      width: calc(2/3 * 100%);
    }
  }
  .md\:w-2\/5 {
    @media (width >= 48rem) {
      width: calc(2/5 * 100%);
    }
  }
  .md\:w-3\/5 {
    @media (width >= 48rem) {
      width: calc(3/5 * 100%);
    }
  }
  .md\:w-3\/6 {
    @media (width >= 48rem) {
      width: calc(3/6 * 100%);
    }
  }
  .md\:w-6 {
    @media (width >= 48rem) {
      width: calc(var(--spacing) * 6);
    }
  }
  .md\:w-7 {
    @media (width >= 48rem) {
      width: calc(var(--spacing) * 7);
    }
  }
  .md\:w-8\/12 {
    @media (width >= 48rem) {
      width: calc(8/12 * 100%);
    }
  }
  .md\:w-10 {
    @media (width >= 48rem) {
      width: calc(var(--spacing) * 10);
    }
  }
  .md\:w-10\/12 {
    @media (width >= 48rem) {
      width: calc(10/12 * 100%);
    }
  }
  .md\:w-12 {
    @media (width >= 48rem) {
      width: calc(var(--spacing) * 12);
    }
  }
  .md\:w-14 {
    @media (width >= 48rem) {
      width: calc(var(--spacing) * 14);
    }
  }
  .md\:w-20 {
    @media (width >= 48rem) {
      width: calc(var(--spacing) * 20);
    }
  }
  .md\:w-28 {
    @media (width >= 48rem) {
      width: calc(var(--spacing) * 28);
    }
  }
  .md\:w-32 {
    @media (width >= 48rem) {
      width: calc(var(--spacing) * 32);
    }
  }
  .md\:w-\[0\.75rem\] {
    @media (width >= 48rem) {
      width: 0.75rem;
    }
  }
  .md\:w-\[1\.25rem\] {
    @media (width >= 48rem) {
      width: 1.25rem;
    }
  }
  .md\:w-\[1\.0625rem\] {
    @media (width >= 48rem) {
      width: 1.0625rem;
    }
  }
  .md\:w-\[1\.1875rem\] {
    @media (width >= 48rem) {
      width: 1.1875rem;
    }
  }
  .md\:w-\[1\.5625rem\] {
    @media (width >= 48rem) {
      width: 1.5625rem;
    }
  }
  .md\:w-\[1\.8125rem\] {
    @media (width >= 48rem) {
      width: 1.8125rem;
    }
  }
  .md\:w-\[2\.375rem\] {
    @media (width >= 48rem) {
      width: 2.375rem;
    }
  }
  .md\:w-\[3\.75rem\] {
    @media (width >= 48rem) {
      width: 3.75rem;
    }
  }
  .md\:w-\[3\.125rem\] {
    @media (width >= 48rem) {
      width: 3.125rem;
    }
  }
  .md\:w-\[5rem\] {
    @media (width >= 48rem) {
      width: 5rem;
    }
  }
  .md\:w-\[7\.5rem\] {
    @media (width >= 48rem) {
      width: 7.5rem;
    }
  }
  .md\:w-\[8\.75rem\] {
    @media (width >= 48rem) {
      width: 8.75rem;
    }
  }
  .md\:w-\[8\.4375rem\] {
    @media (width >= 48rem) {
      width: 8.4375rem;
    }
  }
  .md\:w-\[9\.375rem\] {
    @media (width >= 48rem) {
      width: 9.375rem;
    }
  }
  .md\:w-\[10rem\] {
    @media (width >= 48rem) {
      width: 10rem;
    }
  }
  .md\:w-\[15\.625rem\] {
    @media (width >= 48rem) {
      width: 15.625rem;
    }
  }
  .md\:w-\[15rem\] {
    @media (width >= 48rem) {
      width: 15rem;
    }
  }
  .md\:w-\[16\.25rem\] {
    @media (width >= 48rem) {
      width: 16.25rem;
    }
  }
  .md\:w-\[17\.5rem\] {
    @media (width >= 48rem) {
      width: 17.5rem;
    }
  }
  .md\:w-\[18\.75rem\] {
    @media (width >= 48rem) {
      width: 18.75rem;
    }
  }
  .md\:w-\[19\.5rem\] {
    @media (width >= 48rem) {
      width: 19.5rem;
    }
  }
  .md\:w-\[21\.625rem\] {
    @media (width >= 48rem) {
      width: 21.625rem;
    }
  }
  .md\:w-\[23\.75rem\] {
    @media (width >= 48rem) {
      width: 23.75rem;
    }
  }
  .md\:w-\[25rem\] {
    @media (width >= 48rem) {
      width: 25rem;
    }
  }
  .md\:w-\[30\.625rem\] {
    @media (width >= 48rem) {
      width: 30.625rem;
    }
  }
  .md\:w-\[33\.75rem\] {
    @media (width >= 48rem) {
      width: 33.75rem;
    }
  }
  .md\:w-\[34\.5rem\] {
    @media (width >= 48rem) {
      width: 34.5rem;
    }
  }
  .md\:w-\[35\.625rem\] {
    @media (width >= 48rem) {
      width: 35.625rem;
    }
  }
  .md\:w-\[38px\] {
    @media (width >= 48rem) {
      width: 38px;
    }
  }
  .md\:w-\[42\.5rem\] {
    @media (width >= 48rem) {
      width: 42.5rem;
    }
  }
  .md\:w-\[51\.25rem\] {
    @media (width >= 48rem) {
      width: 51.25rem;
    }
  }
  .md\:w-\[135px\] {
    @media (width >= 48rem) {
      width: 135px;
    }
  }
  .md\:w-\[260px\] {
    @media (width >= 48rem) {
      width: 260px;
    }
  }
  .md\:w-\[300px\] {
    @media (width >= 48rem) {
      width: 300px;
    }
  }
  .md\:w-\[312px\] {
    @media (width >= 48rem) {
      width: 312px;
    }
  }
  .md\:w-auto {
    @media (width >= 48rem) {
      width: auto;
    }
  }
  .md\:w-full {
    @media (width >= 48rem) {
      width: 100%;
    }
  }
  .md\:max-w-2xs {
    @media (width >= 48rem) {
      max-width: var(--container-2xs);
    }
  }
  .md\:max-w-3xl {
    @media (width >= 48rem) {
      max-width: var(--container-3xl);
    }
  }
  .md\:max-w-5xl {
    @media (width >= 48rem) {
      max-width: var(--container-5xl);
    }
  }
  .md\:max-w-\[35\.1875rem\] {
    @media (width >= 48rem) {
      max-width: 35.1875rem;
    }
  }
  .md\:max-w-\[37\.5rem\] {
    @media (width >= 48rem) {
      max-width: 37.5rem;
    }
  }
  .md\:max-w-\[85\%\] {
    @media (width >= 48rem) {
      max-width: 85%;
    }
  }
  .md\:max-w-max {
    @media (width >= 48rem) {
      max-width: max-content;
    }
  }
  .md\:min-w-2xl {
    @media (width >= 48rem) {
      min-width: var(--container-2xl);
    }
  }
  .md\:min-w-\[1\.25rem\] {
    @media (width >= 48rem) {
      min-width: 1.25rem;
    }
  }
  .md\:min-w-\[1\.875rem\] {
    @media (width >= 48rem) {
      min-width: 1.875rem;
    }
  }
  .md\:min-w-\[12\.5rem\] {
    @media (width >= 48rem) {
      min-width: 12.5rem;
    }
  }
  .md\:min-w-\[15\.625rem\] {
    @media (width >= 48rem) {
      min-width: 15.625rem;
    }
  }
  .md\:min-w-\[25\.9375rem\] {
    @media (width >= 48rem) {
      min-width: 25.9375rem;
    }
  }
  .md\:min-w-\[25rem\] {
    @media (width >= 48rem) {
      min-width: 25rem;
    }
  }
  .md\:min-w-\[43\.75rem\] {
    @media (width >= 48rem) {
      min-width: 43.75rem;
    }
  }
  .md\:min-w-\[80px\] {
    @media (width >= 48rem) {
      min-width: 80px;
    }
  }
  .md\:min-w-\[415px\] {
    @media (width >= 48rem) {
      min-width: 415px;
    }
  }
  .md\:min-w-auto {
    @media (width >= 48rem) {
      min-width: auto;
    }
  }
  .md\:flex-1 {
    @media (width >= 48rem) {
      flex: 1;
    }
  }
  .md\:flex-none {
    @media (width >= 48rem) {
      flex: none;
    }
  }
  .md\:flex-shrink-0 {
    @media (width >= 48rem) {
      flex-shrink: 0;
    }
  }
  .md\:-translate-x-1\/2 {
    @media (width >= 48rem) {
      --tw-translate-x: calc(calc(1/2 * 100%) * -1);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }
  .md\:-translate-y-1\/2 {
    @media (width >= 48rem) {
      --tw-translate-y: calc(calc(1/2 * 100%) * -1);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }
  .md\:-translate-y-1\/3 {
    @media (width >= 48rem) {
      --tw-translate-y: calc(calc(1/3 * 100%) * -1);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }
  .md\:scale-100 {
    @media (width >= 48rem) {
      --tw-scale-x: 100%;
      --tw-scale-y: 100%;
      --tw-scale-z: 100%;
      scale: var(--tw-scale-x) var(--tw-scale-y);
    }
  }
  .md\:-rotate-90 {
    @media (width >= 48rem) {
      rotate: calc(90deg * -1);
    }
  }
  .md\:grid-cols-2 {
    @media (width >= 48rem) {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }
  .md\:grid-cols-3 {
    @media (width >= 48rem) {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }
  .md\:flex-col {
    @media (width >= 48rem) {
      flex-direction: column;
    }
  }
  .md\:flex-row {
    @media (width >= 48rem) {
      flex-direction: row;
    }
  }
  .md\:flex-nowrap {
    @media (width >= 48rem) {
      flex-wrap: nowrap;
    }
  }
  .md\:items-center {
    @media (width >= 48rem) {
      align-items: center;
    }
  }
  .md\:justify-between {
    @media (width >= 48rem) {
      justify-content: space-between;
    }
  }
  .md\:justify-center {
    @media (width >= 48rem) {
      justify-content: center;
    }
  }
  .md\:justify-end {
    @media (width >= 48rem) {
      justify-content: flex-end;
    }
  }
  .md\:justify-evenly {
    @media (width >= 48rem) {
      justify-content: space-evenly;
    }
  }
  .md\:gap-0 {
    @media (width >= 48rem) {
      gap: calc(var(--spacing) * 0);
    }
  }
  .md\:gap-5 {
    @media (width >= 48rem) {
      gap: calc(var(--spacing) * 5);
    }
  }
  .md\:gap-8 {
    @media (width >= 48rem) {
      gap: calc(var(--spacing) * 8);
    }
  }
  .md\:gap-10 {
    @media (width >= 48rem) {
      gap: calc(var(--spacing) * 10);
    }
  }
  .md\:gap-40 {
    @media (width >= 48rem) {
      gap: calc(var(--spacing) * 40);
    }
  }
  .md\:gap-x-2 {
    @media (width >= 48rem) {
      column-gap: calc(var(--spacing) * 2);
    }
  }
  .md\:gap-x-3 {
    @media (width >= 48rem) {
      column-gap: calc(var(--spacing) * 3);
    }
  }
  .md\:gap-x-4 {
    @media (width >= 48rem) {
      column-gap: calc(var(--spacing) * 4);
    }
  }
  .md\:gap-x-5 {
    @media (width >= 48rem) {
      column-gap: calc(var(--spacing) * 5);
    }
  }
  .md\:gap-x-6 {
    @media (width >= 48rem) {
      column-gap: calc(var(--spacing) * 6);
    }
  }
  .md\:gap-x-8 {
    @media (width >= 48rem) {
      column-gap: calc(var(--spacing) * 8);
    }
  }
  .md\:gap-x-10 {
    @media (width >= 48rem) {
      column-gap: calc(var(--spacing) * 10);
    }
  }
  .md\:gap-x-16 {
    @media (width >= 48rem) {
      column-gap: calc(var(--spacing) * 16);
    }
  }
  .md\:gap-x-24 {
    @media (width >= 48rem) {
      column-gap: calc(var(--spacing) * 24);
    }
  }
  .md\:gap-x-\[1\.25rem\] {
    @media (width >= 48rem) {
      column-gap: 1.25rem;
    }
  }
  .md\:gap-x-\[2\.5rem\] {
    @media (width >= 48rem) {
      column-gap: 2.5rem;
    }
  }
  .md\:gap-x-\[3\.75rem\] {
    @media (width >= 48rem) {
      column-gap: 3.75rem;
    }
  }
  .md\:gap-x-\[3\.125rem\] {
    @media (width >= 48rem) {
      column-gap: 3.125rem;
    }
  }
  .md\:gap-x-\[4\.375rem\] {
    @media (width >= 48rem) {
      column-gap: 4.375rem;
    }
  }
  .md\:gap-x-\[5rem\] {
    @media (width >= 48rem) {
      column-gap: 5rem;
    }
  }
  .md\:gap-y-4 {
    @media (width >= 48rem) {
      row-gap: calc(var(--spacing) * 4);
    }
  }
  .md\:gap-y-5 {
    @media (width >= 48rem) {
      row-gap: calc(var(--spacing) * 5);
    }
  }
  .md\:gap-y-7 {
    @media (width >= 48rem) {
      row-gap: calc(var(--spacing) * 7);
    }
  }
  .md\:gap-y-10 {
    @media (width >= 48rem) {
      row-gap: calc(var(--spacing) * 10);
    }
  }
  .md\:gap-y-12 {
    @media (width >= 48rem) {
      row-gap: calc(var(--spacing) * 12);
    }
  }
  .md\:gap-y-\[1\.125rem\] {
    @media (width >= 48rem) {
      row-gap: 1.125rem;
    }
  }
  .md\:overflow-hidden {
    @media (width >= 48rem) {
      overflow: hidden;
    }
  }
  .md\:overflow-visible {
    @media (width >= 48rem) {
      overflow: visible;
    }
  }
  .md\:rounded-2xl {
    @media (width >= 48rem) {
      border-radius: var(--radius-2xl);
    }
  }
  .md\:rounded-full {
    @media (width >= 48rem) {
      border-radius: calc(infinity * 1px);
    }
  }
  .md\:rounded-lg {
    @media (width >= 48rem) {
      border-radius: var(--radius-lg);
    }
  }
  .md\:rounded-none {
    @media (width >= 48rem) {
      border-radius: 0;
    }
  }
  .md\:rounded-xl {
    @media (width >= 48rem) {
      border-radius: var(--radius-xl);
    }
  }
  .md\:rounded-tl-4xl {
    @media (width >= 48rem) {
      border-top-left-radius: var(--radius-4xl);
    }
  }
  .md\:rounded-br-xl {
    @media (width >= 48rem) {
      border-bottom-right-radius: var(--radius-xl);
    }
  }
  .md\:border {
    @media (width >= 48rem) {
      border-style: var(--tw-border-style);
      border-width: 1px;
    }
  }
  .md\:border-0 {
    @media (width >= 48rem) {
      border-style: var(--tw-border-style);
      border-width: 0px;
    }
  }
  .md\:border-r {
    @media (width >= 48rem) {
      border-right-style: var(--tw-border-style);
      border-right-width: 1px;
    }
  }
  .md\:border-b {
    @media (width >= 48rem) {
      border-bottom-style: var(--tw-border-style);
      border-bottom-width: 1px;
    }
  }
  .md\:border-\[\#dae9ff\] {
    @media (width >= 48rem) {
      border-color: #dae9ff;
    }
  }
  .md\:border-\[var\(--border-color\)\] {
    @media (width >= 48rem) {
      border-color: var(--border-color);
    }
  }
  .md\:bg-\[\#e0eaff\] {
    @media (width >= 48rem) {
      background-color: #e0eaff;
    }
  }
  .md\:bg-\[\#fff\] {
    @media (width >= 48rem) {
      background-color: #fff;
    }
  }
  .md\:bg-transparent {
    @media (width >= 48rem) {
      background-color: transparent;
    }
  }
  .md\:bg-white {
    @media (width >= 48rem) {
      background-color: var(--color-white);
    }
  }
  .md\:bg-gradient-to-r {
    @media (width >= 48rem) {
      --tw-gradient-position: to right in oklab;
      background-image: linear-gradient(var(--tw-gradient-stops));
    }
  }
  .md\:bg-\[url\(\/images\/backgrounds\/pc_bj\.jpg\)\] {
    @media (width >= 48rem) {
      background-image: url(/images/backgrounds/pc_bj.jpg);
    }
  }
  .md\:bg-\[url\(\/static\/home\/<USER>\/backgrounds\/case_01_bj\.jpg\)\] {
    @media (width >= 48rem) {
      background-image: url(/static/home/<USER>/backgrounds/case_01_bj.jpg);
    }
  }
  .md\:bg-\[url\(\/static\/home\/<USER>\/backgrounds\/pc_bj\.jpg\)\] {
    @media (width >= 48rem) {
      background-image: url(/static/home/<USER>/backgrounds/pc_bj.jpg);
    }
  }
  .md\:bg-\[url\(\/static\/home\/<USER>\/icons\/xiala\.png\)\] {
    @media (width >= 48rem) {
      background-image: url(/static/home/<USER>/icons/xiala.png);
    }
  }
  .md\:bg-\[url\(__IMG__\/backgrounds\/case_01_bj\.jpg\)\] {
    @media (width >= 48rem) {
      background-image: url(__IMG__/backgrounds/case_01_bj.jpg);
    }
  }
  .md\:bg-\[url\(__IMG__\/backgrounds\/pc_bj\.jpg\)\] {
    @media (width >= 48rem) {
      background-image: url(__IMG__/backgrounds/pc_bj.jpg);
    }
  }
  .md\:bg-\[url\(__IMG__\/icons\/xiala\.png\)\] {
    @media (width >= 48rem) {
      background-image: url(__IMG__/icons/xiala.png);
    }
  }
  .md\:from-white {
    @media (width >= 48rem) {
      --tw-gradient-from: var(--color-white);
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }
  }
  .md\:via-white\/0 {
    @media (width >= 48rem) {
      --tw-gradient-via: color-mix(in srgb, #fff 0%, transparent);
      @supports (color: color-mix(in lab, red, red)) {
        --tw-gradient-via: color-mix(in oklab, var(--color-white) 0%, transparent);
      }
      --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
      --tw-gradient-stops: var(--tw-gradient-via-stops);
    }
  }
  .md\:to-white\/0 {
    @media (width >= 48rem) {
      --tw-gradient-to: color-mix(in srgb, #fff 0%, transparent);
      @supports (color: color-mix(in lab, red, red)) {
        --tw-gradient-to: color-mix(in oklab, var(--color-white) 0%, transparent);
      }
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }
  }
  .md\:bg-auto {
    @media (width >= 48rem) {
      background-size: auto;
    }
  }
  .md\:bg-cover {
    @media (width >= 48rem) {
      background-size: cover;
    }
  }
  .md\:bg-size-\[1\.25rem\] {
    @media (width >= 48rem) {
      background-size: 1.25rem;
    }
  }
  .md\:bg-size-\[15\.6875rem\] {
    @media (width >= 48rem) {
      background-size: 15.6875rem;
    }
  }
  .md\:bg-size-\[100\%\] {
    @media (width >= 48rem) {
      background-size: 100%;
    }
  }
  .md\:bg-size-\[100\%_100\%\] {
    @media (width >= 48rem) {
      background-size: 100% 100%;
    }
  }
  .md\:bg-size-\[auto\] {
    @media (width >= 48rem) {
      background-size: auto;
    }
  }
  .md\:bg-\[\.75rem_\.75rem\] {
    @media (width >= 48rem) {
      background-position: .75rem .75rem;
    }
  }
  .md\:bg-\[1\.2rem_center\] {
    @media (width >= 48rem) {
      background-position: 1.2rem center;
    }
  }
  .md\:bg-\[1\.5rem_center\] {
    @media (width >= 48rem) {
      background-position: 1.5rem center;
    }
  }
  .md\:bg-\[90\%_bottom\] {
    @media (width >= 48rem) {
      background-position: 90% bottom;
    }
  }
  .md\:bg-\[95\%_center\] {
    @media (width >= 48rem) {
      background-position: 95% center;
    }
  }
  .md\:bg-\[left_top\] {
    @media (width >= 48rem) {
      background-position: left top;
    }
  }
  .md\:bg-center {
    @media (width >= 48rem) {
      background-position: center;
    }
  }
  .md\:bg-no-repeat {
    @media (width >= 48rem) {
      background-repeat: no-repeat;
    }
  }
  .md\:p-0 {
    @media (width >= 48rem) {
      padding: calc(var(--spacing) * 0);
    }
  }
  .md\:p-3\.5 {
    @media (width >= 48rem) {
      padding: calc(var(--spacing) * 3.5);
    }
  }
  .md\:p-4 {
    @media (width >= 48rem) {
      padding: calc(var(--spacing) * 4);
    }
  }
  .md\:p-6 {
    @media (width >= 48rem) {
      padding: calc(var(--spacing) * 6);
    }
  }
  .md\:p-7 {
    @media (width >= 48rem) {
      padding: calc(var(--spacing) * 7);
    }
  }
  .md\:p-8 {
    @media (width >= 48rem) {
      padding: calc(var(--spacing) * 8);
    }
  }
  .md\:p-10 {
    @media (width >= 48rem) {
      padding: calc(var(--spacing) * 10);
    }
  }
  .md\:p-14 {
    @media (width >= 48rem) {
      padding: calc(var(--spacing) * 14);
    }
  }
  .md\:p-16 {
    @media (width >= 48rem) {
      padding: calc(var(--spacing) * 16);
    }
  }
  .md\:p-\[1\.875rem\] {
    @media (width >= 48rem) {
      padding: 1.875rem;
    }
  }
  .md\:p-\[2\.5rem\] {
    @media (width >= 48rem) {
      padding: 2.5rem;
    }
  }
  .md\:p-\[3\.125rem\] {
    @media (width >= 48rem) {
      padding: 3.125rem;
    }
  }
  .md\:px-0 {
    @media (width >= 48rem) {
      padding-inline: calc(var(--spacing) * 0);
    }
  }
  .md\:px-2 {
    @media (width >= 48rem) {
      padding-inline: calc(var(--spacing) * 2);
    }
  }
  .md\:px-3 {
    @media (width >= 48rem) {
      padding-inline: calc(var(--spacing) * 3);
    }
  }
  .md\:px-4 {
    @media (width >= 48rem) {
      padding-inline: calc(var(--spacing) * 4);
    }
  }
  .md\:px-5 {
    @media (width >= 48rem) {
      padding-inline: calc(var(--spacing) * 5);
    }
  }
  .md\:px-7 {
    @media (width >= 48rem) {
      padding-inline: calc(var(--spacing) * 7);
    }
  }
  .md\:px-8 {
    @media (width >= 48rem) {
      padding-inline: calc(var(--spacing) * 8);
    }
  }
  .md\:px-10 {
    @media (width >= 48rem) {
      padding-inline: calc(var(--spacing) * 10);
    }
  }
  .md\:px-12 {
    @media (width >= 48rem) {
      padding-inline: calc(var(--spacing) * 12);
    }
  }
  .md\:px-14 {
    @media (width >= 48rem) {
      padding-inline: calc(var(--spacing) * 14);
    }
  }
  .md\:px-15 {
    @media (width >= 48rem) {
      padding-inline: calc(var(--spacing) * 15);
    }
  }
  .md\:px-16 {
    @media (width >= 48rem) {
      padding-inline: calc(var(--spacing) * 16);
    }
  }
  .md\:px-\[1\.25rem\] {
    @media (width >= 48rem) {
      padding-inline: 1.25rem;
    }
  }
  .md\:px-\[1\.875rem\] {
    @media (width >= 48rem) {
      padding-inline: 1.875rem;
    }
  }
  .md\:px-\[2\.5rem\] {
    @media (width >= 48rem) {
      padding-inline: 2.5rem;
    }
  }
  .md\:py-0 {
    @media (width >= 48rem) {
      padding-block: calc(var(--spacing) * 0);
    }
  }
  .md\:py-2\.5 {
    @media (width >= 48rem) {
      padding-block: calc(var(--spacing) * 2.5);
    }
  }
  .md\:py-3 {
    @media (width >= 48rem) {
      padding-block: calc(var(--spacing) * 3);
    }
  }
  .md\:py-3\.5 {
    @media (width >= 48rem) {
      padding-block: calc(var(--spacing) * 3.5);
    }
  }
  .md\:py-4 {
    @media (width >= 48rem) {
      padding-block: calc(var(--spacing) * 4);
    }
  }
  .md\:py-5 {
    @media (width >= 48rem) {
      padding-block: calc(var(--spacing) * 5);
    }
  }
  .md\:py-6 {
    @media (width >= 48rem) {
      padding-block: calc(var(--spacing) * 6);
    }
  }
  .md\:py-7 {
    @media (width >= 48rem) {
      padding-block: calc(var(--spacing) * 7);
    }
  }
  .md\:py-9 {
    @media (width >= 48rem) {
      padding-block: calc(var(--spacing) * 9);
    }
  }
  .md\:py-10 {
    @media (width >= 48rem) {
      padding-block: calc(var(--spacing) * 10);
    }
  }
  .md\:py-14 {
    @media (width >= 48rem) {
      padding-block: calc(var(--spacing) * 14);
    }
  }
  .md\:py-16 {
    @media (width >= 48rem) {
      padding-block: calc(var(--spacing) * 16);
    }
  }
  .md\:py-20 {
    @media (width >= 48rem) {
      padding-block: calc(var(--spacing) * 20);
    }
  }
  .md\:py-44 {
    @media (width >= 48rem) {
      padding-block: calc(var(--spacing) * 44);
    }
  }
  .md\:py-\[1\.25rem\] {
    @media (width >= 48rem) {
      padding-block: 1.25rem;
    }
  }
  .md\:py-\[2\.75rem\] {
    @media (width >= 48rem) {
      padding-block: 2.75rem;
    }
  }
  .md\:pt-0 {
    @media (width >= 48rem) {
      padding-top: calc(var(--spacing) * 0);
    }
  }
  .md\:pt-4 {
    @media (width >= 48rem) {
      padding-top: calc(var(--spacing) * 4);
    }
  }
  .md\:pt-5 {
    @media (width >= 48rem) {
      padding-top: calc(var(--spacing) * 5);
    }
  }
  .md\:pt-5\.5 {
    @media (width >= 48rem) {
      padding-top: calc(var(--spacing) * 5.5);
    }
  }
  .md\:pt-7 {
    @media (width >= 48rem) {
      padding-top: calc(var(--spacing) * 7);
    }
  }
  .md\:pt-10 {
    @media (width >= 48rem) {
      padding-top: calc(var(--spacing) * 10);
    }
  }
  .md\:pt-20 {
    @media (width >= 48rem) {
      padding-top: calc(var(--spacing) * 20);
    }
  }
  .md\:pt-24 {
    @media (width >= 48rem) {
      padding-top: calc(var(--spacing) * 24);
    }
  }
  .md\:pt-28 {
    @media (width >= 48rem) {
      padding-top: calc(var(--spacing) * 28);
    }
  }
  .md\:pt-\[1\.875rem\] {
    @media (width >= 48rem) {
      padding-top: 1.875rem;
    }
  }
  .md\:pt-\[2\.5rem\] {
    @media (width >= 48rem) {
      padding-top: 2.5rem;
    }
  }
  .md\:pr-0 {
    @media (width >= 48rem) {
      padding-right: calc(var(--spacing) * 0);
    }
  }
  .md\:pr-7 {
    @media (width >= 48rem) {
      padding-right: calc(var(--spacing) * 7);
    }
  }
  .md\:pr-32 {
    @media (width >= 48rem) {
      padding-right: calc(var(--spacing) * 32);
    }
  }
  .md\:pr-36 {
    @media (width >= 48rem) {
      padding-right: calc(var(--spacing) * 36);
    }
  }
  .md\:pr-\[1\.25rem\] {
    @media (width >= 48rem) {
      padding-right: 1.25rem;
    }
  }
  .md\:pr-\[6\.25rem\] {
    @media (width >= 48rem) {
      padding-right: 6.25rem;
    }
  }
  .md\:pr-\[9\.375rem\] {
    @media (width >= 48rem) {
      padding-right: 9.375rem;
    }
  }
  .md\:pb-0 {
    @media (width >= 48rem) {
      padding-bottom: calc(var(--spacing) * 0);
    }
  }
  .md\:pb-8 {
    @media (width >= 48rem) {
      padding-bottom: calc(var(--spacing) * 8);
    }
  }
  .md\:pb-10 {
    @media (width >= 48rem) {
      padding-bottom: calc(var(--spacing) * 10);
    }
  }
  .md\:pb-14 {
    @media (width >= 48rem) {
      padding-bottom: calc(var(--spacing) * 14);
    }
  }
  .md\:pb-20 {
    @media (width >= 48rem) {
      padding-bottom: calc(var(--spacing) * 20);
    }
  }
  .md\:pb-44 {
    @media (width >= 48rem) {
      padding-bottom: calc(var(--spacing) * 44);
    }
  }
  .md\:pb-\[6\.25rem\] {
    @media (width >= 48rem) {
      padding-bottom: 6.25rem;
    }
  }
  .md\:pl-10 {
    @media (width >= 48rem) {
      padding-left: calc(var(--spacing) * 10);
    }
  }
  .md\:pl-16 {
    @media (width >= 48rem) {
      padding-left: calc(var(--spacing) * 16);
    }
  }
  .md\:pl-\[2\.5rem\] {
    @media (width >= 48rem) {
      padding-left: 2.5rem;
    }
  }
  .md\:pl-\[3\.125rem\] {
    @media (width >= 48rem) {
      padding-left: 3.125rem;
    }
  }
  .md\:text-center {
    @media (width >= 48rem) {
      text-align: center;
    }
  }
  .md\:text-right {
    @media (width >= 48rem) {
      text-align: right;
    }
  }
  .md\:text-2xl {
    @media (width >= 48rem) {
      font-size: var(--text-2xl);
      line-height: var(--tw-leading, var(--text-2xl--line-height));
    }
  }
  .md\:text-3xl {
    @media (width >= 48rem) {
      font-size: var(--text-3xl);
      line-height: var(--tw-leading, var(--text-3xl--line-height));
    }
  }
  .md\:text-4xl {
    @media (width >= 48rem) {
      font-size: var(--text-4xl);
      line-height: var(--tw-leading, var(--text-4xl--line-height));
    }
  }
  .md\:text-5xl {
    @media (width >= 48rem) {
      font-size: var(--text-5xl);
      line-height: var(--tw-leading, var(--text-5xl--line-height));
    }
  }
  .md\:text-8xl {
    @media (width >= 48rem) {
      font-size: var(--text-8xl);
      line-height: var(--tw-leading, var(--text-8xl--line-height));
    }
  }
  .md\:text-base {
    @media (width >= 48rem) {
      font-size: var(--text-base);
      line-height: var(--tw-leading, var(--text-base--line-height));
    }
  }
  .md\:text-lg {
    @media (width >= 48rem) {
      font-size: var(--text-lg);
      line-height: var(--tw-leading, var(--text-lg--line-height));
    }
  }
  .md\:text-xl {
    @media (width >= 48rem) {
      font-size: var(--text-xl);
      line-height: var(--tw-leading, var(--text-xl--line-height));
    }
  }
  .md\:text-\[1\.875rem\] {
    @media (width >= 48rem) {
      font-size: 1.875rem;
    }
  }
  .md\:text-\[1\.5625rem\] {
    @media (width >= 48rem) {
      font-size: 1.5625rem;
    }
  }
  .md\:text-\[1rem\] {
    @media (width >= 48rem) {
      font-size: 1rem;
    }
  }
  .md\:text-\[2\.5rem\] {
    @media (width >= 48rem) {
      font-size: 2.5rem;
    }
  }
  .md\:text-\[3\.75rem\] {
    @media (width >= 48rem) {
      font-size: 3.75rem;
    }
  }
  .md\:text-\[12\.5rem\] {
    @media (width >= 48rem) {
      font-size: 12.5rem;
    }
  }
  .md\:leading-8 {
    @media (width >= 48rem) {
      --tw-leading: calc(var(--spacing) * 8);
      line-height: calc(var(--spacing) * 8);
    }
  }
  .md\:leading-9 {
    @media (width >= 48rem) {
      --tw-leading: calc(var(--spacing) * 9);
      line-height: calc(var(--spacing) * 9);
    }
  }
  .md\:leading-10 {
    @media (width >= 48rem) {
      --tw-leading: calc(var(--spacing) * 10);
      line-height: calc(var(--spacing) * 10);
    }
  }
  .md\:leading-\[1\.25rem\] {
    @media (width >= 48rem) {
      --tw-leading: 1.25rem;
      line-height: 1.25rem;
    }
  }
  .md\:leading-\[2\.5rem\] {
    @media (width >= 48rem) {
      --tw-leading: 2.5rem;
      line-height: 2.5rem;
    }
  }
  .md\:leading-none {
    @media (width >= 48rem) {
      --tw-leading: 1;
      line-height: 1;
    }
  }
  .md\:underline-offset-8 {
    @media (width >= 48rem) {
      text-underline-offset: 8px;
    }
  }
  .md\:opacity-30 {
    @media (width >= 48rem) {
      opacity: 30%;
    }
  }
  .md\:shadow-lg {
    @media (width >= 48rem) {
      --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 4px 6px -4px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }
}
@media (min-width: 1920px) {
  :root {
    font-size: 16px;
  }
}
@media (max-width: 1680px) and (min-resolution: 120dpi) {
  :root {
    font-size: 12.8px;
  }
  body {
    max-width: 1536px;
    margin: 0 auto;
  }
}
@media (max-width: 760px) {
  :root {
    font-size: 18px;
  }
}
:root {
  --border-color: #dae9ff;
}
@font-face {
  font-family: 'Roboto_Regular';
  src: url('/static/home/<USER>/roboto/Roboto-Regular.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
}
@font-face {
  font-family: 'Roboto_LightItalic';
  src: url('/static/home/<USER>/roboto/Roboto-LightItalic.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
}
@font-face {
  font-family: 'Roboto_Bold';
  src: url('/static/home/<USER>/roboto/Roboto-Bold.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
}
body {
  font-family: 'Roboto_Regular', sans-serif;
}
.Roboto_Light {
  font-family: 'Roboto_Light', sans-serif;
}
.Roboto_LightItalic {
  font-family: 'Roboto_LightItalic', sans-serif;
}
.Roboto_Regular {
  font-family: 'Roboto_Regular', sans-serif;
}
.Roboto_Bold {
  font-family: 'Roboto_Bold', sans-serif;
}
.slide-thumb-active {
  background-color: #105eb3;
  color: var(--color-white);
}
.Digital-l {
  font-size: var(--text-base);
  line-height: var(--tw-leading, var(--text-base--line-height));
  @media (width >= 48rem) {
    font-size: var(--text-xl);
    line-height: var(--tw-leading, var(--text-xl--line-height));
  }
}
.Digital-r {
  font-size: var(--text-sm);
  line-height: var(--tw-leading, var(--text-sm--line-height));
  color: #999;
  @media (width >= 48rem) {
    font-size: var(--text-base);
    line-height: var(--tw-leading, var(--text-base--line-height));
  }
}
.swiper.swiper-banner {
  --swiper-pagination-bullet-inactive-color: #fff;
  --swiper-pagination-color: #fff;
}
.swiper-banner-pagination {
  bottom: .75rem !important;
}
.hollow-number {
  -webkit-text-stroke: 2px white;
  text-stroke: 2px white;
}
.product-introduction dt {
  position: relative;
}
.product-introduction dt::after {
  content: '';
  position: absolute;
  top: calc(1/2 * 100%);
  left: calc(var(--spacing) * -4);
  height: calc(var(--spacing) * 1.5);
  width: calc(var(--spacing) * 1.5);
  --tw-translate-y: calc(calc(1/2 * 100%) * -1);
  translate: var(--tw-translate-x) var(--tw-translate-y);
  border-radius: calc(infinity * 1px);
  background-color: #000;
}
.hollow-number-ripple {
  position: relative;
  display: inline-block;
  z-index: 1;
}
.service-pagination .Digital-r {
  color: #d5eaff;
}
:root {
  --animate-duration: 800ms;
  --animate-delay: 0.4s;
}
.navigation:hover .secondary {
  display: block;
}
.navigation-item:hover>ul {
  display: block;
}
.Navigation li::after {
  content: '/';
  display: inline-block;
  margin: 0 .3125rem;
}
.Navigation li:last-child::after {
  content: '';
}
.content-txt p {
  margin-block-start: 1em;
  margin-block-end: 1em;
  color: #666;
  font-size: var(--text-base);
  line-height: var(--tw-leading, var(--text-base--line-height));
  @media (width >= 48rem) {
    font-size: var(--text-xl);
    line-height: var(--tw-leading, var(--text-xl--line-height));
  }
}
.item-btn.active {
  border-style: var(--tw-border-style);
  border-width: 1px;
  border-color: #155290;
  background-color: #e0eaff;
  color: #030000;
}
.user-tab-link {
  border-bottom-style: var(--tw-border-style);
  border-bottom-width: 2px;
  border-color: #dae9ff;
}
.user-tab-link span {
  position: relative;
  display: block;
  cursor: pointer;
  padding: calc(var(--spacing) * 4);
  color: #999;
  @media (width >= 48rem) {
    padding-block: calc(var(--spacing) * 6);
  }
}
.user-tab-link span.active {
  color: #111111;
}
.user-tab-link span.active::after {
  content: '';
  position: absolute;
  bottom: calc(.125rem * -1);
  left: calc(var(--spacing) * 0);
  width: 100%;
  border-bottom-style: var(--tw-border-style);
  border-bottom-width: .0625rem;
  border-color: #155797;
  @media (width >= 48rem) {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 2px;
  }
}
.navigation-bar a {
  font-family: 'Roboto_Bold', sans-serif;
  display: block;
}
.Points-list li {
  border-bottom-style: var(--tw-border-style);
  border-bottom-width: 1px;
  border-color: #e0eaff;
  padding-block: calc(var(--spacing) * 3);
  @media (width >= 48rem) {
    padding-block: calc(var(--spacing) * 5);
  }
}
.Points-list li:last-child {
  border-bottom-style: var(--tw-border-style);
  border-bottom-width: 0px;
}
.Points-list li:first-child {
  padding-top: calc(var(--spacing) * 0);
}
.Points-list li>span {
  font-size: var(--text-sm);
  line-height: var(--tw-leading, var(--text-sm--line-height));
  color: #999;
  @media (width >= 48rem) {
    font-size: var(--text-base);
    line-height: var(--tw-leading, var(--text-base--line-height));
  }
}
.Points-list li>p {
  @media (width >= 48rem) {
    margin-top: calc(var(--spacing) * 2);
  }
  @media (width >= 48rem) {
    font-size: var(--text-xl);
    line-height: var(--tw-leading, var(--text-xl--line-height));
  }
}
.message-list li {
  display: flex;
  align-items: flex-start;
  column-gap: calc(var(--spacing) * 3);
  border-bottom-style: var(--tw-border-style);
  border-bottom-width: 1px;
  border-color: #e0eaff;
  padding-block: calc(var(--spacing) * 3);
  @media (width >= 48rem) {
    column-gap: 1.25rem;
  }
  @media (width >= 48rem) {
    padding-block: calc(var(--spacing) * 5);
  }
}
.message-img {
  height: calc(var(--spacing) * 10);
  width: calc(var(--spacing) * 10);
  flex-shrink: 0;
  align-items: center;
  justify-content: center;
  border-radius: calc(infinity * 1px);
  @media (width >= 48rem) {
    height: 5rem;
  }
  @media (width >= 48rem) {
    width: 5rem;
  }
}
.message-list li:last-child {
  border-bottom-style: var(--tw-border-style);
  border-bottom-width: 0px;
}
.message-info strong {
  margin-right: calc(var(--spacing) * 2.5);
}
.message-content p a {
  color: #f08411;
}
.message-tips-list li {
  display: flex;
  align-items: center;
  column-gap: calc(var(--spacing) * 1);
  border-bottom-style: var(--tw-border-style);
  border-bottom-width: 1px;
  border-color: #e0eaff;
  padding: calc(var(--spacing) * 3);
  font-size: var(--text-sm);
  line-height: var(--tw-leading, var(--text-sm--line-height));
  @media (width >= 48rem) {
    padding-inline: 1.875rem;
  }
  @media (width >= 48rem) {
    padding-block: 1.25rem;
  }
  @media (width >= 48rem) {
    font-size: var(--text-base);
    line-height: var(--tw-leading, var(--text-base--line-height));
  }
}
.message-tips-list li span {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}
.used-item::after {
  content: '';
  position: absolute;
  top: calc(var(--spacing) * 0);
  right: calc(var(--spacing) * 0);
  z-index: calc(1 * -1);
  height: 100%;
  width: calc(11/12 * 100%);
  border-top-left-radius: var(--radius-4xl);
  border-top-right-radius: var(--radius-lg);
  border-bottom-right-radius: var(--radius-lg);
  border-bottom-left-radius: var(--radius-4xl);
  background-color: #fff2e4;
}
.used-item-l::after {
  content: '';
  background: linear-gradient(180deg, transparent 50%, #fff2e4 50%);
  position: absolute;
  top: calc(0.725rem * -1);
  right: calc(0.625rem * -1);
  height: 1.25rem;
  width: 1.25rem;
  border-radius: calc(infinity * 1px);
}
.used-item-l::before {
  content: '';
  background: linear-gradient(0deg, transparent 50%, #fff2e4 50%);
  position: absolute;
  right: calc(0.625rem * -1);
  bottom: calc(0.725rem * -1);
  height: 1.25rem;
  width: 1.25rem;
  border-radius: calc(infinity * 1px);
}
.used-list.expired .used-item-l::after {
  background: linear-gradient(180deg, transparent 50%, #fafbff 50%);
}
.used-list.expired .used-item-l::before {
  background: linear-gradient(0deg, transparent 50%, #fafbff 50%);
}
.used-list.expired .used-item::after {
  background-color: #fafbff;
}
.order-tab-item {
  cursor: pointer;
  border-top-left-radius: var(--radius-xl);
  border-top-right-radius: var(--radius-xl);
  padding-inline: calc(var(--spacing) * 3.5);
  padding-block: calc(var(--spacing) * 2);
  color: #999;
  @media (width >= 48rem) {
    border-top-left-radius: var(--radius-2xl);
  }
  @media (width >= 48rem) {
    border-top-right-radius: var(--radius-2xl);
  }
  @media (width >= 48rem) {
    padding-inline: 1.25rem;
  }
  @media (width >= 48rem) {
    padding-block: .625rem;
  }
}
.order-tab-item.active {
  background-color: #f08411;
  color: var(--color-white);
}
.order-right-text-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom-style: var(--tw-border-style);
  border-bottom-width: 1px;
  border-color: #e0eaff;
  padding-block: calc(var(--spacing) * 2);
  @media (width >= 48rem) {
    padding-block: calc(var(--spacing) * 3);
  }
}
.item-status {
  width: calc(1/2 * 100%);
  text-align: right;
  font-size: var(--text-xs);
  line-height: var(--tw-leading, var(--text-xs--line-height));
  @media (width >= 48rem) {
    width: calc(1/3 * 100%);
  }
  @media (width >= 48rem) {
    font-size: var(--text-base);
    line-height: var(--tw-leading, var(--text-base--line-height));
  }
}
.item-left-text-name {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  width: calc(1/2 * 100%);
  text-wrap: balance;
  @media (width >= 48rem) {
    width: calc(2/3 * 100%);
  }
  @media (width >= 48rem) {
    font-size: var(--text-xl);
    line-height: var(--tw-leading, var(--text-xl--line-height));
  }
}
.order-list-item-container-item {
  margin-bottom: calc(var(--spacing) * 5);
  padding-bottom: calc(var(--spacing) * 3);
}
.user-name-drop {
  display: none;
}
.product-list-tab-item {
  position: relative;
  height: 2.5rem;
  padding-inline: calc(var(--spacing) * 5);
  text-align: center;
  --tw-leading: 2.5rem;
  line-height: 2.5rem;
}
.arrow-container {
  display: flex;
  align-items: center;
  font-family: sans-serif;
}
.arrow-step {
  position: relative;
  background-color: #dddddd;
  flex-shrink: 0;
  cursor: pointer;
  margin-right: 0.125rem;
  height: 2.5rem;
  padding-right: 1.5625rem;
  padding-left: 2rem;
  --tw-leading: 2.5rem;
  line-height: 2.5rem;
  color: #999;
  @media (width >= 48rem) {
    height: 3.75rem;
  }
  @media (width >= 48rem) {
    padding-inline: 3.75rem;
  }
  @media (width >= 48rem) {
    --tw-leading: 3.75rem;
    line-height: 3.75rem;
  }
}
.arrow-step::before, .arrow-step::after {
  content: "";
  position: absolute;
  top: 0;
  width: 0;
  height: 0;
  border-top: 1.25rem solid transparent;
  border-bottom: 1.25rem solid transparent;
  @media (width >= 48rem) {
    border-top-style: var(--tw-border-style);
    border-top-width: 1.875rem;
  }
  @media (width >= 48rem) {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 1.875rem;
  }
}
.arrow-step::before {
  left: 0;
  border-left: 1rem solid white;
  z-index: 0;
  border-left-style: var(--tw-border-style);
  border-left-width: 1rem;
  @media (width >= 48rem) {
    border-left-style: var(--tw-border-style);
    border-left-width: 1.5rem;
  }
}
.arrow-step::after {
  right: -1rem;
  border-left: 1rem solid #ddd;
  z-index: 1;
  border-left-style: var(--tw-border-style);
  border-left-width: 1rem;
  @media (width >= 48rem) {
    right: calc(1.45rem * -1);
  }
  @media (width >= 48rem) {
    border-left-style: var(--tw-border-style);
    border-left-width: 1.5rem;
  }
}
.arrow-step:first-child {
  padding-left: 1rem;
  @media (width >= 48rem) {
    padding-left: 3.75rem;
  }
}
.arrow-step:first-child::before {
  display: none;
  padding-left: 1rem;
}
.arrow-step.active {
  color: #fff !important;
}
.arrow-step.service-proceed {
  background-color: #dae9ff;
}
.arrow-step.service-proceed::after {
  border-left-color: #dae9ff;
}
.arrow-step.service-completed {
  background-color: #f08411;
}
.arrow-step.service-completed::after {
  border-left-color: #f08411;
}
.arrow-step.proceed {
  background-color: #f08411;
  color: #f5f5f5;
}
.arrow-step.proceed::after {
  border-left-color: #f08411;
}
.arrow-step.completed {
  background-color: #28a745;
  color: white;
}
.arrow-step.completed::after {
  border-left-color: #28a745;
}
.download-list ul {
  display: grid;
  grid-template-columns: repeat(1, minmax(0, 1fr));
  @media (width >= 48rem) {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
}
.download-list ul>li {
  border-right-style: var(--tw-border-style);
  border-right-width: 1px;
  border-bottom-style: var(--tw-border-style);
  border-bottom-width: 1px;
  border-color: #dae9ff;
  padding-inline: calc(var(--spacing) * 3);
  padding-block: calc(var(--spacing) * 3);
  @media (width >= 48rem) {
    padding: calc(var(--spacing) * 5);
  }
}
.action-buttons button {
  border-radius: var(--radius-sm);
  padding-inline: calc(var(--spacing) * 5);
  padding-block: calc(var(--spacing) * 1.5);
  color: var(--color-white);
}
.about-container p {
  margin-block-start: 1em;
  margin-block-end: 1em;
  font-size: var(--text-sm);
  line-height: var(--tw-leading, var(--text-sm--line-height));
  color: #111;
  @media (width >= 48rem) {
    font-size: var(--text-xl);
    line-height: var(--tw-leading, var(--text-xl--line-height));
  }
}
.about-container h2 {
  font-size: 1.5rem;
  font-size: var(--text-xl);
  line-height: var(--tw-leading, var(--text-xl--line-height));
  color: #111;
  @media (width >= 48rem) {
    font-size: var(--text-3xl);
    line-height: var(--tw-leading, var(--text-3xl--line-height));
  }
}
.about-container h3 {
  font-size: 1.17rem;
  font-size: var(--text-xl);
  line-height: var(--tw-leading, var(--text-xl--line-height));
  color: #111;
  @media (width >= 48rem) {
    font-size: var(--text-2xl);
    line-height: var(--tw-leading, var(--text-2xl--line-height));
  }
}
.btn-terms button {
  border-radius: calc(infinity * 1px);
  padding-block: calc(var(--spacing) * 2.5);
}
.discover-news-screening span {
  cursor: pointer;
  border-radius: var(--radius-md);
  border-style: var(--tw-border-style);
  border-width: 1px;
  border-color: transparent;
  padding-inline: calc(var(--spacing) * 4);
  padding-block: calc(var(--spacing) * 1.5);
  color: #999;
  @media (width >= 48rem) {
    padding-inline: calc(var(--spacing) * 10);
  }
  @media (width >= 48rem) {
    padding-block: calc(var(--spacing) * 3);
  }
}
.discover-news-screening span.active {
  border-style: var(--tw-border-style);
  border-width: 1px;
  border-color: #155797;
  color: #155797;
}
.news-content-item h3 a {
  color: #666;
}
.news-content-item h3 a:hover {
  color: #155797;
}
.news-content-item li {
  @media (width >= 48rem) {
    border-radius: var(--radius-2xl);
  }
}
.news-content-item li:hover {
  border-color: #155797;
}
.resources span {
  cursor: pointer;
  border-radius: var(--radius-md);
  border-style: var(--tw-border-style);
  border-width: 1px;
  border-color: #e0eaff;
  padding-inline: calc(var(--spacing) * 4);
  padding-block: calc(var(--spacing) * 1.5);
  color: #999;
  @media (width >= 48rem) {
    border-radius: var(--radius-xl);
  }
  @media (width >= 48rem) {
    padding-inline: calc(var(--spacing) * 8);
  }
  @media (width >= 48rem) {
    padding-block: calc(var(--spacing) * 4);
  }
}
.resources-list::after {
  content: '';
  position: absolute;
  top: calc(1/2 * 100%);
  right: calc(1/2 * 100%);
  height: 100%;
  width: 1px;
  --tw-translate-x: calc(calc(1/2 * 100%) * -1);
  --tw-translate-y: calc(calc(1/2 * 100%) * -1);
  translate: var(--tw-translate-x) var(--tw-translate-y);
  border-right-style: var(--tw-border-style);
  border-right-width: 1px;
  border-color: #dae9ff;
}
.resources span.active {
  border-color: #155290;
  background-color: #e0eaff;
  color: #030000;
}
#btn_agree.active {
  background-color: #5399ff;
}
@media screen and (min-width: 768px) {
  .swiper-banner-pagination {
    bottom: 40px !important;
  }
  .secondary {
    box-shadow: 0 0 20px -2px rgba(84, 111, 138, 0.3);
  }
}
@media (hover: hover) and (pointer: fine) {
  .btn-name:hover .user-name-drop {
    display: block;
  }
}
@property --tw-translate-x {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-translate-y {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-translate-z {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-scale-x {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}
@property --tw-scale-y {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}
@property --tw-scale-z {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}
@property --tw-rotate-x {
  syntax: "*";
  inherits: false;
}
@property --tw-rotate-y {
  syntax: "*";
  inherits: false;
}
@property --tw-rotate-z {
  syntax: "*";
  inherits: false;
}
@property --tw-skew-x {
  syntax: "*";
  inherits: false;
}
@property --tw-skew-y {
  syntax: "*";
  inherits: false;
}
@property --tw-border-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}
@property --tw-leading {
  syntax: "*";
  inherits: false;
}
@property --tw-font-weight {
  syntax: "*";
  inherits: false;
}
@property --tw-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-inset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-inset-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-inset-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-ring-color {
  syntax: "*";
  inherits: false;
}
@property --tw-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-inset-ring-color {
  syntax: "*";
  inherits: false;
}
@property --tw-inset-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-ring-inset {
  syntax: "*";
  inherits: false;
}
@property --tw-ring-offset-width {
  syntax: "<length>";
  inherits: false;
  initial-value: 0px;
}
@property --tw-ring-offset-color {
  syntax: "*";
  inherits: false;
  initial-value: #fff;
}
@property --tw-ring-offset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-gradient-position {
  syntax: "*";
  inherits: false;
}
@property --tw-gradient-from {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}
@property --tw-gradient-via {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}
@property --tw-gradient-to {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}
@property --tw-gradient-stops {
  syntax: "*";
  inherits: false;
}
@property --tw-gradient-via-stops {
  syntax: "*";
  inherits: false;
}
@property --tw-gradient-from-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 0%;
}
@property --tw-gradient-via-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 50%;
}
@property --tw-gradient-to-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 100%;
}
@keyframes ping {
  75%, 100% {
    transform: scale(2);
    opacity: 0;
  }
}
@layer properties {
  @supports ((-webkit-hyphens: none) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color:rgb(from red r g b)))) {
    *, ::before, ::after, ::backdrop {
      --tw-translate-x: 0;
      --tw-translate-y: 0;
      --tw-translate-z: 0;
      --tw-scale-x: 1;
      --tw-scale-y: 1;
      --tw-scale-z: 1;
      --tw-rotate-x: initial;
      --tw-rotate-y: initial;
      --tw-rotate-z: initial;
      --tw-skew-x: initial;
      --tw-skew-y: initial;
      --tw-border-style: solid;
      --tw-leading: initial;
      --tw-font-weight: initial;
      --tw-shadow: 0 0 #0000;
      --tw-shadow-color: initial;
      --tw-shadow-alpha: 100%;
      --tw-inset-shadow: 0 0 #0000;
      --tw-inset-shadow-color: initial;
      --tw-inset-shadow-alpha: 100%;
      --tw-ring-color: initial;
      --tw-ring-shadow: 0 0 #0000;
      --tw-inset-ring-color: initial;
      --tw-inset-ring-shadow: 0 0 #0000;
      --tw-ring-inset: initial;
      --tw-ring-offset-width: 0px;
      --tw-ring-offset-color: #fff;
      --tw-ring-offset-shadow: 0 0 #0000;
      --tw-gradient-position: initial;
      --tw-gradient-from: #0000;
      --tw-gradient-via: #0000;
      --tw-gradient-to: #0000;
      --tw-gradient-stops: initial;
      --tw-gradient-via-stops: initial;
      --tw-gradient-from-position: 0%;
      --tw-gradient-via-position: 50%;
      --tw-gradient-to-position: 100%;
    }
  }
}
