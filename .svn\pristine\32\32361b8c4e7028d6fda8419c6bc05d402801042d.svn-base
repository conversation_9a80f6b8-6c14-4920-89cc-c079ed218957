{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "5d3d99df2e43d347b6710ec3e9df5900", "packages": [{"name": "league/flysystem", "version": "1.1.10", "source": {"type": "git", "url": "https://github.com/thephpleague/flysystem.git", "reference": "3239285c825c152bcc315fe0e87d6b55f5972ed1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/flysystem/zipball/3239285c825c152bcc315fe0e87d6b55f5972ed1", "reference": "3239285c825c152bcc315fe0e87d6b55f5972ed1", "shasum": ""}, "require": {"ext-fileinfo": "*", "league/mime-type-detection": "^1.3", "php": "^7.2.5 || ^8.0"}, "conflict": {"league/flysystem-sftp": "<1.0.6"}, "require-dev": {"phpspec/prophecy": "^1.11.1", "phpunit/phpunit": "^8.5.8"}, "suggest": {"ext-ftp": "Allows you to use FTP server storage", "ext-openssl": "Allows you to use FTPS server storage", "league/flysystem-aws-s3-v2": "Allows you to use S3 storage with AWS SDK v2", "league/flysystem-aws-s3-v3": "Allows you to use S3 storage with AWS SDK v3", "league/flysystem-azure": "Allows you to use Windows Azure Blob storage", "league/flysystem-cached-adapter": "Flysystem adapter decorator for metadata caching", "league/flysystem-eventable-filesystem": "Allows you to use EventableFilesystem", "league/flysystem-rackspace": "Allows you to use Rackspace Cloud Files", "league/flysystem-sftp": "Allows you to use SFTP server storage via phpseclib", "league/flysystem-webdav": "Allows you to use WebDAV storage", "league/flysystem-ziparchive": "Allows you to use ZipArchive adapter", "spatie/flysystem-dropbox": "Allows you to use Dropbox storage", "srmklive/flysystem-dropbox-v2": "Allows you to use Dropbox storage for PHP 5 applications"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "autoload": {"psr-4": {"League\\Flysystem\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Filesystem abstraction: Many filesystems, one API.", "keywords": ["Cloud Files", "WebDAV", "abstraction", "aws", "cloud", "copy.com", "dropbox", "file systems", "files", "filesystem", "filesystems", "ftp", "rackspace", "remote", "s3", "sftp", "storage"], "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/1.1.10"}, "funding": [{"url": "https://offset.earth/frankdejonge", "type": "other"}], "time": "2022-10-04T09:16:37+00:00"}, {"name": "league/flysystem-cached-adapter", "version": "1.1.0", "source": {"type": "git", "url": "https://github.com/thephpleague/flysystem-cached-adapter.git", "reference": "d1925efb2207ac4be3ad0c40b8277175f99ffaff"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/flysystem-cached-adapter/zipball/d1925efb2207ac4be3ad0c40b8277175f99ffaff", "reference": "d1925efb2207ac4be3ad0c40b8277175f99ffaff", "shasum": ""}, "require": {"league/flysystem": "~1.0", "psr/cache": "^1.0.0"}, "require-dev": {"mockery/mockery": "~0.9", "phpspec/phpspec": "^3.4", "phpunit/phpunit": "^5.7", "predis/predis": "~1.0", "tedivm/stash": "~0.12"}, "suggest": {"ext-phpredis": "Pure C implemented extension for PHP"}, "type": "library", "autoload": {"psr-4": {"League\\Flysystem\\Cached\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "frank<PERSON><PERSON>e", "email": "<EMAIL>"}], "description": "An adapter decorator to enable meta-data caching.", "support": {"issues": "https://github.com/thephpleague/flysystem-cached-adapter/issues", "source": "https://github.com/thephpleague/flysystem-cached-adapter/tree/master"}, "time": "2020-07-25T15:56:04+00:00"}, {"name": "league/mime-type-detection", "version": "1.16.0", "source": {"type": "git", "url": "https://github.com/thephpleague/mime-type-detection.git", "reference": "2d6702ff215bf922936ccc1ad31007edc76451b9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/mime-type-detection/zipball/2d6702ff215bf922936ccc1ad31007edc76451b9", "reference": "2d6702ff215bf922936ccc1ad31007edc76451b9", "shasum": ""}, "require": {"ext-fileinfo": "*", "php": "^7.4 || ^8.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.2", "phpstan/phpstan": "^0.12.68", "phpunit/phpunit": "^8.5.8 || ^9.3 || ^10.0"}, "type": "library", "autoload": {"psr-4": {"League\\MimeTypeDetection\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Mime-type detection for Flysystem", "support": {"issues": "https://github.com/thephpleague/mime-type-detection/issues", "source": "https://github.com/thephpleague/mime-type-detection/tree/1.16.0"}, "funding": [{"url": "https://github.com/frankdejonge", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/league/flysystem", "type": "tidelift"}], "time": "2024-09-21T08:32:55+00:00"}, {"name": "liliuwei/thinkphp-jump", "version": "v2.0", "source": {"type": "git", "url": "https://github.com/liliuwei/thinkphp-jump.git", "reference": "27a6d8da1c8d4f5734e0ce05f482ac07c568769c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/liliuwei/thinkphp-jump/zipball/27a6d8da1c8d4f5734e0ce05f482ac07c568769c", "reference": "27a6d8da1c8d4f5734e0ce05f482ac07c568769c", "shasum": ""}, "require": {"php": ">=8.0.0", "topthink/framework": "^8.0", "topthink/think-view": "^2.0"}, "type": "think-extend", "extra": {"think": {"config": {"jump": "src/config/jump.php"}}}, "autoload": {"psr-4": {"liliuwei\\think\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "lili<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "适用于thinkphp6.0的跳转扩展", "keywords": ["error", "redirect", "result", "success", "think-jump", "thinkphp"], "support": {"issues": "https://github.com/liliuwei/thinkphp-jump/issues", "source": "https://github.com/liliuwei/thinkphp-jump/tree/v2.0"}, "time": "2023-09-13T03:03:59+00:00"}, {"name": "phpmailer/phpmailer", "version": "v6.10.0", "source": {"type": "git", "url": "https://github.com/PHPMailer/PHPMailer.git", "reference": "bf74d75a1fde6beaa34a0ddae2ec5fce0f72a144"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPMailer/PHPMailer/zipball/bf74d75a1fde6beaa34a0ddae2ec5fce0f72a144", "reference": "bf74d75a1fde6beaa34a0ddae2ec5fce0f72a144", "shasum": ""}, "require": {"ext-ctype": "*", "ext-filter": "*", "ext-hash": "*", "php": ">=5.5.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "^1.0", "doctrine/annotations": "^1.2.6 || ^1.13.3", "php-parallel-lint/php-console-highlighter": "^1.0.0", "php-parallel-lint/php-parallel-lint": "^1.3.2", "phpcompatibility/php-compatibility": "^9.3.5", "roave/security-advisories": "dev-latest", "squizlabs/php_codesniffer": "^3.7.2", "yoast/phpunit-polyfills": "^1.0.4"}, "suggest": {"decomplexity/SendOauth2": "Adapter for using XOAUTH2 authentication", "ext-mbstring": "Needed to send email in multibyte encoding charset or decode encoded addresses", "ext-openssl": "Needed for secure SMTP sending and DKIM signing", "greew/oauth2-azure-provider": "Needed for Microsoft Azure XOAUTH2 authentication", "hayageek/oauth2-yahoo": "Needed for Yahoo XOAUTH2 authentication", "league/oauth2-google": "Needed for Google XOAUTH2 authentication", "psr/log": "For optional PSR-3 debug logging", "symfony/polyfill-mbstring": "To support UTF-8 if the Mbstring PHP extension is not enabled (^1.2)", "thenetworg/oauth2-azure": "Needed for Microsoft XOAUTH2 authentication"}, "type": "library", "autoload": {"psr-4": {"PHPMailer\\PHPMailer\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-2.1-only"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}], "description": "PHPMailer is a full-featured email creation and transfer class for PHP", "support": {"issues": "https://github.com/PHPMailer/PHPMailer/issues", "source": "https://github.com/PHPMailer/PHPMailer/tree/v6.10.0"}, "funding": [{"url": "https://github.com/Synchro", "type": "github"}], "time": "2025-04-24T15:19:31+00:00"}, {"name": "psr/cache", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/cache.git", "reference": "d11b50ad223250cf17b86e38383413f5a6764bf8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/cache/zipball/d11b50ad223250cf17b86e38383413f5a6764bf8", "reference": "d11b50ad223250cf17b86e38383413f5a6764bf8", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Cache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for caching libraries", "keywords": ["cache", "psr", "psr-6"], "support": {"source": "https://github.com/php-fig/cache/tree/master"}, "time": "2016-08-06T20:24:11+00:00"}, {"name": "psr/container", "version": "2.0.2", "source": {"type": "git", "url": "https://github.com/php-fig/container.git", "reference": "c71ecc56dfe541dbd90c5360474fbc405f8d5963"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/container/zipball/c71ecc56dfe541dbd90c5360474fbc405f8d5963", "reference": "c71ecc56dfe541dbd90c5360474fbc405f8d5963", "shasum": ""}, "require": {"php": ">=7.4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Container\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common Container Interface (PHP FIG PSR-11)", "homepage": "https://github.com/php-fig/container", "keywords": ["PSR-11", "container", "container-interface", "container-interop", "psr"], "support": {"issues": "https://github.com/php-fig/container/issues", "source": "https://github.com/php-fig/container/tree/2.0.2"}, "time": "2021-11-05T16:47:00+00:00"}, {"name": "psr/http-message", "version": "1.1", "source": {"type": "git", "url": "https://github.com/php-fig/http-message.git", "reference": "cb6ce4845ce34a8ad9e68117c10ee90a29919eba"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message/zipball/cb6ce4845ce34a8ad9e68117c10ee90a29919eba", "reference": "cb6ce4845ce34a8ad9e68117c10ee90a29919eba", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-message/tree/1.1"}, "time": "2023-04-04T09:50:52+00:00"}, {"name": "psr/log", "version": "3.0.2", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "f16e1d5863e37f8d8c2a01719f5b34baa2b714d3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/f16e1d5863e37f8d8c2a01719f5b34baa2b714d3", "reference": "f16e1d5863e37f8d8c2a01719f5b34baa2b714d3", "shasum": ""}, "require": {"php": ">=8.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "autoload": {"psr-4": {"Psr\\Log\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "support": {"source": "https://github.com/php-fig/log/tree/3.0.2"}, "time": "2024-09-11T13:17:53+00:00"}, {"name": "psr/simple-cache", "version": "3.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/simple-cache.git", "reference": "764e0b3939f5ca87cb904f570ef9be2d78a07865"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/simple-cache/zipball/764e0b3939f5ca87cb904f570ef9be2d78a07865", "reference": "764e0b3939f5ca87cb904f570ef9be2d78a07865", "shasum": ""}, "require": {"php": ">=8.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\SimpleCache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interfaces for simple caching", "keywords": ["cache", "caching", "psr", "psr-16", "simple-cache"], "support": {"source": "https://github.com/php-fig/simple-cache/tree/3.0.0"}, "time": "2021-10-29T13:26:27+00:00"}, {"name": "topthink/framework", "version": "v8.1.1", "source": {"type": "git", "url": "https://github.com/top-think/framework.git", "reference": "d301115ea77f33745b8d6659c74740c34fe53e9b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/top-think/framework/zipball/d301115ea77f33745b8d6659c74740c34fe53e9b", "reference": "d301115ea77f33745b8d6659c74740c34fe53e9b", "shasum": ""}, "require": {"ext-ctype": "*", "ext-json": "*", "ext-mbstring": "*", "php": ">=8.0.0", "psr/container": "^2.0", "psr/http-message": "^1.0", "psr/log": "^1.0|^2.0|^3.0", "psr/simple-cache": "^1.0|^2.0|^3.0", "topthink/think-helper": "^3.1", "topthink/think-orm": "^3.0|^4.0"}, "require-dev": {"guzzlehttp/psr7": "^2.1.0", "mikey179/vfsstream": "^1.6", "mockery/mockery": "^1.2", "phpunit/phpunit": "^9.5"}, "type": "library", "autoload": {"files": [], "psr-4": {"think\\": "src/think/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "liu21st", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "The ThinkPHP Framework.", "homepage": "http://thinkphp.cn/", "keywords": ["framework", "orm", "thinkphp"], "support": {"issues": "https://github.com/top-think/framework/issues", "source": "https://github.com/top-think/framework/tree/v8.1.1"}, "time": "2024-11-26T05:57:55+00:00"}, {"name": "topthink/think-captcha", "version": "v3.0.11", "source": {"type": "git", "url": "https://github.com/top-think/think-captcha.git", "reference": "4f24f560a31011329e3d144732e5370d7676b3fb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/top-think/think-captcha/zipball/4f24f560a31011329e3d144732e5370d7676b3fb", "reference": "4f24f560a31011329e3d144732e5370d7676b3fb", "shasum": ""}, "require": {"topthink/framework": "^6.0|^8.0"}, "type": "library", "extra": {"think": {"config": {"captcha": "src/config.php"}, "services": ["think\\captcha\\CaptchaService"]}}, "autoload": {"files": ["src/helper.php"], "psr-4": {"think\\captcha\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "captcha package for thinkphp", "support": {"issues": "https://github.com/top-think/think-captcha/issues", "source": "https://github.com/top-think/think-captcha/tree/v3.0.11"}, "time": "2024-11-22T12:59:35+00:00"}, {"name": "topthink/think-filesystem", "version": "v2.0.3", "source": {"type": "git", "url": "https://github.com/top-think/think-filesystem.git", "reference": "e8e51adb9f3a3f3aac2aa3ef73b7b439100f777d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/top-think/think-filesystem/zipball/e8e51adb9f3a3f3aac2aa3ef73b7b439100f777d", "reference": "e8e51adb9f3a3f3aac2aa3ef73b7b439100f777d", "shasum": ""}, "require": {"league/flysystem": "^1.1.4", "league/flysystem-cached-adapter": "^1.0", "php": ">=7.2.5", "topthink/framework": "^6.1|^8.0"}, "require-dev": {"mikey179/vfsstream": "^1.6", "mockery/mockery": "^1.2", "phpunit/phpunit": "^8.0"}, "type": "library", "autoload": {"psr-4": {"think\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "The ThinkPHP6.1 Filesystem Package", "support": {"issues": "https://github.com/top-think/think-filesystem/issues", "source": "https://github.com/top-think/think-filesystem/tree/v2.0.3"}, "time": "2024-10-16T03:37:24+00:00"}, {"name": "topthink/think-helper", "version": "v3.1.10", "source": {"type": "git", "url": "https://github.com/top-think/think-helper.git", "reference": "ac66cc0859a12cd5d73258f50f338aadc95e9b46"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/top-think/think-helper/zipball/ac66cc0859a12cd5d73258f50f338aadc95e9b46", "reference": "ac66cc0859a12cd5d73258f50f338aadc95e9b46", "shasum": ""}, "require": {"php": ">=7.1.0"}, "require-dev": {"phpunit/phpunit": "^9.5"}, "type": "library", "autoload": {"files": ["src/helper.php"], "psr-4": {"think\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "The ThinkPHP6 Helper Package", "support": {"issues": "https://github.com/top-think/think-helper/issues", "source": "https://github.com/top-think/think-helper/tree/v3.1.10"}, "time": "2024-11-21T01:47:51+00:00"}, {"name": "topthink/think-multi-app", "version": "v1.1.1", "source": {"type": "git", "url": "https://github.com/top-think/think-multi-app.git", "reference": "f93c604d5cfac2b613756273224ee2f88e457b88"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/top-think/think-multi-app/zipball/f93c604d5cfac2b613756273224ee2f88e457b88", "reference": "f93c604d5cfac2b613756273224ee2f88e457b88", "shasum": ""}, "require": {"php": ">=7.1.0", "topthink/framework": "^6.0|^8.0"}, "type": "library", "extra": {"think": {"services": ["think\\app\\Service"]}}, "autoload": {"psr-4": {"think\\app\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "liu21st", "email": "<EMAIL>"}], "description": "thinkphp multi app support", "support": {"issues": "https://github.com/top-think/think-multi-app/issues", "source": "https://github.com/top-think/think-multi-app/tree/v1.1.1"}, "time": "2024-11-25T08:52:44+00:00"}, {"name": "topthink/think-orm", "version": "v3.0.33", "source": {"type": "git", "url": "https://github.com/top-think/think-orm.git", "reference": "6e0ea679f7448ff9c8906606462505597681e22e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/top-think/think-orm/zipball/6e0ea679f7448ff9c8906606462505597681e22e", "reference": "6e0ea679f7448ff9c8906606462505597681e22e", "shasum": ""}, "require": {"ext-json": "*", "ext-pdo": "*", "php": ">=8.0.0", "psr/log": ">=1.0", "psr/simple-cache": ">=1.0", "topthink/think-helper": "^3.1"}, "require-dev": {"phpunit/phpunit": "^9.6|^10"}, "suggest": {"ext-mongodb": "provide mongodb support"}, "type": "library", "autoload": {"files": ["stubs/load_stubs.php"], "psr-4": {"think\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "liu21st", "email": "<EMAIL>"}], "description": "the PHP Database&ORM Framework", "keywords": ["database", "orm"], "support": {"issues": "https://github.com/top-think/think-orm/issues", "source": "https://github.com/top-think/think-orm/tree/v3.0.33"}, "time": "2024-12-19T01:52:44+00:00"}, {"name": "topthink/think-template", "version": "v3.0.2", "source": {"type": "git", "url": "https://github.com/top-think/think-template.git", "reference": "0b88bd449f0f7626dd75b05f557c8bc208c08b0c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/top-think/think-template/zipball/0b88bd449f0f7626dd75b05f557c8bc208c08b0c", "reference": "0b88bd449f0f7626dd75b05f557c8bc208c08b0c", "shasum": ""}, "require": {"php": ">=8.0.0", "psr/simple-cache": ">=1.0"}, "type": "library", "autoload": {"psr-4": {"think\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "liu21st", "email": "<EMAIL>"}], "description": "the php template engine", "support": {"issues": "https://github.com/top-think/think-template/issues", "source": "https://github.com/top-think/think-template/tree/v3.0.2"}, "time": "2024-10-16T03:41:06+00:00"}, {"name": "topthink/think-view", "version": "v2.0.4", "source": {"type": "git", "url": "https://github.com/top-think/think-view.git", "reference": "e6a68e4179baf8d795700d62b710da86d7c3f783"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/top-think/think-view/zipball/e6a68e4179baf8d795700d62b710da86d7c3f783", "reference": "e6a68e4179baf8d795700d62b710da86d7c3f783", "shasum": ""}, "require": {"php": ">=8.0.0", "topthink/think-template": "^3.0"}, "type": "library", "autoload": {"psr-4": {"think\\view\\driver\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "liu21st", "email": "<EMAIL>"}], "description": "thinkphp template driver", "support": {"issues": "https://github.com/top-think/think-view/issues", "source": "https://github.com/top-think/think-view/tree/v2.0.4"}, "time": "2025-01-10T08:49:57+00:00"}, {"name": "wenh<PERSON>an/thinkphp6-auth", "version": "1.1.2", "source": {"type": "git", "url": "https://github.com/wenhainan/thinkphp6-auth.git", "reference": "0fc5d4f9c02bc5bdc6629ca78f5f588884d0cdfe"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/wenhainan/thinkphp6-auth/zipball/0fc5d4f9c02bc5bdc6629ca78f5f588884d0cdfe", "reference": "0fc5d4f9c02bc5bdc6629ca78f5f588884d0cdfe", "shasum": ""}, "require": {"php": ">=5.4.0"}, "type": "library", "extra": {"think": {"config": {"auth": "src/config/auth.php"}}}, "autoload": {"psr-4": {"think\\wenhainan\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "auth package for thinkphp6", "homepage": "https://github.com/wenhainan/thinkphp6-auth", "support": {"issues": "https://github.com/wenhainan/thinkphp6-auth/issues", "source": "https://github.com/wenhainan/thinkphp6-auth/tree/1.1.2"}, "time": "2021-12-10T08:59:24+00:00"}], "packages-dev": [{"name": "symfony/polyfill-mbstring", "version": "v1.31.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "85181ba99b2345b0ef10ce42ecac37612d9fd341"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/85181ba99b2345b0ef10ce42ecac37612d9fd341", "reference": "85181ba99b2345b0ef10ce42ecac37612d9fd341", "shasum": ""}, "require": {"php": ">=7.2"}, "provide": {"ext-mbstring": "*"}, "suggest": {"ext-mbstring": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-mbstring/tree/v1.31.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/var-dumper", "version": "v6.0.19", "source": {"type": "git", "url": "https://github.com/symfony/var-dumper.git", "reference": "eb980457fa6899840fe1687e8627a03a7d8a3d52"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/var-dumper/zipball/eb980457fa6899840fe1687e8627a03a7d8a3d52", "reference": "eb980457fa6899840fe1687e8627a03a7d8a3d52", "shasum": ""}, "require": {"php": ">=8.0.2", "symfony/polyfill-mbstring": "~1.0"}, "conflict": {"phpunit/phpunit": "<5.4.3", "symfony/console": "<5.4"}, "require-dev": {"ext-iconv": "*", "symfony/console": "^5.4|^6.0", "symfony/process": "^5.4|^6.0", "symfony/uid": "^5.4|^6.0", "twig/twig": "^2.13|^3.0.4"}, "suggest": {"ext-iconv": "To convert non-UTF-8 strings to UTF-8 (or symfony/polyfill-iconv in case ext-iconv cannot be used).", "ext-intl": "To show region name in time zone dump", "symfony/console": "To use the ServerDumpCommand and/or the bin/var-dump-server script"}, "bin": ["Resources/bin/var-dump-server"], "type": "library", "autoload": {"files": ["Resources/functions/dump.php"], "psr-4": {"Symfony\\Component\\VarDumper\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides mechanisms for walking through any arbitrary PHP variable", "homepage": "https://symfony.com", "keywords": ["debug", "dump"], "support": {"source": "https://github.com/symfony/var-dumper/tree/v6.0.19"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-01-20T17:44:14+00:00"}, {"name": "topthink/think-trace", "version": "v1.6", "source": {"type": "git", "url": "https://github.com/top-think/think-trace.git", "reference": "136cd5d97e8bdb780e4b5c1637c588ed7ca3e142"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/top-think/think-trace/zipball/136cd5d97e8bdb780e4b5c1637c588ed7ca3e142", "reference": "136cd5d97e8bdb780e4b5c1637c588ed7ca3e142", "shasum": ""}, "require": {"php": ">=7.1.0", "topthink/framework": "^6.0|^8.0"}, "type": "library", "extra": {"think": {"config": {"trace": "src/config.php"}, "services": ["think\\trace\\Service"]}}, "autoload": {"psr-4": {"think\\trace\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "liu21st", "email": "<EMAIL>"}], "description": "thinkphp debug trace", "support": {"issues": "https://github.com/top-think/think-trace/issues", "source": "https://github.com/top-think/think-trace/tree/v1.6"}, "time": "2023-02-07T08:36:32+00:00"}], "aliases": [], "minimum-stability": "stable", "stability-flags": [], "prefer-stable": false, "prefer-lowest": false, "platform": {"php": ">=8.0.0"}, "platform-dev": [], "plugin-api-version": "2.3.0"}