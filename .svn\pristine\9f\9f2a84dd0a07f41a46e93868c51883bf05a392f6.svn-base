
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: #f5f5f5;
    height: 100vh;
    display: flex;
}

.sidebar {
    width: 300px;
    background-color: #ffffff;
    border-right: 1px solid #e0e0e0;
    display: flex;
    flex-direction: column;
}

.sidebar-header {
    padding: 20px;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    align-items: center;
    gap: 10px;
}

.desktop-header {
    display: block;
    width: 100%;
}

.mobile-header {
    display: none;
    align-items: center;
    justify-content: space-between;
    width: 100%;
}

.mobile-close-btn {
    background: none;
    border: none;
    font-size: 18px;
    color: #666;
    cursor: pointer;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.2s;
}

.mobile-close-btn:hover {
    background-color: #f5f5f5;
}

.mobile-header-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    flex: 1;
    text-align: center;
    margin-right: 32px; /* 平衡关闭按钮的宽度 */
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
}

.chat-list {
    flex: 1;
    overflow-y: auto;
}

.chat-item {
    padding: 15px 20px;
    border-bottom: 1px solid #f0f0f0;
    cursor: pointer;
    transition: background-color 0.2s;
    position: relative;
}

.chat-item:hover {
    background-color: #f8f9fa;
}

.chat-item.active {
    background-color: #e3f2fd;
}

.chat-item-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 5px;
}

.chat-avatar {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    background: linear-gradient(135deg, #ff6b6b 0%, #feca57 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 14px;
    overflow: hidden;
}

.chat-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
}

.chat-name {
    font-weight: 600;
    color: #333;
    font-size: 14px;
}

.chat-time {
    font-size: 12px;
    color: #999;
    margin-left: auto;
}

.chat-preview {
    font-size: 13px;
    color: #666;
    margin-left: 47px;
    overflow-wrap: anywhere;
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
}

.unread-badge {
    position: absolute;
    top: 15px;
    right: 20px;
    background-color: #ff4757;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
}

.main-chat {
    flex: 1;
    display: flex;
    flex-direction: column;
    background-color: #ffffff;
}

.chat-header {
    padding: 20px;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    align-items: center;
    gap: 15px;
}

.chat-header-info h2 {
    font-size: 18px;
    color: #333;
}

.chat-header-actions {
    margin-left: auto;
    display: flex;
    gap: 10px;
}

.action-btn {
    width: 35px;
    height: 35px;
    border: none;
    background-color: #f8f9fa;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s;
}

.action-btn:hover {
    background-color: #e9ecef;
}

.messages-container {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.message {
    display: flex;
    gap: 12px;
    max-width: 70%;
}

.message.sent {
    align-self: flex-end;
    flex-direction: row-reverse;
}

.message-avatar {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    flex-shrink: 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 14px;
    overflow: hidden;
}

.message-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
}

.message-content {
    background-color: #f8f9fa;
    padding: 12px 16px;
    border-radius: 18px;
    max-width: 100%;
}

.message.sent .message-content {
    background-color: #007bff;
    color: white;
}

.message-text {
    font-size: 14px;
    line-height: 1.4;
    margin-bottom: 5px;
}

.message-time {
    font-size: 11px;
    color: #999;
    margin-top: 5px;
}

.message.sent .message-time {
    color: rgba(255, 255, 255, 0.7);
}

.file-message {
    background-color: #e3f2fd;
    border: 1px solid #bbdefb;
    border-radius: 12px;
    padding: 15px;
    display: flex;
    align-items: center;
    gap: 12px;
}

.file-icon {
    width: 40px;
    height: 40px;
    background-color: #2196f3;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 18px;
}

.file-info h4 {
    font-size: 14px;
    color: #333;
    margin-bottom: 2px;
}

.file-size {
    font-size: 12px;
    color: #666;
}

.download-btn {
    margin-left: auto;
    padding: 8px 16px;
    background-color: #2196f3;
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 12px;
}

.refresh-notice {
    text-align: center;
    padding: 20px;
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 8px;
    margin: 20px;
}

.refresh-notice p {
    color: #856404;
    margin-bottom: 10px;
    font-size: 14px;
}

.refresh-btn {
    background-color: #007bff;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
}

.input-area {
    padding: 20px;
    border-top: 1px solid #e0e0e0;
    display: flex;
    align-items: center;
    gap: 15px;
}

.input-actions {
    display: flex;
    gap: 10px;
}

.input-btn {
    width: 35px;
    height: 35px;
    border: none;
    background-color: #f8f9fa;
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    z-index: 10;
    transition: background-color 0.2s;
}

.input-btn:hover {
    background-color: #e9ecef;
}

.input-btn:active {
    background-color: #dee2e6;
    transform: scale(0.95);
}

.message-input {
    flex: 1;
    border: 1px solid #e0e0e0;
    border-radius: 20px;
    padding: 10px 16px;
    font-size: 14px;
    outline: none;
    resize: none;
    min-height: 40px;
    max-height: 120px;
}

.send-btn {
    background-color: #007bff;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 20px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
}

.send-btn:disabled {
    background-color: #ccc;
    cursor: not-allowed;
}

/* 文件上传相关样式 */
.file-input {
    display: none;
}

.image-message {
    max-width: 300px;
    border-radius: 12px;
    overflow: hidden;
    cursor: pointer;
    transition: transform 0.2s;
}

.image-message:hover {
    transform: scale(1.02);
}

.image-message img {
    width: 100%;
    height: auto;
    display: block;
}

.file-message-custom {
    background-color: #f8f9fa;
    border: 1px solid #e0e0e0;
    border-radius: 12px;
    padding: 12px 16px;
    display: flex;
    align-items: center;
    gap: 12px;
    max-width: 280px;
}

.message.sent .file-message-custom {
    background-color: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.3);
}

.file-icon-custom {
    width: 35px;
    height: 35px;
    background-color: #b8c6d285;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 16px;
    flex-shrink: 0;
}

.file-info-custom {
    flex: 1;
    min-width: 0;
}

.file-name-custom {
    font-size: 14px;
    font-weight: 500;
    color: #333;
    margin-bottom: 2px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.message.sent .file-name-custom {
    color: white;
}

.file-size-custom {
    font-size: 12px;
    color: #666;
}

.message.sent .file-size-custom {
    color: rgba(255, 255, 255, 0.8);
}

.file-download-btn {
    background-color: #007bff;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    font-weight: 500;
    margin-left: 10px;
    transition: background-color 0.2s;
    flex-shrink: 0;
}

.file-download-btn:hover {
    background-color: #0056b3;
}

.file-download-btn:active {
    background-color: #004085;
    transform: scale(0.98);
}

/* 下载通知样式 */
.download-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background-color: #ffffff;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 12px 16px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    display: flex;
    align-items: center;
    gap: 12px;
    z-index: 2000;
    min-width: 280px;
    animation: slideInRight 0.3s ease;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.download-icon {
    font-size: 20px;
    flex-shrink: 0;
}

.download-info {
    flex: 1;
    min-width: 0;
}

.download-filename {
    font-size: 14px;
    font-weight: 500;
    color: #333;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-bottom: 2px;
}

.download-size {
    font-size: 12px;
    color: #666;
}

.download-status {
    font-size: 12px;
    color: #007bff;
    font-weight: 500;
    flex-shrink: 0;
}

/* 清空聊天通知样式 */
.clear-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background-color: #ffffff;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 12px 16px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    display: flex;
    align-items: center;
    gap: 12px;
    z-index: 2000;
    min-width: 280px;
    animation: slideInRight 0.3s ease;
    border-left: 4px solid #ff6b6b;
}

.clear-icon {
    font-size: 20px;
    flex-shrink: 0;
    color: #ff6b6b;
}

.clear-info {
    flex: 1;
    min-width: 0;
}

.clear-title {
    font-size: 14px;
    font-weight: 500;
    color: #333;
    margin-bottom: 2px;
}

.clear-desc {
    font-size: 12px;
    color: #666;
}

/* 无聊天对话时的空状态 */
.no-chats {
    text-align: center;
    padding: 60px 20px;
    color: #999;
    font-size: 16px;
}

/* 搜索模态框样式 */
.search-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 2000;
    display: none;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.search-modal-content {
    background-color: #ffffff;
    border-radius: 12px;
    width: 100%;
    max-width: 600px;
    max-height: 80vh;
    display: flex;
    flex-direction: column;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

.search-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px 24px 16px 24px;
    border-bottom: 1px solid #e0e0e0;
}

.search-header h3 {
    margin: 0;
    font-size: 18px;
    color: #333;
    font-weight: 600;
}

.search-close-btn {
    background: none;
    border: none;
    font-size: 20px;
    color: #666;
    cursor: pointer;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.2s;
}

.search-close-btn:hover {
    background-color: #f5f5f5;
}

.search-input-container {
    padding: 20px 24px;
    display: flex;
    gap: 12px;
    border-bottom: 1px solid #e0e0e0;
}

.search-input {
    flex: 1;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 12px 16px;
    font-size: 14px;
    outline: none;
    transition: border-color 0.2s;
}

.search-input:focus {
    border-color: #007bff;
}

.search-btn {
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 8px;
    padding: 12px 16px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.2s;
}

.search-btn:hover {
    background-color: #0056b3;
}

.clear-messages-btn {
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 8px;
    padding: 12px 16px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.2s;
    margin-left: 8px;
}

.clear-messages-btn:hover {
    background-color: #c82333;
}

.search-results {
    flex: 1;
    overflow-y: auto;
    padding: 16px 24px;
    min-height: 200px;
}

.search-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: #999;
}

.search-placeholder-icon {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
}

.search-placeholder-text {
    font-size: 16px;
}

/* 搜索结果项样式 */
.search-result-item {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 12px 16px;
    margin-bottom: 12px;
    cursor: pointer;
    transition: all 0.2s;
}

.search-result-item:hover {
    border-color: #007bff;
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.1);
}

.search-result-header {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 8px;
}

.search-result-avatar {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 12px;
    font-weight: bold;
    overflow: hidden;
}

.search-result-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
}

.search-result-avatar.sent {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
}

.search-result-avatar.received {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.search-result-user {
    font-size: 14px;
    font-weight: 500;
    color: #333;
}

.search-result-sender {
    font-size: 12px;
    color: #666;
    margin-left: 4px;
}

.search-result-sender.sent {
    color: #007bff;
}

.search-result-sender.received {
    color: #667eea;
}

.search-result-time {
    font-size: 12px;
    color: #999;
    margin-left: auto;
}

.search-result-content {
    font-size: 14px;
    color: #555;
    line-height: 1.4;
}

.search-result-content .highlight {
    background-color: #fff3cd;
    color: #856404;
    padding: 1px 3px;
    border-radius: 3px;
    font-weight: 500;
}

.search-result-type {
    display: inline-block;
    background-color: #f8f9fa;
    color: #6c757d;
    font-size: 11px;
    padding: 2px 6px;
    border-radius: 10px;
    margin-top: 6px;
}

.search-no-results {
    text-align: center;
    padding: 40px 20px;
    color: #999;
}

.search-no-results-icon {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
}

.search-no-results-text {
    font-size: 16px;
    margin-bottom: 8px;
}

.search-no-results-desc {
    font-size: 14px;
    color: #ccc;
}

/* 图片预览模态框 */
.image-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.9);
    z-index: 2000;
    display: none;
}

.image-modal-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    max-width: 80%;
    max-height: 80%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.image-modal img {
    max-width: 100%;
    max-height: 100%;
    width: auto;
    height: auto;
    object-fit: contain;
    border-radius: 8px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    transition: transform 0.3s ease;
    cursor: grab;
    display: block;
    margin: 0 auto;
}

.image-modal img:active {
    cursor: grabbing;
}

.image-modal-close {
    position: fixed;
    top: 20px;
    right: 20px;
    background: rgba(255, 255, 255, 0.9);
    border: none;
    color: #333;
    font-size: 20px;
    font-weight: bold;
    cursor: pointer;
    width: 45px;
    height: 45px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    z-index: 2001;
}

.image-modal-close:hover {
    background-color: rgba(255, 255, 255, 1);
    transform: scale(1.1);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.3);
}

.image-modal-close:active {
    transform: scale(0.95);
}

/* 图片预览控制栏 */
.image-modal-controls {
    position: fixed;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 12px;
    background: rgba(0, 0, 0, 0.8);
    padding: 12px 20px;
    border-radius: 30px;
    backdrop-filter: blur(10px);
    z-index: 2001;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
}

.image-control-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    font-size: 16px;
    cursor: pointer;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
    backdrop-filter: blur(5px);
}

.image-control-btn:hover {
    background-color: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.4);
    transform: scale(1.1);
}

.image-control-btn:active {
    transform: scale(0.95);
}

/* 图片信息显示 */
.image-modal-info {
    position: fixed;
    top: 20px;
    left: 20px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 10px 15px;
    border-radius: 20px;
    font-size: 13px;
    backdrop-filter: blur(10px);
    max-width: 250px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    z-index: 2001;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* 上传预览区域 */
.upload-preview {
    display: none;
    padding: 10px 20px;
    border-top: 1px solid #e0e0e0;
    background-color: #f8f9fa;
    gap: 10px;
    flex-wrap: wrap;
}

.upload-preview.show {
    display: flex;
}

.preview-item {
    position: relative;
    display: inline-block;
}

.preview-image {
    width: 60px;
    height: 60px;
    object-fit: cover;
    border-radius: 8px;
    border: 2px solid #007bff;
}

.preview-file {
    width: 200px;
    padding: 8px 12px;
    background-color: white;
    border: 2px solid #007bff;
    border-radius: 8px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.preview-file-icon {
    width: 24px;
    height: 24px;
    background-color: #6c757d;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 12px;
    flex-shrink: 0;
}

.preview-file-name {
    flex: 1;
    font-size: 12px;
    color: #333;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.preview-remove {
    position: absolute;
    top: -5px;
    right: -5px;
    width: 20px;
    height: 20px;
    background-color: #ff4757;
    color: white;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 拖拽上传样式 */
.main-chat.drag-over {
    background-color: rgba(0, 123, 255, 0.1);
    border: 2px dashed #007bff;
}

.main-chat.drag-over::after {
    content: '📁 拖拽文件到这里上传';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: rgba(0, 123, 255, 0.9);
    color: white;
    padding: 20px 30px;
    border-radius: 10px;
    font-size: 16px;
    font-weight: bold;
    z-index: 1000;
    pointer-events: none;
}

/* 响应式设计 - 移动端适配 */
@media (max-width: 768px) {
    body {
        flex-direction: column;
    }

    .sidebar {
        width: 100%;
        height: auto;
        max-height: 40vh;
        border-right: none;
        border-bottom: 1px solid #e0e0e0;
        position: relative;
    }

    .sidebar.mobile-hidden {
        display: none;
    }

    .main-chat {
        flex: 1;
        min-height: 60vh;
    }

    .chat-header {
        padding: 15px;
        position: relative;
    }

    .mobile-menu-btn {
        display: block;
        position: absolute;
        left: 15px;
        top: 50%;
        transform: translateY(-50%);
        background: none;
        border: none;
        font-size: 0;
        cursor: pointer;
        z-index: 10;
    }

    .chat-header-info {
        text-align: center;
        flex: 1;
    }

    .chat-header-actions {
        position: absolute;
        right: 15px;
        top: 50%;
        transform: translateY(-50%);
    }

    .messages-container {
        padding: 15px;
        gap: 15px;
    }

    .message {
        max-width: 85%;
    }

    .message-content {
        padding: 10px 14px;
        border-radius: 16px;
    }

    .input-area {
        padding: 15px;
        gap: 10px;
    }

    .message-input {
        font-size: 16px; /* 防止iOS缩放 */
        border-radius: 18px;
        padding: 12px 16px;
    }

    .send-btn {
        padding: 12px 18px;
        border-radius: 18px;
        font-size: 14px;
    }

    .input-btn {
        width: 40px;
        height: 40px;
    }

    .chat-item {
        padding: 12px 15px;
    }

    .chat-avatar, .message-avatar {
        width: 32px;
        height: 32px;
        font-size: 13px;
    }

    .sidebar-header {
        padding: 15px;
    }

    .file-message {
        padding: 12px;
        gap: 10px;
    }

    .file-icon {
        width: 35px;
        height: 35px;
        font-size: 16px;
    }

    .refresh-notice {
        margin: 15px;
        padding: 15px;
    }

    .action-btn {
        width: 32px;
        height: 32px;
    }

    .image-message {
        max-width: 250px;
    }

    .file-message-custom {
        max-width: 250px;
        padding: 10px 12px;
    }

    .upload-preview {
        padding: 8px 12px;
        gap: 8px;
    }

    .preview-image {
        width: 50px;
        height: 50px;
    }

    .preview-file {
        width: 180px;
        padding: 6px 10px;
    }

    .image-modal-content {
        max-width: 90%;
        max-height: 70%;
    }

    .image-modal-close {
        top: 15px;
        right: 15px;
        width: 40px;
        height: 40px;
        font-size: 18px;
    }

    .image-modal-controls {
        bottom: 20px;
        padding: 10px 15px;
        gap: 10px;
    }

    .image-control-btn {
        width: 35px;
        height: 35px;
        font-size: 14px;
    }

    .image-modal-info {
        top: 15px;
        left: 15px;
        font-size: 12px;
        padding: 8px 12px;
        max-width: 180px;
    }

    .download-notification {
        top: 10px;
        right: 10px;
        left: 10px;
        min-width: auto;
        padding: 10px 12px;
    }

    .download-filename {
        font-size: 13px;
    }

    .download-size {
        font-size: 11px;
    }

    .download-status {
        font-size: 11px;
    }

    .file-download-btn {
        padding: 4px 8px;
        font-size: 11px;
        margin-left: 8px;
    }

    .clear-notification {
        top: 10px;
        right: 10px;
        left: 10px;
        min-width: auto;
        padding: 10px 12px;
    }

    .clear-title {
        font-size: 13px;
    }

    .clear-desc {
        font-size: 11px;
    }

    .search-modal {
        padding: 10px;
    }

    .search-modal-content {
        max-height: 90vh;
    }

    .search-header {
        padding: 16px 20px 12px 20px;
    }

    .search-header h3 {
        font-size: 16px;
    }

    .search-input-container {
        padding: 16px 20px;
        flex-direction: column;
        gap: 10px;
    }

    .clear-messages-btn {
        padding: 10px 14px;
        font-size: 13px;
        margin-left: 0;
        margin-top: 8px;
    }

    .search-results {
        padding: 12px 20px;
    }

    .search-result-item {
        padding: 10px 12px;
    }

    .search-result-content {
        font-size: 13px;
    }
}

@media (max-width: 480px) {
    .sidebar {
        max-height: 35vh;
    }

    .main-chat {
        min-height: 65vh;
    }

    .chat-header {
        padding: 12px 15px;
        padding-left: 50px;
    }

    .chat-header h2 {
        font-size: 16px;
    }

    .messages-container {
        padding: 12px;
        gap: 12px;
    }

    .message {
        max-width: 90%;
    }

    .message-content {
        padding: 8px 12px;
        border-radius: 14px;
    }

    .message-text {
        font-size: 13px;
    }

    .input-area {
        padding: 12px;
        gap: 8px;
    }

    .input-actions {
        gap: 6px;
    }

    .input-btn {
        width: 36px;
        height: 36px;
        font-size: 14px;
    }

    .message-input {
        font-size: 16px;
        padding: 10px 14px;
    }

    .send-btn {
        padding: 10px 16px;
        font-size: 13px;
    }

    .chat-item {
        padding: 10px 12px;
    }

    .chat-preview {
        font-size: 12px;
        margin-left: 44px;
    }

    .sidebar-header {
        padding: 12px;
    }

    .desktop-header {
        display: none;
    }

    .mobile-header {
        display: flex;
    }

    /* .user-info h3 {
        font-size: 15px;
    }

    .user-info p {
        font-size: 11px;
    } */
}

/* 隐藏桌面端不需要的移动端按钮 */
.mobile-menu-btn {
    display: none;
}

[data-btn="back"]{
    display: none;
}

/* 移动端侧边栏覆盖样式 */
@media (max-width: 768px) {
    [data-btn="back"]{
    display: flex;
}
    .sidebar.mobile-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 80%;
        height: 100vh;
        max-height: 100vh;
        z-index: 1000;
        background-color: #ffffff;
        box-shadow: 2px 0 10px rgba(0,0,0,0.1);
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }

    .sidebar.mobile-overlay.show {
        transform: translateX(0);
    }

    .mobile-backdrop {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0,0,0,0.5);
        z-index: 999;
        opacity: 0;
        visibility: hidden;
        transition: opacity 0.3s ease, visibility 0.3s ease;
    }

    .mobile-backdrop.show {
        opacity: 1;
        visibility: visible;
    }
}