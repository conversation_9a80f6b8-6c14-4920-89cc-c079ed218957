<!DOCTYPE html>
<html>
<head lang="en">
    <meta charset="UTF-8">
    <title></title>

    {include file="common:head"}
</head>

<body>
    <div class="menus_r">
        <div id="con_two_1" style="display: block;">
            <div class="maps">
                <i></i>
                <p class="current">您当前的位置：<a href="{:url('Public/main')}">首页</a>&nbsp;>&nbsp;
                    <a>{$coupon_type==0?"普通":"灰色"}试用券</a>
                </p>
                <a href="{:url('add', ['coupon_type'=>$coupon_type])}" class="add-button">添加试用券</a>
            </div>

            <div class="form_con">
                <table class="bor_cen">
                    <thead>
                        <tr class="mid_01">
                            <td class="mid_one"></td>
                            <td class="mid_one">折扣</td>
                            <td class="mid_one">说明</td>
                            {if $coupon_type == 0}
                            <td class="mid_one">开始时间</td>
                            <td class="mid_one">结束时间</td>
                            {else }
                            <td class="mid_one">积分数</td>
                            {/if}
                            <td class="mid_s">操作</td>
                        </tr>
                    </thead>
                    <tbody>
                    {volist name="List" key="k" id="vo"}
                        <tr  class="mid_02">
                            <td class="mid_one">{$k}</td>
                            <td class="mid_one">{$vo.name}%</td>
                            <td class="mid_one">{$vo.description}</td>
                            {if $coupon_type == 0}
                            <!-- 普通试用券 -->
                            <td class="mid_one">{$vo.start_time}</td>
                            <td class="mid_one">{$vo.end_time}</td>
                            {else }
                            <!-- 灰色试用券 -->
                            <td class="mid_one">{$vo.points}</td>
                            {/if}

                            <td class="mid_s">
                                <a href="{:url('del', ['id'=>$vo['id']])}" onclick="return confirm('您确定要删除吗?')" class="delete-c">删除</a>
                                <a href="{:url('edit', ['id'=>$vo['id']])}" class="basic">修改</a>
                                {if $coupon_type == 0}
                                <!-- 普通试用券 -->
                                <a href="{:url('add_gift', ['id'=>$vo['id']])}" class="compile">发放试用券</a>
                                {/if}
                            </td>
                        </tr>
                    {/volist}
                    </tbody>
                </table>

                <div class="interpret">
                    {$List|raw}
                </div>
            </div>
        </div>
    </div>

    {include file="common:foot"}
</body>
</html>