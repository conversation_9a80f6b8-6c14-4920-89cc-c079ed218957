<?php /*a:4:{s:62:"C:\phpstudy_pro\WWW\opendelclub\app\home\view\index\index.html";i:1753162773;s:64:"C:\phpstudy_pro\WWW\opendelclub\app\home\view\public\header.html";i:1753067175;s:64:"C:\phpstudy_pro\WWW\opendelclub\app\home\view\public\footer.html";i:1753075005;s:62:"C:\phpstudy_pro\WWW\opendelclub\app\home\view\public\foot.html";i:1753075005;}*/ ?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0,maximum-scale=1.0,minimum-scale=1.0,user-scalable=no">

    <title><?php echo !empty($tdk['seo_title']) ? htmlentities((string) $tdk['seo_title']) : "HitGen OpenDEL™"; ?></title>
    <!-- SEO 信息 -->
    <meta name="robots" content="index,follow" />
    <meta name="author" content="HitGen" />
    <meta name="copyright" content="Copyright © 2024 HitGen Inc." />
    <meta name="revisit-after" content="7 days" />
    <meta name="generator" content="HitGen OpenDEL™" />
    <meta name="keywords" content="<?php echo htmlentities((string) $tdk['seo_keywords']); ?>" />
    <meta name="description" content="<?php echo htmlentities((string) $tdk['seo_description']); ?>" />

    <link rel="shortcut icon" href="/favicon.ico" />
    <link href="/static/home/<USER>/vendors/swiper-bundle.min.css" rel="stylesheet" />
    <link href="/static/home/<USER>/vendors/animate.min.css" rel="stylesheet" />
    <link href="/static/home/<USER>/vendors/aos.css" rel="stylesheet">
    <link href="/static/home/<USER>/style.css" rel="stylesheet" />
</head>

<body>
    <!-- <section class="bg-[url(/static/home/<USER>/backgrounds/m_bj.jpg)] bg-no-repeat pb-10 bg-size-[100%] relative md:bg-[url(/static/home/<USER>/backgrounds/pc_bj.jpg)] md:bg-cover md:pb-20"> -->
        <section
        class="pb-5 md:pb-20 relative bg-[url(/static/home/<USER>/bg.jpg)] ">
        <span class="absolute top-0 left-0 w-full h-full bg-[url(/static/home/<USER>/tm-left.png)] bg-no-repeat bg-[top_left] z-0 bg-size-[10rem] md:bg-size-[26.9375rem]"></span>
        <span class="absolute top-0 right-0 w-full h-full bg-[url(/static/home/<USER>/tm-right.png)] bg-no-repeat bg-[top_right] z-0 bg-size-[10rem] md:bg-size-[25.0625rem]"></span>

        <div id="header" class="relative z-[9999]">
    <header class="w-11/12 mx-auto md:w-10/12">
        <nav class="nav-wrapper flex justify-between items-center h-[3.125rem] md:h-24 top-0 relative" role="navigation">
            <div class="flex-1 md:flex md:items-center md:gap-5 md:h-full md:relative md:z-0">
                <a href="/">
                    <img src="/static/home/<USER>/logo.png" alt="HitGen OpenDEL™" class="w-24 md:w-[8.4375rem]" />
                </a>
                <ul class="fixed hidden items-center h-[calc(100%-3.125rem)] bottom-0 overflow-auto left-0 z-[99999] w-full bg-white text-base
                md:flex md:gap-x-[3.75rem] md:relative md:bg-transparent md:overflow-visible md:h-full md:flex-1 md:text-xl animate__animated animate__delay"
                    id="nav_list">
                    <li class="md:h-full flex">
                        <a href="/" class="text-[#000]  flex-1 leading-[3.125rem] px-5 border-b border-[#e0eaff]
                        md:border-0 md:leading-none md:flex md:items-center
                        ">Home</a>
                    </li>
                    <li class="relative cursor-pointer md:h-full flex flex-col">
                        <a href="/news/" class="text-[#000] cursor-pointer flex-1 h-full leading-[3.125rem] px-5 flex justify-between items-center border-b border-[#e0eaff] md:border-0">
                            <span>DELHunter</span>
                        </a>
                    </li>
                    <li class="relative flex justify-between items-center border-b border-[#e0eaff] cursor-pointer navigation flex-wrap group
                        md:border-0 md:h-full md:flex-nowrap">
                        <a href="/product/"
                            class="text-[#000] cursor-pointer leading-[3.125rem] px-5 md:px-0 md:leading-none">
                            Services
                        </a>
                        <i class="w-[1rem] h-[.5625rem] inline-block bg-[url(/static/home/<USER>/icons/dow.png)] bg-no-repeat bg-center bg-size-[1rem] ml-2 group-hover:rotate-180 transition-all mr-5
                        md:mr-0
                        "></i>
                        <ul class="
                            secondary static z-50 hidden bg-[#f8fdff]
                            w-full md:absolute md:top-24 md:left-1/2  md:-translate-x-1/2 md:bg-white md:w-[19.5rem] ">
                            <?php if(is_array($menu_product) || $menu_product instanceof \think\Collection || $menu_product instanceof \think\Paginator): $i = 0; $__LIST__ = $menu_product;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?>
                            <li class="<?php echo !empty($vo['service']) ? 'flex items-center justify-between flex-wrap border-b border-t border-[#e0eaff] relative group navigation-item' : 'grid grid-cols-1'; ?> md:pr-[1.25rem] md:px-0">
                                <a href="/product/<?php echo htmlentities((string) $vo['seo_url']); ?>"
                                    class="text-[#000] flex items-center  md:py-5 px-8 h-[3.125rem] md:h-auto">
                                    <?php echo htmlentities((string) $vo['name']); ?>
                                </a>
                                <i class="w-[1rem] h-[.5625rem] inline-block bg-[url(/static/home/<USER>/icons/dow.png)] bg-no-repeat rotate-0 bg-size-[1rem] md:-rotate-90 bg-center ml-2 transition-all mr-5
                                md:mr-0 "></i>
                                <ul class="three-menu w-full hidden bg-[#f8fdff] md:absolute md:top-0 Roboto_Regular md:left-full md:w-full md:bg-[#fff] md:shadow-lg ">
                                    <?php if(is_array($vo['service']) || $vo['service'] instanceof \think\Collection || $vo['service'] instanceof \think\Paginator): $i = 0; $__LIST__ = $vo['service'];if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$v): $mod = ($i % 2 );++$i;?>
                                    <li class="border-b border-t border-[#e0eaff]">
                                        <a href="/service/<?php echo htmlentities((string) $v['seo_url']); ?>" class="
                                        flex items-center px-8 h-[3.125rem] md:h-auto
                                        md:px-8 md:py-5 line-clamp-1 ">
                                            <?php echo htmlentities((string) $v['name']); ?>
                                        </a>
                                    </li>
                                    <?php endforeach; endif; else: echo "" ;endif; ?>
                                </ul>
                            </li>
                            <?php endforeach; endif; else: echo "" ;endif; ?>
                        </ul>
                    </li>

                    <li class="relative flex justify-between items-center border-b border-[#e0eaff] cursor-pointer navigation flex-wrap group
                        md:border-0 md:h-full md:flex-nowrap">
                        <a href="" class="text-[#000] cursor-pointer leading-[3.125rem] px-5 md:px-0 md:leading-none">
                            Resources
                        </a>
                        <i class="w-[1rem] h-[.5625rem] inline-block bg-[url(/static/home/<USER>/icons/dow.png)] bg-no-repeat bg-center ml-2 bg-size-[1rem] group-hover:rotate-180 transition-all mr-5
                        md:mr-0
                        "></i>
                        <ul class="
                        secondary static bg-[#f8fdff] z-50
                        hidden
                        w-full
                        md:absolute
                        md:top-24
                        md:left-1/2
                        md:-translate-x-1/2
                            md:bg-white
                            md:w-[19.5rem] ">
                            <?php if(is_array($menu_resources_category) || $menu_resources_category instanceof \think\Collection || $menu_resources_category instanceof \think\Paginator): $k = 0; $__LIST__ = $menu_resources_category;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($k % 2 );++$k;?>
                            <li class="grid grid-cols-1">
                                <a href="/resources/?tab=<?php echo htmlentities((string) $k); ?>" class="text-[#000] flex items-center md:py-5 px-8 h-[3.125rem] border-b border-[#e0eaff] md:h-auto">
                                    <?php echo htmlentities((string) $vo['name']); ?>
                                </a>
                            </li>
                            <?php endforeach; endif; else: echo "" ;endif; ?>

                            <li class="grid grid-cols-1">
                                <a href="/faq/" class="text-[#000] flex items-center md:py-5 px-8 h-[3.125rem] border-b border-[#e0eaff] md:h-auto">
                                    FAQ
                                </a>
                            </li>
                        </ul>
                    </li>

                    <?php if($basic['forum_status']==1): ?>
                    <li class="md:h-full grid grid-cols-1">
                        <a href="/iCommunity/" class="text-[#000] border-b border-[#e0eaff] leading-[3.125rem] px-5 md:border-0
                        md:leading-none
                        md:flex
                        md:items-center">iCommunity</a>
                    </li>
                    <?php endif; ?>
                </ul>
            </div>

            <div class="flex items-center gap-3.5 md:gap-8 md:relative md:z-20">
                <div class="md:flex md:items-center">
                    <div class="bg-white rounded-full p-1 h-8 w-8 border border-[#dfe7ff] md:w-12 md:h-12 "
                        id="search_btn">
                        <button type="button"
                            class="search-btn w-full h-full bg-[url(/static/home/<USER>/icons/sousuo.png)] bg-size-[1rem] rounded-full bg-no-repeat bg-center md:bg-size-[1.3rem] cursor-pointer"
                            aria-label="搜索"></button>
                    </div>
                    <form action="/search" method="get"
                        class="hidden absolute w-full left-0 top-full md:relative z-10" id="search_form">
                        <div
                            class="bg-[#f8fdff] h-14 md:bg-white md:rounded-full md:ml-2 md:w-[18.75rem] md:h-12 relative flex items-center px-2.5 md:p-0">
                            <input type="text"
                                name="q"
                                value="<?php echo isset($keyword) ? htmlentities((string) $keyword) : ''; ?>"
                                class="border flex-1 rounded-md h-10 px-5 border-[#e0eaff] bg-white md:rounded-full md:ml-2 md:w-full md:h-12 md:px-5"
                                placeholder="Enter the keyword" />
                            <button type="submit" tabindex="-1"
                                class="w-8 h-8 md:absolute md:right-[5px] md:top-1/2 md:-translate-y-1/2 md:w-[2.375rem] md:h-[2.375rem] bg-[url(/static/home/<USER>/icons/sousuo.png)] rounded-full bg-no-repeat bg-center bg-size-[1rem] md:bg-size-[1.3rem] z-10 cursor-pointer"
                                id="submit_btn"></button>
                        </div>
                    </form>
                </div>

                <?php if(session('userId')): ?>
                <div class="bg-white rounded-full p-1 h-8 w-8 border border-[#dfe7ff] relative cursor-pointer md:w-12 md:h-12 flex justify-center items-center"
                 id="message_btn" onmouseenter="showMessageDrop()" onmouseleave="hideMessageDrop()"
                    onclick="toggleMessageDrop()">
                    <div class="btn-message">
                        <div class="w-full h-full flex items-center justify-center">
                            <img src="/static/home/<USER>/icons/lingdang.png" alt="" class="w-[1rem] md:w-[1.2rem]">
                        </div>
                        <?php if($user_message_count>0): ?>
                        <div class="absolute -top-1 -right-[.525rem] text-white text-center" id="message_num">
                            <div class="min-w-[1rem] min-h-[1rem] px-1 text-[.75rem] bg-[#ff0000] rounded-full md:min-w-[1.25rem] md:h-[1.25rem] md:leading-[1.25rem]">
                                <span><?php echo htmlentities((string) $user_message_count); ?></span>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>

                    <!-- Message下拉通知 -->
                    <div class="absolute top-full left-[10%] -translate-x-1/2 z-20 min-w-[15.625rem] cursor-default pt-3
                                md:min-w-[25rem] md:-left-[100%]" id="message_drop" onclick="event.stopPropagation()" style="display: none;">
                        <?php if(count($user_message)>0): ?>
                        <div class="bg-white shadow-2xl rounded-tl-xl rounded-tr-xl">
                            <div class="message-header flex items-center justify-between p-3 border-b border-[#e0eaff] text-base md:text-xl md:py-[1.25rem] md:px-[1.875rem]">
                                <h3 class="font-bold">Tooltip</h3>
                                <button type="button"
                                    class="w-[1rem] h-[1rem] bg-[url(/static/home/<USER>/icons/shanchu.png)] bg-no-repeat bg-size-[.95rem] cursor-pointer md:w-[1.25rem] md:h-[1.375rem] md:bg-size-[1rem]"
                                    id="EmptyMessage"></button>
                            </div>
                            <div class="message-tips-list" id="message_cont">
                                <ul>
                                    <?php if(is_array($user_message) || $user_message instanceof \think\Collection || $user_message instanceof \think\Paginator): $i = 0; $__LIST__ = $user_message;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?>
                                    <li>
                                        <a href="/user/message" class="flex items-center line-clamp-1 gap-x-1">
                                            <strong>[System]</strong><span><?php echo htmlentities((string) $vo['content']); ?></span>
                                        </a>
                                    </li>
                                    <?php endforeach; endif; else: echo "" ;endif; ?>
                                </ul>
                            </div>
                            <!-- <div class="flex items-center justify-center py-4 gap-2.5" id="message_more">
                                <a href="/user/message">
                                    <strong class="text-sm md:text-xl"> View All </strong>
                                </a>
                                <img src="/static/home/<USER>/icons/changjiantou-zuoshang2.png" alt="" class="w-[1rem]">
                            </div> -->
                            <!-- 清空/暂无消息时候显示 -->
                            <div class="text-center py-4 text-[#f08411] no-message hidden" id="no_message">
                                <span class="text-sm md:text-xl">No message</span>
                            </div>
                        </div>
                        <?php endif; if(count($user_icommunity)>0 && $basic['letter_status']==1): ?>
                        <div class="bg-white shadow-2xl rounded-bl-xl rounded-br-xl">
                            <div class="message-header flex items-center justify-between p-3 border-b border-[#e0eaff] text-base md:text-xl md:py-[1.25rem] md:px-[1.875rem]">
                                <h3 class="font-bold">iCommunity</h3>
                                <button type="button"
                                    class="w-[1rem] h-[1rem] bg-[url(/static/home/<USER>/icons/shanchu.png)] bg-no-repeat bg-size-[.95rem] cursor-pointer md:w-[1.25rem] md:h-[1.375rem] md:bg-size-[1rem]"
                                    id="EmptyMessage"></button>
                            </div>
                            <div class="message-tips-list" id="message_cont">
                                <ul>
                                    <?php if(is_array($user_icommunity) || $user_icommunity instanceof \think\Collection || $user_icommunity instanceof \think\Paginator): $i = 0; $__LIST__ = $user_icommunity;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?>
                                    <li>
                                        <a href="/user/private-message" class="flex items-center line-clamp-1 gap-x-1">
                                            <strong>[<?php echo htmlentities((string) $vo['first_name']); ?> <?php echo htmlentities((string) $vo['last_name']); ?>]</strong>
                                            <span><?php echo htmlentities((string) $vo['content']); ?></span>
                                        </a>
                                    </li>
                                    <?php endforeach; endif; else: echo "" ;endif; ?>
                                </ul>
                            </div>
                            <!-- <div class="flex items-center justify-center py-4 gap-2.5" id="message_more">
                                <a href="/user/private-message">
                                    <strong class="text-sm md:text-xl"> View All </strong>
                                </a>
                                <img src="/static/home/<USER>/icons/changjiantou-zuoshang2.png" alt="" class="w-[1rem]">
                            </div> -->
                            <!-- 清空/暂无消息时候显示 -->
                            <div class="text-center py-4 text-[#f08411] no-message hidden" id="no_message">
                                <span class="text-sm md:text-xl">No message</span>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="w-8 h-8 rounded-full md:w-[3.125rem] md:h-[3.125rem] relative cursor-pointer btn-name">
                    <div class="user-name">
                        <img src="<?php echo isset($user['avatar']) ? htmlentities((string) $user['avatar']) : '/static/home/<USER>/user-1.jpg'; ?>" class="w-full h-full rounded-full object-cover" alt="<?php echo session('userEmail'); ?>" />
                    </div>
                    <!-- 登录以后的下拉 -->
                    <div class="user-name-drop absolute top-full -left-[50%] -translate-x-1/2 min-w-[12.5rem] pt-3 z-20 md:right-0 md:min-w-[15.625rem] md:-left-full cursor-default" onclick="event.stopPropagation()">
                        <div class="bg-white p-[1.25rem] shadow-2xl rounded-xl  md:p-6 ">
                            <div class="text-center mb-3">
                                <img src="<?php echo isset($user['avatar']) ? htmlentities((string) $user['avatar']) : '/static/home/<USER>/user-1.jpg'; ?>" alt="" class="w-[4rem] h-[4rem] mx-auto rounded-full" />
                                <p class="mt-[.625rem] text-lg">
                                    <strong class="line-clamp-1"><?php echo htmlentities((string) $user['first_name']); ?> <?php echo htmlentities((string) $user['last_name']); ?></strong>
                                </p>
                            </div>
                            <div class="text-sm md:text-xl">
                                <div class="border-b py-2  px-2 border-[#e0eaff]">
                                    <a href="/user/">
                                        Personal Center
                                    </a>
                                </div>
                                <div class="py-2 px-2">
                                    <a href="/logout">
                                        Sign Out
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <?php else: ?>
                <div class="flex items-center h-8 min-w-20">
                    <button aria-label="登录账户"
                        class="bg-[#f08411] h-full w-full text-white cursor-pointer bg-[url(/static/home/<USER>/icons/yonghu.png)] bg-no-repeat bg-size-[0.8125rem] bg-[0.75rem_center] rounded-lg text-sm pl-6 pr-2 md:w-32 md:h-12 md:bg-size-[1.1rem] md:text-xl">
                        <a href="/login/">Login</a>
                    </button>
                </div>
                <?php endif; ?>

                <div class="menu min-w-8 h-5 ml-2 md:hidden">
                    <button aria-label="打开菜单"
                        class="h-full w-full bg-[url(/static/home/<USER>/icons/menu.png)] bg-no-repeat bg-center bg-size-[1.3rem] cursor-pointer"
                        id="menu_btn"></button>
                </div>
        </nav>
    </header>
</div>

        <article class="w-11/12 mx-auto pt-8 md:pt-28 md:mb-20 md:w-10/12">
            <div class="Roboto_LightItalic flex justify-center mb-4 md:mb-12" data-aos="fade-right">
                <span
                    class="bg-[rgba(102,102,102,0.1)] text-[#333] inline-block px-4 py-2 rounded-full rounded-bl-none text-base md:w-[18.75rem] md:h-12 md:text-xl md:text-center"
                    role="doc-subtitle">
                    “ Welcome to OpenDEL™ ”</span>
            </div>
            <div class="w-11/12 text-center mx-auto flex flex-col md:items-center" data-aos="fade-up">
                <h1 class="text-6xl text-[#333333] md:text-8xl md:mb-10">HitGen OpenDEL™</h1>
                <p class="text-[#666] text-sm text-center mt-4 md:text-xl md:max-w-[37.5rem] md:mb-10">
                    Facilitate internal management and customer communication, enhance
                    customer stickiness, improve customer experience, and expand
                    influence.
                </p>
                <div class="bg-[#105eb3] relative mt-8 rounded-md md:w-[18.75rem]">
                    <a href="/about" class="flex justify-center items-center gap-2 text-white text-sm py-3 md:text-xl md:h-16 md:justify-evenly">
                        <span> Learn More </span>
                        <img src="/static/home/<USER>/icons/changjiantou-zuoshang.png" alt="箭头图标"
                            class="w-3 absolute right-7 md:w-[1.2rem] md:relative md:right-0" />
                    </a>
                </div>
            </div>
        </article>

        <section class="swiper swiper-banner mt-7 w-11/12 mx-auto relative pb-11 md:w-10/12" data-aos="fade-up">
            <div class="swiper-wrapper">
                <?php if(is_array($banner) || $banner instanceof \think\Collection || $banner instanceof \think\Paginator): $i = 0; $__LIST__ = $banner;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?>
                <div class="swiper-slide">
                    <a href="/">
                        <picture class="w-full">
                            <source media="(min-width: 37.5625rem)" srcset="<?php echo htmlentities((string) $vo['image']); ?>"
                                class="w-full rounded-xl object-cover" />
                            <img src="<?php echo htmlentities((string) $vo['smt_image']); ?>" alt="banner_01" class="w-full rounded-xl object-cover" />
                        </picture>
                    </a>
                </div>
                <?php endforeach; endif; else: echo "" ;endif; ?>
            </div>

            <div class="swiper-pagination swiper-banner-pagination bottom-3 md:bottom-10"></div>
            <div class="md:bottom-10 absolute bottom-3 right-4 z-9 flex items-center justify-center gap-5 md:right-10">
                <div
                    class=" swiper-banner-prev w-5 h-5 md:w-[0.75rem] md:h-[1.1875rem] bg-[url(/static/home/<USER>/icons/banner_l.png)] bg-no-repeat bg-center bg-size-[0.5rem_0.75rem] md:bg-size-[0.7rem] cursor-pointer">
                </div>
                <div
                    class="swiper-banner-next w-5 h-5 md:w-[0.75rem] md:h-[1.1875rem] bg-[url(/static/home/<USER>/icons/banner_r.png)] bg-no-repeat bg-center bg-size-[0.5rem_0.75rem] md:bg-size-[0.7rem] cursor-pointer">
                </div>
            </div>

            <div
                class="number-pagination absolute left-4 bottom-3 z-50 text-white Roboto_Light flex items-center gap-2 text-[0.75rem] md:left-10 md:bottom-10">
            </div>
        </section>
    </section>

    <main class="w-full">
        <section class=" bg-[#f5f9ff]">
            <div class="w-11/12 mx-auto flex flex-col pb-6 md:w-10/12 relative">
                <header class="pt-8 pb-6 md:pt-20 md:pb-14">
                    <h2 class="Roboto_Bold text-2xl text-[#111] md:text-5xl">
                        Latest Progress of DEL
                    </h2>
                </header>
                <section class="flex flex-col gap-y-4 md:flex-row md:gap-x-8">
                    <article class="md:flex-1" data-aos="fade-right">
                        <ul role="list" class="grid grid-cols-1 border border-[#e0eaff] md:grid-cols-2 md:h-[37.4375rem]">
                            <?php if(is_array($news) || $news instanceof \think\Collection || $news instanceof \think\Paginator): $k = 0; $__LIST__ = $news;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($k % 2 );++$k;?>
                            <li class="flex group flex-col bg-white px-5 py-4 gap-3 border-b border-[#e0eaff] md:order-1 md:px-10 md:py-7 md:gap-0 md:relative <?php echo $k % 2 === 1 ? 'md:border-r border-[#e0e0ff]' : ''; ?>">
                                <div class="text-sm md:mb-6 md:text-base">
                                    <span class="text-[<?php echo htmlentities((string) $vo['color']); ?>] min-w-12 bg-[<?php echo htmlentities((string) $vo['backgroud_color']); ?>] text-center py-0.5 px-1.5 rounded-sm mr-1" style="color: <?php echo htmlentities((string) $vo['color']); ?>; background-color: <?php echo htmlentities((string) $vo['backgroud_color']); ?>"><?php echo htmlentities((string) $vo['name']); ?></span>
                                    <time><?php echo date('j F Y', strtotime($vo['publish_date'])); ?></time>
                                </div>
                                <div>
                                    <h3 class="text-xl Roboto_Bold mb-2.5 line-clamp-1 md:text-3xl md:mb-5">
                                        <a href="/news/<?php echo htmlentities((string) $vo['seo_url']); ?>">
                                            <?php echo htmlentities((string) $vo['title']); ?>
                                        </a>
                                    </h3>
                                    <p class="line-clamp-2 text-[#999] text-sm md:text-xl">
                                        <?php echo htmlentities((string) strip_tags($vo['content'])); ?>
                                    </p>
                                </div>
                                <div class="flex justify-end mt-2.5 md:absolute md:bottom-7 md:right-10 transition-all group-hover:right-5">
                                    <a href="/news/<?php echo htmlentities((string) $vo['seo_url']); ?>" class="flex items-center gap-1.5 md:gap-x-3">
                                        <span class="text-[#333] text-sm md:text-base">Learn More</span>
                                        <img src="/static/home/<USER>/icons/changjiantou-r.png" alt="more" class="w-4 inline-block md:w-[1.2rem] md:opacity-30 group-hover:opacity-100" />
                                    </a>
                                </div>
                            </li>
                            <?php endforeach; endif; else: echo "" ;endif; ?>
                        </ul>
                    </article>
                    <aside class="md:w-[34.5rem] md:h-[37.4375rem]" data-aos="fade-left">
                        <figure>
                            <img src="/static/home/<USER>/BHEEHGGIAFIJB-a0bqJwnT0B.jpg" alt="Latest DEL Progress Image" class="object-cover w-full rounded-tr-3xl" />
                            <figcaption class="sr-only">
                                Latest Progress Visual Overview
                            </figcaption>
                        </figure>
                    </aside>
                </section>

                <div class="bg-[#ffffff] relative mt-4 rounded-md border border-[#dae9ff] md:absolute md:right-0 md:top-14 md:w-[16.25rem] group ">
                    <a href="/news" class="flex justify-center items-center gap-2 text-[#155797] text-sm py-3 md:text-xl md:h-[3.75rem] md:justify-evenly">
                        <span> Learn More </span>
                        <img src="/static/home/<USER>/icons/changjiantou-zuoshang_b.png" alt="more" class="w-3 absolute right-7 transition-all md:w-[1rem] group-hover:right-2 md:relative md:right-0" />
                    </a>
                </div>
            </div>
        </section>

        <section class="relative" data-aos="fade-up">
            <header class="w-11/12 mx-auto pt-8 pb-6 md:w-10/12 md:p-0 md:flex md:justify-between md:items-center md:pt-20 md:pb-14">
                <h2 class="Roboto_Bold text-2xl text-[#111] md:text-5xl">User Service</h2>
                <div class="bg-[#ffffff] relative mt-4 rounded-md border border-[#dae9ff] group md:w-[16.25rem] md:block hidden">
                    <a href="/product" class="flex justify-center items-center gap-2 text-[#155797] text-sm py-3 md:text-xl md:h-[3.75rem] md:justify-evenly">
                        <span> Learn More </span>
                        <img src="/static/home/<USER>/icons/changjiantou-zuoshang_b.png" alt="more"
                            class="w-3 absolute right-7 transition-all md:w-[1rem] group-hover:right-2 md:relative md:right-0">
                    </a>
                </div>
            </header>
            <div class="swiper swiper-service relative">
                <div class="swiper-wrapper">
                    <div class="swiper-slide">
                        <style>
                            .swiper-1{
                                background-image: url('<?php echo htmlentities((string) $product['image_background_smt']); ?>');

                            }
                            @media (min-width: 768px) {
                               .swiper-1{ background-image: url('<?php echo htmlentities((string) $product['image_background']); ?>');
                               }
                            }
                        </style>
                        <div class="swiper-1 swiper-slide-content min-h-lvh bg-no-repeat bg-center bg-cover py-10 md:h-[62.5rem] md:min-h-auto md:bg-center overflow-hidden md:pt-24">
                            <div class="w-11/12 mx-auto md:w-10/12 flex flex-col gap-y-20 md:flex-row md:items-center md:pr-[9.375rem]">
                                <!-- 文字 -->
                                <div class="order-2 flex-1/2 md:order-1">
                                    <article class="product-introduction">
                                        <h3 class="text-lg Roboto_Bold text-[#111] mb-2 md:text-4xl md:mb-5"><?php echo htmlentities((string) $product['name']); ?></h3>
                                        <p class="text-sm line-clamp-2 mb-3.5 md:text-xl">
                                            <?php echo htmlentities((string) $product['title']); ?>
                                        </p>
                                        <?php echo $product['content']; ?>
                                    </article>
                                    <section class="flex items-center gap-3 mt-5 md:mt-10 relative z-30">
                                        <a href="/product/<?php echo htmlentities((string) $product['seo_url']); ?>" class="flex justify-around items-center border border-[#155797] gap-2 text-[#155797] text-sm py-2.5 min-w-36 rounded-md md:w-[16.25rem] md:h-[3.75rem] md:text-xl md:rounded-lg">
                                            <span> Learn More </span>
                                            <img src="/static/home/<USER>/icons/changjiantou-zuoshang_b.png" alt="more" class="w-3 right-7 md:w-[1rem]" />
                                        </a>
                                        <a class="quote-btn flex min-w-36 justify-around items-center border border-[#f08411] gap-2 text-[#f08411] text-sm py-2.5 cursor-pointer rounded-md md:w-[16.25rem] md:h-[3.75rem] md:text-xl md:rounded-lg"
                                            href="<?php echo session('userId') ? '/quote/logged?product_id='. $product['id'] : 'javascript:;'; ?>"
                                            onclick="return <?php echo session('userId') ? 'true' : 'showQuotePopup(\'product_id=' . $product['id'] . '\')'; ?>">
                                            <span> Quote </span>
                                            <img src="/static/home/<USER>/icons/gouwuche-2.png" alt="more" class="w-4 right-7 md:w-[1.25rem]" />
                                        </a>
                                    </section>
                                </div>
                                <!-- 图片 -->
                                <div class="order-1 flex-1/2 flex items-center md:order-2">
                                    <div class="w-80 h-80 mx-auto rounded-full relative hollow-number-ripple md:w-[42.5rem] md:h-[42.5rem]">
                                        <figure class="animate__animated animate__delay-.5s hollow-number border border-white rounded-full animate__fadeInRight">
                                            <img src="<?php echo htmlentities((string) $product['image']); ?>" alt="case_01"
                                                class="w-full object-cover rounded-full" />
                                            <figcaption class="sr-only"><?php echo htmlentities((string) $product['name']); ?></figcaption>
                                        </figure>
                                        <span
                                            class="animate__animated animate__delay-1s hollow-number Roboto_Bold absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 text-9xl text-transparent md:text-[12.5rem] animate__fadeInRight">
                                            01
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php if(is_array($product['services']) || $product['services'] instanceof \think\Collection || $product['services'] instanceof \think\Paginator): $k = 0; $__LIST__ = $product['services'];if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($k % 2 );++$k;?>
                        <style>
                            .swiper-1-<?php echo htmlentities((string) $k+1); ?>{
                                background-image: url('<?php echo htmlentities((string) $vo['image_background_smt']); ?>');

                            }
                            @media (min-width: 768px) {
                               .swiper-1-<?php echo htmlentities((string) $k+1); ?>{ background-image: url('<?php echo htmlentities((string) $vo['image_background']); ?>');
                               }
                            }
                        </style>
                    <div class="swiper-slide">

                        <div class="swiper-1-<?php echo htmlentities((string) $k+1); ?> swiper-slide-content min-h-lvh bg-no-repeat bg-center bg-cover py-10 md:h-[62.5rem] md:min-h-auto md:bg-center overflow-hidden md:pt-24">
                            <div class="w-11/12 mx-auto md:w-10/12 flex flex-col gap-y-20 md:flex-row md:items-center md:pr-[9.375rem]">
                                <!-- 文字 -->
                                <div class="order-2 flex-1/2 md:order-1">
                                    <article class="product-introduction">
                                        <h3 class="text-lg Roboto_Bold text-[#111] mb-2 md:text-4xl md:mb-5"><?php echo htmlentities((string) $vo['name']); ?></h3>
                                        <?php echo $vo['description']; ?>
                                    </article>
                                    <section class="flex items-center gap-3 mt-5 md:mt-10 relative z-30">
                                        <a href="/service/<?php echo htmlentities((string) $vo['seo_url']); ?>"
                                            class="flex justify-around items-center border border-[#155797] gap-2 text-[#155797] text-sm py-2.5 min-w-36 rounded-md md:w-[16.25rem] md:h-[3.75rem] md:text-xl md:rounded-lg">
                                            <span> Learn More </span>
                                            <img src="/static/home/<USER>/icons/changjiantou-zuoshang_b.png" alt="more"
                                                class="w-3 right-7 md:w-[1rem]" />
                                        </a>
                                        <a
                                            class="quote-btn flex min-w-36 justify-around items-center border border-[#f08411] gap-2 text-[#f08411] text-sm py-2.5 cursor-pointer rounded-md md:w-[16.25rem] md:h-[3.75rem] md:text-xl md:rounded-lg"
                                            href="<?php echo session('userId') ? '/quote/logged?service_id='. $vo['id'] : 'javascript:;'; ?>"
                                            onclick="return <?php echo session('userId') ? 'true' : 'showQuotePopup(\'service_id=' . $vo['id'] . '\')'; ?>">
                                            <span> Quote </span>
                                            <img src="/static/home/<USER>/icons/gouwuche-2.png" alt="more"
                                                class="w-4 right-7 md:w-[1.25rem]" />
                                        </a>
                                    </section>
                                </div>
                                <!-- 图片 -->
                                <div class="order-1 flex-2/3 flex items-center md:order-2">
                                    <div
                                        class="w-80 h-80 mx-auto rounded-full relative hollow-number-ripple md:w-[42.5rem] md:h-[42.5rem]">
                                        <!-- <span
                                            class="absolute inline-flex h-full w-full animate-ping rounded-full bg-white opacity-75 -z-[1]"></span> -->
                                        <figure class="animate__animated animate__delay-.5s hollow-number border border-white rounded-full animate__fadeInRight">
                                            <img src="<?php echo htmlentities((string) $vo['image']); ?>" alt="case_01"
                                                class="w-full object-cover rounded-full" />
                                            <figcaption class="sr-only"><?php echo htmlentities((string) $vo['name']); ?></figcaption>
                                        </figure>
                                        <span
                                            class="animate__animated animate__delay-1s hollow-number Roboto_Bold absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 text-9xl text-transparent md:text-[12.5rem] animate__fadeInRight">
                                            <?php echo htmlentities((string) str_pad($k+1,2,0,STR_PAD_LEFT)); ?>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; endif; else: echo "" ;endif; ?>
                </div>
                <div class="absolute flex items-center justify-center w-full top-96 z-50 gap-x-10 md:right-1/12 md:top-1/3 md:-translate-y-1/3 md:w-20 md:flex-col md:gap-y-10">
                    <div class=" swiper-service-prev w-10 h-10 border border-white rounded-full bg-[url(/static/home/<USER>/icons/banner_l.png)] bg-no-repeat bg-center bg-size-[0.5rem_0.75rem] cursor-pointer md:order-2 md:w-20 md:h-20 md:bg-size-[.8rem]">
                    </div>
                    <div class="service-pagination text-white Roboto_Light flex items-center gap-2 text-[0.75rem] md:order-1">
                    </div>

                    <div class="swiper-service-next w-10 h-10 border border-white rounded-full bg-[url(/static/home/<USER>/icons/banner_r.png)] bg-no-repeat bg-center bg-size-[0.5rem_0.75rem] cursor-pointer md:order-2 md:w-20 md:h-20 md:bg-size-[.8rem]">
                    </div>
                </div>
            </div>
            <!-- 缩略图 -->
            <div class="hidden md:block h-[9.375rem] absolute bottom-0 z-10 w-full bg-[rgba(208,221,234,0.2)]">
                <div class="w-10/12  mx-auto">
                    <div class="swiper swiper-service-thumb">
                        <div class="swiper-wrapper">
                            <div class="swiper-slide text-[#feffff]">
                                <div
                                    class=" h-[9.375rem] cursor-pointer flex flex-col justify-center items-center gap-y-2 Roboto_Bold">
                                    <h3 class="text-4xl">01</h3>
                                    <p class="text-xl text-center">
                                        <?php echo htmlentities((string) $product['name']); ?>
                                    </p>
                                </div>
                            </div>
                            <?php if(is_array($product['services']) || $product['services'] instanceof \think\Collection || $product['services'] instanceof \think\Paginator): $k = 0; $__LIST__ = $product['services'];if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($k % 2 );++$k;?>
                            <div class="swiper-slide text-[#feffff]">
                                <div
                                    class=" h-[9.375rem] cursor-pointer flex flex-col justify-center items-center gap-y-2 Roboto_Bold">
                                    <h3 class="text-4xl"><?php echo htmlentities((string) str_pad($k+1,2,0,STR_PAD_LEFT)); ?></h3>
                                    <p class="text-xl text-center">
                                        <?php echo htmlentities((string) $vo['name']); ?>
                                    </p>
                                </div>
                            </div>
                            <?php endforeach; endif; else: echo "" ;endif; ?>
                        </div>
                    </div>
                </div>
            </div>
            <div class="bg-[#ffffff] relative mt-4 rounded-md border border-[#dae9ff] group w-11/12 mx-auto md:hidden">
                <a href="" class="flex justify-center items-center gap-2 text-[#155797] text-sm py-3 md:text-xl md:h-[3.75rem] md:justify-evenly">
                    <span> Learn More </span>
                    <img src="/static/home/<USER>/icons/changjiantou-zuoshang_b.png" alt="more"
                        class="w-3 absolute right-7 transition-all md:w-[1rem] group-hover:right-2 md:relative md:right-0">
                </a>
            </div>
        </section>
        <style>
            .image>img{
                width: auto;
            }
        </style>
        <section class="flex flex-col gap-y-6 w-11/12 mx-auto pt-8 pb-6 md:flex-row md:gap-x-24 md:p-0 md:w-10/12">
            <header class="md:border-r md:border-[#dae9ff] md:flex-1" data-aos="fade-right">
                <div class="md:max-w-[35.1875rem] md:flex flex-col md:gap-y-4 md:justify-center md:h-full">
                    <h2 class="text-xl Roboto_Bold text-[#333] md:text-5xl">
                        What are people in the community saying?
                    </h2>
                    <p class="text-sm text-[#999] mt-2 md:text-xl md:leading-9 md:mb-10">
                        our clients feedback speaks volumes.discover their stories of success,collaboration,and the
                        impactful results we ve delivered together
                    </p>
                    <div class="bg-[#ffffff] relative hidden md:block group">
                        <a href="/iCommunity/" class="flex justify-center items-center gap-2 text-[#155797] text-sm py-3 md:text-xl md:justify-evenly md:w-[21.625rem] md:h-[3.75rem] border border-[#dae9ff] rounded-md">
                            <span> Explore the community </span>
                            <img src="/static/home/<USER>/icons/changjiantou-zuoshang_b.png" alt="more" class="w-3 absolute right-7 group-hover:right-5 md:right-0 md:w-[1rem] md:h-auto md:relative transition-all">
                        </a>
                    </div>
                </div>
            </header>
            <div class="flex flex-col md:flex-row md:gap-x-5 md:flex-1 md:max-w-2xl">
                <div class="l myscroll h-[25rem] overflow-hidden md:h-[62.5rem]">
                    <ul class="flex flex-col gap-y-3 md:gap-y-5">
                        <?php if(is_array($all_post[0]) || $all_post[0] instanceof \think\Collection || $all_post[0] instanceof \think\Paginator): $i = 0; $__LIST__ = $all_post[0];if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?>
                        <li class="border border-[#dae9ff] rounded-sm px-5 py-5 flex flex-col gap-y-3 bg-white md:p-10  overflow-hidden md:rounded-xl">
                            <div class="flex gap-x-3 items-center md:mb-5">
                                <img src="<?php echo isset($vo['avatar']) ? htmlentities((string) $vo['avatar']) : '/static/home/<USER>/user-1.jpg'; ?>" alt="<?php echo htmlentities((string) $vo['user']['first_name']); ?>" class="w-10 h-10 rounded-lg md:w-20 md:h-20 border border-[#dae9ff]">
                                <div class="md:ml-2.5">
                                    <strong class="text-[#1f1f1f] md:text-xl"><?php echo htmlentities((string) $vo['user']['first_name']); ?> <?php echo htmlentities((string) $vo['user']['last_name']); ?></strong>
                                    <p class="text-[#999] text-sm md:text-base">
                                        <?php echo htmlentities((string) $vo['user']['role_name']); ?>
                                    </p>
                                </div>
                            </div>
                            <div class="text-[#666] text-sm md:text-lg line-clamp-5">
                                <p>
                                    <?php echo $vo['content']; ?>
                                </p>
                            </div>
                        </li>
                        <?php endforeach; endif; else: echo "" ;endif; ?>
                    </ul>
                </div>
                <div class="l myscroll-down h-[25rem] overflow-hidden hidden md:block md:h-[62.5rem]">
                    <ul class="flex flex-col gap-y-3 md:gap-y-5">
                        <?php if(is_array($all_post[1]) || $all_post[1] instanceof \think\Collection || $all_post[1] instanceof \think\Paginator): $i = 0; $__LIST__ = $all_post[1];if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?>
                        <li class="border border-[#dae9ff] rounded-sm px-5 py-5 flex flex-col gap-y-3 bg-white md:p-10 overflow-hidden md:rounded-xl">
                            <div class="flex gap-x-3 items-center md:mb-5">
                                <img src="<?php echo isset($vo['avatar']) ? htmlentities((string) $vo['avatar']) : '/static/home/<USER>/user-1.jpg'; ?>" alt="<?php echo htmlentities((string) $vo['user']['first_name']); ?>"
                                    class="w-10 h-10 rounded-lg md:w-20 md:h-20 border border-[#dae9ff]">
                                <div class="md:ml-2.5">
                                    <strong class="text-[#1f1f1f] md:text-xl"><?php echo htmlentities((string) $vo['user']['first_name']); ?> <?php echo htmlentities((string) $vo['user']['last_name']); ?></strong>
                                    <p class="text-[#999] text-sm md:text-base">
                                        <?php echo htmlentities((string) $vo['user']['role_name']); ?>
                                    </p>
                                </div>
                            </div>
                            <div class="text-[#666] text-sm md:text-lg line-clamp-5">
                                <p>
                                    <?php echo $vo['content']; ?>
                                </p>
                            </div>
                        </li>
                        <?php endforeach; endif; else: echo "" ;endif; ?>
                    </ul>
                </div>
            </div>
            <div class="bg-[#ffffff] relative rounded-md border border-[#dae9ff] md:hidden">
                <a href="" class="flex justify-center items-center gap-2 text-[#155797] text-sm py-3">
                    <span> Explore the community </span>
                    <img src="/static/home/<USER>/icons/changjiantou-zuoshang_b.png" alt="more"
                        class="w-3 absolute right-7 :hover:right-6">
                </a>
            </div>
        </section>

        <!--
        <section class="bg-[#f5f9ff] py-6 md:py-44">
            <div class="w-11/12 mx-auto md:w-10/12 md:flex md:justify-between md:gap-40">
                <div class="bg-[url(/static/home/<USER>/img_01.jpg)] bg-no-repeat bg-cover h-[18.75rem] flex flex-col px-8 justify-around mb-5 md:w-[33.75rem] md:h-[42.5rem] md:rounded-tl-4xl md:px-16">
                    <header class="text-white">
                        <h2 class="text-xl Roboto_Bold mb-2.5 md:text-5xl md:mb-8">OpenDEL FAQ</h2>
                        <p class="text-sm md:text-xl md:leading-10">
                            Here you can find all the answers you don't understand. Welcome to ask new questions and we
                            will help you with them.
                        </p>
                    </header>
                    <div class="bg-[#ffffff] relative rounded-md border border-[#dae9ff] w-full md:w-[16.25rem]">
                        <a href="/faq"
                            class="flex justify-center items-center gap-2 text-[#155797] text-sm py-3 md:text-xl md:h-[3.75rem]">
                            <span> Learn More </span>
                            <img src="/static/home/<USER>/icons/changjiantou-zuoshang_b.png" alt="more"
                                class="w-3 absolute right-7 :hover:right-6">
                        </a>
                    </div>
                </div>
                <div class="flex flex-col md:flex-1" data-aos="fade-left">
                    <ul role="list" class="flex flex-col gap-y-2 md:gap-y-[1.125rem]">
                        <?php if(is_array($faq) || $faq instanceof \think\Collection || $faq instanceof \think\Paginator): $i = 0; $__LIST__ = $faq;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?>
                        <li class="bg-white border border-[#dae9ff] rounded-md group">
                            <a href="/faq/<?php echo htmlentities((string) $vo['seo_url']); ?>"
                                class="flex justify-between items-center px-4 py-3 gap-x-3 h-[3.125rem] md:h-24 md:px-12">
                                <div
                                    class="text-sm line-clamp-1 flex-1 md:text-xl group-hover:opacity-45 transition-all">
                                    <?php echo htmlentities((string) $vo['question']); ?>
                                </div>
                                <img src="/static/home/<USER>/icons/add.png" alt="more"
                                    class="w-3 h-3 text-[#333] md:w-auto  md:h-auto" />
                            </a>
                        </li>
                        <?php endforeach; endif; else: echo "" ;endif; ?>
                    </ul>
                </div>
            </div>
        </section>
        -->

        <section class="w-11/12 mx-auto pb-6 md:w-10/12 relative md:pb-44" data-aos="fade-up">
            <header class="pt-8 pb-6 md:pt-20 md:pb-14">
                <h2 class="text-2xl Roboto_Bold mb-2.5 text-[#111] md:text-5xl">
                    OpenDEL™ Resources
                </h2>
            </header>

            <div class="flex flex-col gap-y-6 mb-6 md:flex-row md:gap-x-6">
                <?php if(is_array($resource) || $resource instanceof \think\Collection || $resource instanceof \think\Paginator): $k = 0; $__LIST__ = $resource;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($k % 2 );++$k;?>
                <div class="bg-white p-6 border border-[#e0eaff] rounded-lg md:flex-1 md:rounded-xl md:p-10" style="box-shadow: 0 0 1.25rem -0.125rem rgba(196,220,255,0.6);">
                    <div class="flex items-start justify-between mb-2">
                        <figure>
                            <div class="w-10 h-10 rounded-full bg-[rgba(21,87,151,0.1)] flex items-center justify-center mb-2 md:w-20 md:h-20 md:mb-6">
                                <img src="<?php echo htmlentities((string) $vo['icon']); ?>" alt="" class="w-4 md:w-[1.8rem]">
                            </div>
                            <figcaption class="text-[#111111] md:text-3xl Roboto_Bold"><?php echo htmlentities((string) $vo['name']); ?></figcaption>
                        </figure>
                        <div class="w-10 h-10 rounded-full bg-[#fff] border border-[#dae9ff] md:w-20 md:h-20">
                            <a href="/resources/?tab=<?php echo htmlentities((string) $k); ?>" class="h-full w-full flex items-center justify-center">
                                <img src="/static/home/<USER>/icons/changjiantou-zuoshang_b.png" alt="" class="w-4 h-4 md:w-[1.4rem] md:h-auto">
                            </a>
                        </div>
                    </div>
                    <ul role="list">
                        <?php if(is_array($vo['files']) || $vo['files'] instanceof \think\Collection || $vo['files'] instanceof \think\Paginator): $i = 0; $__LIST__ = $vo['files'];if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$v): $mod = ($i % 2 );++$i;?>
                        <li class="border-[#e0eaff] border-b py-4 flex items-center justify-between gap-x-2 md:py-6">
                            <div class="flex items-center flex-1">
                                <span class="uppercase text-xs border border-[#155797] text-[#155797] rounded-4xl px-2 py-0.5 mr-2 scale-75 md:scale-100">
                                    <?php echo htmlentities((string) $v['file_type']); ?>
                                </span>
                                <h3 class="text-[#666] text-sm md:text-xl md:ml-2.5">
                                    <a href="<?php echo htmlentities((string) $v['file']); ?>" download="<?php echo htmlentities((string) $v['file_name']); ?>" class="line-clamp-1"> <?php echo htmlentities((string) $v['name']); ?></a>
                                </h3>
                            </div>
                            <div class="w-8 h-8 md:w-10 md:h-10 rounded-full bg-[rgba(21,87,151,0.1)] border border-[#e0eaff] flex items-center justify-center scale-75">
                                <a href="<?php echo htmlentities((string) $v['file']); ?>" download="<?php echo htmlentities((string) $v['file_name']); ?>">
                                    <img src="/static/home/<USER>/icons/xiazai1.png" alt="" class="w-3.5 md:w-[1.1rem]" />
                                </a>
                            </div>
                        </li>
                        <?php endforeach; endif; else: echo "" ;endif; ?>
                    </ul>
                </div>
                <?php endforeach; endif; else: echo "" ;endif; ?>
            </div>
            <div class="bg-[#ffffff] relative mt-4 rounded-md border border-[#dae9ff] md:absolute md:right-0 md:top-14 md:w-[16.25rem] group ">
                <a href="/resources/" class="flex justify-center items-center gap-2 text-[#155797] text-sm py-3 md:text-xl md:h-[3.75rem] md:justify-evenly">
                    <span> Learn More </span>
                    <img src="/static/home/<USER>/icons/changjiantou-zuoshang_b.png" alt="more" class="w-3 absolute right-7 transition-all md:w-[1rem] group-hover:right-2 md:relative md:right-0" />
                </a>
            </div>
        </section>

        <section class="bg-[url(/static/home/<USER>/backgrounds/bg_01.jpg)] bg-no-repeat bg-cover bg-center py-8 md:h-[62.5rem] md:bg-[left_top]" data-aos="fade-up">
            <div class="md:flex md:justify-end md:items-center md:w-10/12 md:mx-auto md:h-full">
                <form action=""
                    class="w-11/12 mx-auto md:mx-0 py-6 px-4 flex flex-col gap-3 bg-white rounded-md md:w-[51.25rem]  md:p-14"
                    id="feedbackForm">
                    <h2 class="text-[#111111] text-2xl Roboto_Bold mb-2 md:text-center md:text-5xl md:mb-12">
                        Messages and Feedback
                    </h2>
                    <div class="flex flex-col gap-3 md:grid md:grid-cols-2 md:gap-5">
                        <div class="input-item group">
                            <input type="text" name="name" class="w-full px-4 py-2 border border-[#dae9ff] bg-white focus:border-[#dae9ff] rounded-md text-sm
                            md:h-[3.75rem] md:text-xl md:rounded-lg" required placeholder="Name*"
                                oninvalid="setCustomValidity('This field is required.');"
                                oninput="setCustomValidity('') ">
                        </div>
                        <div class="input-item">
                            <input type="tel" name="phone" class="w-full px-4 py-2 border border-[#dae9ff] bg-white focus:border-[#dae9ff] rounded-md text-sm
                            md:h-[3.75rem] md:text-xl md:rounded-lg" required placeholder="Phone*"
                                oninvalid="setCustomValidity('This field is required.');"
                                oninput="setCustomValidity('') ">
                        </div>
                        <div class="input-item">
                            <input type="Email" name="email" class="w-full px-4 py-2 border border-[#dae9ff] bg-white focus:border-[#dae9ff] rounded-md text-sm
                            md:h-[3.75rem] md:text-xl md:rounded-lg" required placeholder="Email*"
                                oninvalid="setCustomValidity('This field is required.');"
                                oninput="setCustomValidity('') ">
                        </div>
                        <div class="input-item">
                            <input type="text" name="company" class="w-full px-4 py-2 border border-[#dae9ff] bg-white focus:border-[#dae9ff] rounded-md text-sm
                            md:h-[3.75rem] md:text-xl md:rounded-lg" required placeholder="Company*"
                                oninvalid="setCustomValidity('This field is required.');"
                                oninput="setCustomValidity('') ">
                        </div>
                        <div class="input-item md:col-span-2">
                            <textarea name="feedback" class="w-full px-4 py-2 h-[6.25rem] border border-[#dae9ff] bg-white focus:border-[#dae9ff] rounded-md text-sm
                            md:h-[12.5rem] md:text-xl md:rounded-lg resize-none "
                                placeholder="Feedback message..."></textarea>
                        </div>
                    </div>

                    <div class="input-agree mb-4">
                        <p class="text-[#999] text-xs mb-1 md:text-base">
                            By submitting your information, you acknowledge having received, read and understood our
                            Privacy
                            Notice as made available above.
                        </p>
                        <label for="agree">
                            <input type="checkbox" name="agree" id="agree" class="mr-2 w-5 h-5 align-middle">
                            <span class="text-[#111] text-sm md:text-base">I agree</span>
                            </input>
                        </label>
                    </div>
                    <button type="submit" id="submitBtn"
                        class="flex justify-center items-center gap-2 text-white text-sm py-3 bg-[#155290] rounded-md relative md:h-20 cursor-pointer">
                        <span class="text-white text-sm md:text-3xl">Send</span>
                        <img src="/static/home/<USER>/icons/changjiantou-zuoshang.png" alt="more"
                            class="w-3 absolute right-7 md:w-[1.2rem] md:right-10">
                    </button>
                </form>
            </div>
        </section>
    </main>

    <section class="flex items-center justify-center py-6 md:py-10 border-t border-[#e0eaff]">
    <figure>
        <img src="/static/home/<USER>/logo.png" alt="logo" class="w-[9.375rem] md:w-[10rem]" />
        <figcaption class="sr-only">logo</figcaption>
    </figure>
</section>

<footer class="bg-[#155290]">
    <div
        class="flex flex-wrap justify-around max-w-2xs mx-auto text-white pt-8 pb-6 gap-y-3 md:max-w-max md:gap-x-10 md:text-xl">
        <a href="/">
            Open DEL
        </a>
        <a href="/about">
            About
        </a>
        <a href="/contact">
            Contact Us
        </a>
        <a href="/privacy/terms">
            Terms of Service
        </a>
        <a href="/privacy/">
            Privacy Agreement
        </a>
    </div>
    <div class="border-t border-[#2873bf] p-4">
        <p class="text-xs text-center text-white md:text-xl">
            © 2025 HitGen Inc. All Rights Reserved. <a href="https://www.miit.gov.cn/" target="_blank"
                rel="noopener noreferrer">蜀ICP备16024889-1号</a>
        </p>
    </div>

    <!-- 机器人 -->
    <div class="robot fixed right-5 top-1/2 z-50 -translate-y-1/2">
        <div class="robot-img relative w-14 h-14 border border-white rounded-full md:w-20 md:h-20">
            <span
                class="absolute inline-flex h-full w-full animate-ping rounded-full bg-[#105eb3] opacity-75 -z-[1]"></span>
            <a href="" _target="_blank">
                <img src="/static/home/<USER>/robot.png" alt="robot" class="w-full h-full object-cover" />
                <span class="absolute w-5 h-5 bg-[#f08411] text-xs rounded-full text-white flex items-center justify-center -top-2 right-0
                    md:w-6 md:h-6 md:text-base md:right-3">1</span>
            </a>
        </div>
        <div
            class="robot-text absolute bg-white right-[110%] bottom-0 min-w-[13.5rem] rounded-xl rounded-br-none p-4 text-sm md:min-w-[25.9375rem] md:p-7 md:text-xl border-[#dfe7ff] border">
            <span
                class="absolute w-5 h-5 md:w-7 md:h-7 rounded-full bg-[url(/static/home/<USER>/icons/close.png)] bg-no-repeat bg-center bg-cover -left-6 top-0 cursor-pointer md:-left-10"
                id="btn_close"></span>
            <p class="leading-6 md:leading-8">
                welcome back! still wondering if we are a good match?<img src="/static/home/<USER>/icons/515.png" alt=""
                    class="mx-1.5 w-[1.3rem] inline-block"> How can we help you today?
            </p>
        </div>
    </div>

    <!-- 置顶 -->
    <div class="fixed cursor-pointer right-5 bottom-5 w-10 h-10 bg-[#f08411] md:w-14 md:h-14 rounded-full justify-center items-center z-50 hidden"
        id="popup">
        <img src="/static/home/<USER>/icons/xiazai.png" alt="" class="w-3 md:w-[1.3rem]" />
    </div>
</footer>

<div class="modal-container fixed top-0 left-0 bottom-0 right-0 bg-[rgba(21,82,144,0.5)] hidden z-50" id="pop_container">
    <section class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white border-t-2 border-[#155290] rounded-xl overflow-hidden min-w-[90%] flex flex-col justify-center items-center py-10
    md:min-w-[43.75rem] md:min-h-[28.125rem]
    ">
        <div class="mb-6">
            <h1 class="text-2xl">
                Already have an account?
            </h1>
        </div>
        <div class="mb-7 flex flex-col gap-y-4 text-center min-w-[16.25rem]">
            <a href="/login" class="bg-[#155290] text-[#fff] text-lg py-3">
                Log In
            </a>
            <a href="/quote" onclick="window.location.href = '/quote?' + localStorage.getItem('quoteParams'); return false;" class="bg-[#e0eaff] text-[#155290] text-lg py-3">
                Not now
            </a>
        </div>
        <div class="text-base text-[#333]">
            Don t have an account? <a href="/login/register" class="underline text-[#f08411] ml-1">Sign Up</a>
        </div>
        <div class="close_btn absolute w-8 h-8 rounded-full top-2 right-3 flex items-center justify-center bg-[#155290] text-white cursor-pointer"  data-close-modal>x</div>
    </section>
</div>

</body>

<script src="/static/home/<USER>/vendors/jquery-1.8.3.min.js"></script>
<script src="/static/home/<USER>/vendors/swiper-bundle.min.js"></script>
<script src="/static/home/<USER>/vendors/aos.js"></script>

<script src="/static/layer/layer.js"></script>
<script src="/static/home/<USER>/encapsulate.js"></script>
<script src="/static/home/<USER>/index.js"></script>

<script src="/static/home/<USER>/TabSwitch.js"></script>
<script src="/static/home/<USER>/ShowMore.js"></script>
<script src="/static/home/<USER>/MultiSelect.js"></script>

<script>
    AOS.init(); //延迟加载动画效果
</script>

<script>
    // 显示弹窗的函数
    function showQuotePopup(params) {
        // 存储参数到本地存储或全局变量
        localStorage.setItem('quoteParams', params);

        $('#pop_container').removeClass('hidden').addClass('block');
        return false; // 阻止默认行为
    }

    // 统一关闭所有弹窗
    $(document).on('click', '[data-close-modal]', function() {
        $(this).closest('.modal-container').removeClass('block').addClass('hidden');
    });
</script>

<script>
    $('#EmptyMessage').on('click', function() {
        $.ajax({
            url: '/clear-message/',
            type: 'GET',
            dataType: 'json',
            success: function(response) {
                console.log(response)
                if (response.code === 1) {
                    // 清空消息内容
                    $('#message_cont ul').empty();
                    // 隐藏查看更多按钮
                    $('#message_more').hide();
                    // 显示无消息提示（移除hidden类）
                    $('#no_message').removeClass('hidden');
                }
            },
            error: function(xhr, status, error) {
                console.error("error:", error);
            }
        });
    });
</script>


<script>
$(document).ready(function() {
    $('#feedbackForm').on('submit', function(e) {
        //阻止表单提交
        e.preventDefault();

        // 检查是否同意条款
        if (!$('#agree').is(':checked')) {
            layer.msg('Please check the "I agree" clause first and then submit', { icon: 2 });
            return false;
        }

        // 禁用提交按钮
        var $submitBtn = $('#submitBtn');
        $submitBtn.prop('disabled', true).addClass('opacity-50 cursor-not-allowed');

        // 发送AJAX请求
        $.ajax({
            url: '/submit-feedback',
            type: 'POST',
            data: $(this).serialize(),
            dataType: 'json',
            success: function(data) {
                if (data.code === 1) {
                    //提交成功
                    layer.msg(data.msg, { icon: 1, time: 2000 }, () => {
                        $('#feedbackForm')[0].reset(); // 重置表单
                    });
                } else {
                    //提交失败
                    layer.msg('Error: ' + data.msg, { icon: 2 });
                }
            },
            error: function(xhr, status, error) {
                layer.msg('An error occurred: ' + error, { icon: 2 });
            },
            complete: function() {
                // 无论成功失败，都重新启用按钮
                $submitBtn.prop('disabled', false).removeClass('opacity-50 cursor-not-allowed');
            }
        });
    });
});
</script>

<script>
    var bannerSwiper = new Swiper(".swiper-banner", {
            autoHeight: true,
            spaceBetween: 20,
            effect: "fade",
            autoplay: {
                delay: 3000,
                disableOnInteraction: false,
            },
            loop: true,

            pagination: {
                el: ".swiper-banner-pagination",
                dynamicBullets: true
            },
            navigation: {
                nextEl: ".swiper-banner-next",
                prevEl: ".swiper-banner-prev",
            },
        });
        let numberPagination = document.querySelector(".number-pagination");
        setupNumberPagination(bannerSwiper, numberPagination);
        bannerSwiper.init();


        var swiperService = new Swiper(".swiper-service", {
            slidesPerView: 1,
            spaceBetween: 20,
            effect: "fade",
            navigation: {
                nextEl: ".swiper-service-next",
                prevEl: ".swiper-service-prev",
            },
            thumbs: { // 缩略图
                swiper: {
                    el: '.swiper-service-thumb',
                    slidesPerView: 5,
                },
                slideThumbActiveClass: 'slide-thumb-active',
            },
            autoHeight: true,
            on: {
                slideChangeTransitionStart: function () {
                    // 获取当前slide中的所有数字元素
                    var activeSlide = this.slides[this.activeIndex];
                    if (activeSlide) {
                        var numberEls = activeSlide.querySelectorAll('.hollow-number');
                        numberEls.forEach(function (el) {
                            // 移除并重新添加动画类
                            el.classList.remove('animate__fadeInRight');
                            void el.offsetWidth; // 触发重绘
                            el.classList.add('animate__fadeInRight');
                        });
                    }
                }
            }
        });
        let servicePagination = document.querySelector(".service-pagination");
        setupNumberPagination(swiperService, servicePagination);
        bannerSwiper.init();



    // 向上无缝滚动
    var upScrollBox = document.querySelector('.myscroll');
    if (upScrollBox) seamlessScroll(upScrollBox, { direction: "up" });

    // 向下无缝滚动（假设有另一个容器类名为myscroll-down）
    var downScrollBox = document.querySelector('.myscroll-down');
    if (downScrollBox) seamlessScroll(downScrollBox, { direction: "down" });
</script>

</html>