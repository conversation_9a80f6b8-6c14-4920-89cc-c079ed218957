<?php
//文件地址
namespace app\home\route;
//使用Route对象
use think\facade\Route;

Route::get('/', 'Index/index');
//首页提交表单
Route::post('submit-feedback', 'Index/submitFeedback');

// 发送验证码
Route::post('send_captcha', 'Captcha/send');
// 验证验证码
Route::post('verify_captcha', 'Captcha/verify');

//注册
Route::rule('login/register', 'Login/register');
//登录
Route::rule('login', 'Login/index');
//退出登录
Route::rule('logout', 'Login/logout');

Route::rule('clear-message', 'User/clearMessage');

//产品详情页
Route::get('product/<url>/detail', 'Product/detail')
    ->pattern([
        'url' => '[-\w\x{4e00}-\x{9fa5}]+', // 允许中文、字母、数字、下划线和连字符
    ]);
//产品+服务页面
Route::get('product/<url>', 'Product/service')
    ->pattern([
        'url' => '[-\w\x{4e00}-\x{9fa5}]+', // 允许中文、字母、数字、下划线和连字符
    ]);
//产品列表页
Route::get('product', 'Product/index');


//服务详情页
Route::get('service/<url>', 'Service/index')
    ->pattern([
        'url' => '[-\w\x{4e00}-\x{9fa5}]+', // 允许中文、字母、数字、下划线和连字符
    ]);

//quote咨询
Route::rule('quote/logged', 'Quote/logged');
Route::rule('quote', 'Quote/index');

Route::get('getServices/:product_id', 'Quote/getServices');

//FAQ搜索页
Route::rule('faq/result', 'Faq/results');
//FAQ详情页
Route::get('faq/<url>', 'Faq/details')
    ->pattern([
        'url' => '[-\w\x{4e00}-\x{9fa5}]+', // 允许中文、字母、数字、下划线和连字符
    ]);

//FAQ总页
Route::get('faq', 'Faq/index');
Route::post('ask-question', 'Faq/askQuestion');

//订单详情页
Route::get('user/order-detail-<id>', 'User/order_detail')
    ->pattern([
        'id' => '[-\w\x{4e00}-\x{9fa5}]+', // 允许中文、字母、数字、下划线和连字符
    ]);

//上传用户资料
Route::post('upload/userfile', 'User/upload_userfile');

Route::get('about', 'About/index');
Route::get('contact', 'Contact/index');

//新闻页面
Route::get('news/year/:year', 'News/index');
Route::get('news/<url>', 'News/details')
    ->pattern([
        'url' => '[-\w\x{4e00}-\x{9fa5}]+', // 允许中文、字母、数字、下划线和连字符
    ]);
Route::get('news', 'News/index');

Route::get('privacy/terms', 'Privacy/terms');
Route::get('privacy', 'Privacy/index');

Route::get('resources', 'Resources/index');



?>
