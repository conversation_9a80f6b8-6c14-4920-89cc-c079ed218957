<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <title>{$getone.seo_title?$getone.seo_title:$getone.name}</title>
    <meta name="keywords" content="{$getone.seo_keywords}" />
    <meta name="description" content="{$getone.seo_description}" />
    <link rel="shortcut icon" href="/favicon.ico" />
    <link href="__CSS__/vendors/aos.css" rel="stylesheet">
    <link href="__CSS__/style.css" rel="stylesheet" />
</head>

<body>
    <section class="bg-[url(__IMG__/backgrounds/m_bj.jpg)] bg-no-repeat pb-10 bg-size-[100%_100%] md:bg-[url(__IMG__/backgrounds/pc_bj.jpg)] md:bg-size-[100%_100%] md:pb-20">

        {include file="public:header"}

        <div class="w-11/12 mx-auto md:w-10/12">
            <div class="mt-5 Navigation mb-7 md:mb-14">
                <ul class="text-[#999] flex items-center text-md md:text-xl">
                    <li>
                        <a href="/" class="text-[#155797]">
                            Home
                        </a>
                    </li>
                    <li>
                        <span class="text-[#155797]">Services</span>
                    </li>
                    <li>
                        {$getone.name}
                    </li>
                </ul>
            </div>
            <div class="flex flex-col gap-5">
                <div class="w-full overflow-x-auto">
                    <div class="flex flex-nowrap min-w-max gap-x-4 pb-2 Roboto_Bold md:text-3xl">
                        {volist name="product" id="vo"}
                        <a href="/product/{$vo.seo_url}" class="px-10 py-3 whitespace-nowrap rounded-lg border transition-colors md:px-15 md:py-6 {if $vo.id===$getone.id}text-[#030000] bg-[#e0eaff] border-[#155290] hover:bg-[#155290] hover:text-white{else /}text-[#999] bg-white border-[#e0eaff] hover:bg-gray-200{/if}">
                            OpenDEL™ Kit
                        </a>
                        {/volist}
                    </div>
                </div>
                <div data-aos="fade-up" class="bg-white border border-[#b5cee6] rounded-xl p-8 flex flex-col gap-y-5 relative
                md:p-16 md:flex-row md:min-h-[45.375rem] md:grid md:grid-cols-2 md:items-center md:justify-between
                ">
                    <div class="text-[#030000] md:felx-1">
                        <h1 class="text-xl Roboto_Bold mb-2 md:text-4xl md:mb-5">{$getone.name}</h1>
                        <p class="text-[#666] text-sm mb-3 md:text-xl md:mb-5">{$getone.title}</p>
                        {$getone.content|raw}
                    </div>
                    <div class="flex-1 md:flex-none md:max-w-[37.5rem]">
                        <img src="{$getone.image_detail}" class="w-full object-cover" alt="">
                    </div>
                    <div class="bg-[#ffffff] relative mt-4 rounded-md border border-[#dae9ff] flex-1 group
                    md:absolute md:right-16 md:w-[23.75rem] md:bottom-16
                    ">
                        <a href="/product/{$getone.seo_url}/detail.html"
                            class="flex justify-center items-center gap-2 text-[#155797] text-sm py-3 md:text-xl md:h-[3.75rem] md:justify-evenly">
                            <span> Learn More </span>
                            <img src="__IMG__/icons/changjiantou-zuoshang_b.png" alt="箭头图标"
                                class="w-3 absolute right-7 transition-all md:w-auto group-hover:right-2 md:relative md:right-0">
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <main class="w-full">
        <div class="w-11/12 mx-auto md:w-10/12 md:pt-20">
            <header
                class="w-11/12 mx-auto pt-8 pb-6 md:w-10/12 md:p-0 md:justify-between md:items-center md:pt-20 md:pb-14 md:hidden">
                <h2 class="Roboto_Bold text-2xl text-[#155797] md:text-5xl">User Service</h2>
            </header>
            <div class="flex flex-col gap-y-5 mb-10
            md:gap-y-12">
                {volist name="service" id="vo"}
                <div data-aos="fade-up" class="bg-[#f8fdff] p-8 border border-[#b5cee6] rounded-xl
                md:py-16 md:px-14 md:rounded-2xl">
                    <h3 class="text-xl Roboto_Bold mb-3
                    md:text-5xl md:mb-11">{$vo.name}</h3>
                    <div class="text-sm  leading-[1.5] mb-1.5
                    md:text-xl md:mb-12">
                        <p>
                            {$vo.description|raw}
                        </p>
                    </div>
                    <div class="bg-[#ffffff] relative mt-4 rounded-md border border-[#dae9ff] flex-1
                    md:flex-none md:float-right md:px-10">
                        <a href="/service/{$vo.seo_url}" class="flex justify-center items-center gap-2 text-[#155797] text-sm py-3
                        md:text-xl md:h-[3.75rem] md:justify-evenly md:gap-x-10">
                            <span> Learn More </span>
                            <img src="__IMG__/icons/changjiantou-zuoshang_b.png" alt="箭头图标"
                                class="w-3 absolute right-7 transition-all md:w-auto group-hover:right-2 md:relative md:right-0">
                        </a>
                    </div>
                </div>
                {/volist}
            </div>
        </div>

        <section data-aos="fade-up" class="bg-[#f6f9ff]
        md:bg-[#e0eaff]">
            <div class="w-11/12 mx-auto
            md:w-10/12">
                <header class=" pt-8 pb-6  md:p-0 md:flex md:justify-between md:items-center md:pt-20 md:pb-14">
                    <h2 class="Roboto_Bold text-2xl text-[#030000]
                    md:text-5xl">Related resources</h2>
                </header>
                <div class="flex flex-col gap-y-3 pb-8
                 md:flex-row md:grid md:grid-cols-3 md:gap-5 md:pb-20">
                    {volist name="resource" id="vo"}
                    <div
                        class="border border-[#dae9ff] bg-white rounded-xl px-5 py-10 flex items-center justify-between gap-x-2">
                        <figure>
                            <div class="w-10 h-10 rounded-full bg-[rgba(21,87,151,0.1)] flex items-center justify-center
                            md:w-14 md:h-14">
                                <img src="__IMG__/icons/diyinwangicon-OL-06.png" alt="" class="w-4
                                md:w-6">
                            </div>
                            <figcaption class="sr-only">Open color page</figcaption>
                        </figure>
                        <p class="text-[#666] text-sm md:text-xl md:ml-2.5">
                            <a href="{$vo.file}" download="{$vo.file_name}" class="line-clamp-1"> Latest Progress of DELLatest Progress of DELL</a>
                        </p>
                        <div class="w-8 h-8 md:w-10 md:h-10 flex items-center justify-center scale-75">
                            <a href="{$vo.file}" download="{$vo.file_name}">
                                <img src="__IMG__/icons/xiazai2.png" alt="" class="w-full md:w-auto">
                            </a>
                        </div>
                    </div>
                    {/volist}
                </div>
            </div>
        </section>
    </main>

    {include file="public:footer"}

    {include file="public:foot"}

</body>
</html>