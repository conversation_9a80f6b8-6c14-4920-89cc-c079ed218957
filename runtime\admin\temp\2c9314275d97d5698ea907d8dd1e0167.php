<?php /*a:1:{s:63:"C:\phpstudy_pro\WWW\opendelclub\app\admin\view\login\index.html";i:1750642893;}*/ ?>
<!DOCTYPE html>
<html>

<head>
	<meta charset="UTF-8">
	<title>后台管理登录</title>
	<link type="text/css" rel="stylesheet" href="/static/admin/css/index.css"/>
	<link type="text/css" rel="stylesheet" href="/static/admin/css/public.css"/>
</head>

<style>
	.captcha-img{
		display: inline-block;
		margin-top: 25px;
		margin-left: 1%;
		vertical-align: top;
	}
	.captcha-img img{
		width: 120px;
		height: 47px;
	}
</style>

<body >
	<div class="login_bg">
		<div class="login_con">
			<form name="form1" method="post" id="login_form" onsubmit="return checksubmit();">
				<h1><img src="/static/admin/images/logo.png" alt="" /></h1>
				<input type="text" value="用户名" onfocus="if(this.value == '用户名') this.value = ''" onblur="if(this.value =='') this.value = '用户名'"  name="username" id="username" class="names" />
				<input type="text" value="密码"  name="password" id="password" class="names" onfocus="if(this.value == '密码'){ this.value = ''; this.type='password';}" onblur="if(this.value ==''){ this.value = '密码'; this.type='text';}"  />
				<input type="text" name="verifycode" id="verifycode" class="yzheng" />
				<input type="hidden" name="verify" value="loginAdmin" />
				<div class="captcha-img"><img class="captcha_img" src="/admin/captcha" width="122" height="45" onClick="this.src='/admin/captcha?tm='+Math.random();" /></div>
				<button type="text" class="login">登 录</button>
			</form>
		</div>
	</div>
</body>

<script src="/static/admin/js/jquery.min.js"></script>
<script src="/static/layer/layer.js"></script>

<script type="text/javascript">
	function checksubmit() {
		var verify = $('#login_form').find('input[name="verify"]').val();

		if ($("#username").val().trim() == "") {
			layer.msg("账号不能为空", { icon: 2 })
			$("#username").focus();
			return false;
		}
		if ($("#password").val().trim() == "") {
			layer.msg("密码不能为空", { icon: 2 })
			$("#password").focus();
			return false;
		}
		if ($("#verifycode").val().trim() == "") {
			layer.msg("验证码不能为空", { icon: 2 })
			$("#verifycode").focus();
			return false;
		}

		$.ajax({
			type: "POST",
			url: "/admin/Login/index",
			data: $('#login_form').serialize(),
			async: false,
			success: function (data) {
				if (data.code == 0) {
					layer.msg(data.msg, { icon: 2 });
					//刷新验证码
					$('#login_form').find(".captcha_img").attr('src', '/admin/captcha?tm=' + Math.random())
				} else {
					layer.msg(data.msg, {
						icon: 1,
						time: 2000
					}, function () {
						window.location.href = data.url;
					});
				}
			},
			error: function () {
				alert("异常！");
				return false;
			}
		});

		return false;
	}
</script>
</html>
