<!DOCTYPE html>
<html>
<head lang="en">
    <meta charset="UTF-8">
    <title></title>

    {include file="common:head"}
</head>

<style>
    .status-1{
        color:limegreen;
    }
    .status-2{
        color:#bf4d4d;
    }
</style>

<body>
    <div class="menus_r">
        <div id="con_two_1" style="display: block;">
            <div class="maps">
                <i></i>
                <p class="current">您当前的位置：<a href="{:url('Public/main')}">首页</a>&nbsp;>&nbsp; <a href="">回复列表</a></p>

                <div class="search-container">
                    <form method="get" action="{:url('reply')}">
                        <input
                            type="text"
                            name="keyword"
                            value="{$params.keyword ?? ''}"
                            class="search-input"
                        >
                        <button type="submit" class="search-button">搜索</button>
                    </form>
                </div>
            </div>

            <div class="form_con">
                <table class="bor_cen">
                    <thead>
                        <tr class="mid_01">
                            <td class="mid_one"></td>
                            <td class="mid_t">内容</td>
                            <td class="mid_one">用户</td>
                            <td class="mid_one">状态</td>
                            <td class="mid_t">不通过原因</td>
                            <td class="mid_one">提交时间</td>
                            <td class="mid_s">操作</td>
                        </tr>
                    </thead>

                    <tbody>
                    {volist name="List" key="k" id="vo"}
                        <tr class="mid_02" data-id="{$vo.id}">
                            <td class="mid_one">{$k}</td>
                            <td class="mid_t">{$vo.content|strip_tags}</td>
                            <td class="mid_one">{$vo.first_name} {$vo.last_name}</td>
                            <td class="mid_one">
                                <span class="status-{$vo.status}">
                                {$vo.status == 0 ? '审核中' : ($vo.status == 1 ? '审核通过' : '审核不通过')}
                                </span>
                            </td>
                            <td class="mid_t">{$vo.fail_reason}</td>
                            <td class="mid_one">{$vo.create_time}</td>
                            <td class="mid_s">
                                <a href="{:url('del_reply', ['id'=>$vo['id']])}" onclick="return confirm('您确定要删除吗?')" class="delete-c">删除</a>
                                {if $vo.status==0}
                                <a href="{:url('examine_reply', ['id'=>$vo['id']])}" class="basic">审核</a>
                                {/if}
                            </td>
                        </tr>
                    {/volist}
                    </tbody>
                </table>

                <div class="interpret">
                    {$List|raw}
                </div>
            </div>
        </div>
    </div>

    {include file="common:foot"}
</body>
</html>