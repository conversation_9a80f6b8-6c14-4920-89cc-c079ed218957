<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0,maximum-scale=1.0,minimum-scale=1.0,user-scalable=no">
    <title>iCommunity-Topic Name - HitGen OpenDEL™</title>
    <link rel="shortcut icon" href="/favicon.ico" />
    <link href="__CSS__/vendors/aos.css" rel="stylesheet">
    <link href="__CSS__/style.css" rel="stylesheet" />
</head>

<body>
    <section
        class="pb-5 md:pb-20 relative bg-[url(__IMG__/bg.jpg)] ">
        <span class="absolute top-0 left-0 w-full h-full bg-[url(__IMG__/tm-left.png)] bg-no-repeat bg-[top_left] z-0 bg-size-[10rem] md:bg-size-[26.9375rem]"></span>
        <span class="absolute top-0 right-0 w-full h-full bg-[url(__IMG__/tm-right.png)] bg-no-repeat bg-[top_right] z-0 bg-size-[10rem] md:bg-size-[25.0625rem]"></span>

        {include file="public:header"}

        <div class="w-11/12 mx-auto md:w-10/12">
            <div class="mt-5 Navigation mb-3.5 md:mb-14">
                <ul class="text-[#999] flex items-center text-md md:text-xl">
                    <li>
                        <a href="/" class="text-[#155797]">
                            Home
                        </a>
                    </li>
                    <li class="max-w-[12.5rem] line-clamp-1 md:max-w-2xs">
                        <a href="/iCommunity/" class="text-[#155797]">
                            iCommunity
                        </a>
                    </li>
                    <li class="max-w-[12.5rem] line-clamp-1 md:max-w-2xs">
                        {$name}
                    </li>
                </ul>
            </div>
            <!-- data-aos="fade-up" -->
            <div class="contact" data-aos="fade-up">
                <h1 class="text-2xl md:text-5xl md:mb-10 mb-5 Roboto_Bold"> {$name}</h1>

                <form action="/iCommunity/topic/{$name}" method="get" class="relative w-full">
                    <div class="browsing-search-box relative md:max-w-[68.75rem]">
                        <input type="text" name="q" value="{$keyword}" placeholder="Search Opendel resources" class="w-full bg-[#f8fdff] h-10 rounded-md border border-[#e0eaff] p-2 pl-4 text-xs pr-10 md:rounded-xl md:w-full md:h-[5rem] md:text-xl md:pr-[5.625rem] md:pl-[2.5rem]">

                        <button type="submit" class="w-10 h-10 bg-[url(__IMG__/icons/sousuo.png)] bg-no-repeat bg-center bg-size-[1rem] rounded-full cursor-pointer absolute right-0 top-1/2 -translate-y-1/2 md:w-[5rem] md:h-[5rem] md:bg-auto">
                            <span class="sr-only">Search</span>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </section>
    <main class="w-full">
        <div class="w-11/12 mx-auto md:w-10/12" data-aos="fade-up">
        <!-- 数据集合 -->
            <div class="flex bg-white py-3.5 flex-wrap gap-3 md:h-[6.25rem] md:items-center md:gap-x-5">
                <div class="browsing-item">
                    <strong>{$all_post|count|number_format}</strong>
                    <span>Posts</span>
                </div>
                <div class="browsing-item">
                    <strong>{$reply_count|number_format}</strong>
                    <span>Replies</span>
                </div>
            </div>
        </div>

        <div class="bg-[#f8fdff] pb-7">
            <div class="w-11/12 mx-auto py-6 mb-5 md:w-10/12 md:py-20  relative z-50" data-aos="fade-up" data-aos-delay="100">

                <div class="iCommunity-content-all bg-white border border-[#c4d7ff] rounded-md">

                    <div data-tab="discussion" class="iCommunity-content-item">
                        <ul class="grid grid-cols-1 mb-2" role="list">
                            {volist name="all_post" id="vo"}
                            <li>
                                <div class="iCommunity-left w-[2.5rem] h-[2.5rem] rounded-md flex-shrink-0 relative md:w-[5rem] md:h-[5rem] border border-[#dae9ff]">
                                    <div class="w-full h-full cursor-pointer">
                                        <img src="{$vo.user.avatar ?? '__IMG__/user-1.jpg'}" alt="" class="w-full h-full rounded-md object-cover">
                                    </div>
                                    <div class="iCommunity-left-info absolute left-0 top-full z-10" style="display: none;">
                                        <div class="bg-[#fafbff] rounded-xl" style="box-shadow: 0 0 1.25rem -0.125rem rgba(84, 111, 138, 0.3);">
                                            <div class="flex items-start gap-x-2 mb-3 md:mb-0 md:gap-x-8 p-4 md:p-10">
                                                <div class="w-[2.5rem] h-[2.5rem] md:w-[8.125rem] md:h-[8.125rem] flex-shrink-0">
                                                    <img src="{$vo.user.avatar ?? '__IMG__/user-1.jpg'}" alt="" class="w-full h-full rounded-md object-cover">
                                                </div>
                                                <!-- 右侧个人信息 -->
                                                <div class="flex flex-col gap-y-2 md:flex-1">
                                                    <!-- 名称、经验、私信 -->
                                                    <div class="flex w-full gap-x-2.5 md:gap-x-8 items-end">
                                                        <div class="name-info">
                                                            <a href="/iCommunity/user/{$vo.user.id}" class="text-base md:text-3xl Roboto_Bold ">{$vo.user.first_name} {$vo.user.last_name}</a>
                                                            <p class="text-xs text-nowrap md:text-xl text-[#999]">{$vo.user.role_name}</p>
                                                        </div>
                                                        <!-- 私信 -->
                                                        {if $vo.user.id != session('userId')}
                                                        <div class="message-btn">
                                                            <a href="/iCommunity/message?user={$vo.user.first_name}_{$vo.user.last_name}" class="text-sm px-2 py-1 md:text-xl bg-[#155797] text-white text-nowrap rounded-md md:px-4 md:py-2 ">
                                                                Private message
                                                            </a>
                                                        </div>
                                                        {/if}
                                                    </div>
                                                    <!-- 发帖数量等 -->
                                                    <div class="mt-3 px-2 md:px-0 md:mt-5">
                                                        <ul class="text-sm justify-between md:text-2xl flex items-center gap-x-5 md:gap-x-10 text-center md:justify-start">
                                                            <li>
                                                                <span>{$vo.user.question_count}</span>
                                                                <p class="text-[#155797]">Questions</p>
                                                            </li>
                                                            <li>
                                                                <span>{$vo.user.posting_count}</span>
                                                                <p class="text-[#155797]">Posts</p>
                                                            </li>
                                                            <li>
                                                                <span>{$vo.user.reply_count}</span>
                                                                <p class="text-[#155797]">Reply</p>
                                                            </li>
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="badge-about">
                                                <!-- 徽章-关于 tab按钮 -->
                                                <div class="flex items-center gap-x-10 border-b border-[#dae9ff] px-10">
                                                    <div class="badge-about-btn active">
                                                        Badge
                                                    </div>
                                                    <div class="badge-about-btn">
                                                        About
                                                    </div>
                                                </div>
                                                <!-- tab详情 -->
                                                <div class="tab-content p-5 md:p-10">
                                                    <div class="tab-content-item flex gap-x-10">
                                                        <!-- 没有徽章的时候显示 -->
                                                        <div class="tab-content-item-no-badge text-sm md:text-2xl" style="display: {$vo.user.role_id == 1 || $vo.user.role_id == 2 || $vo.user.role_id == 3 ? 'block' : 'none'};">
                                                            {$vo.user.first_name} {$vo.user.last_name} did not receive any badges yet.
                                                        </div>
                                                        <!-- 有徽章的时候 -->
                                                        <div class="tab-content-item-badge" style="display: {$vo.user.role_id == 4 || $vo.user.role_id == 5 || $vo.user.role_id == 6 ? 'block' : 'none'};">
                                                            <div class="flex items-center gap-x-2.5 md:gap-x-10 badge-item ">
                                                                <div class=" flex flex-col gap-y-2 items-center">
                                                                    <div class="w-[2.5rem] md:w-[4.6875rem] md:h-[5.0625rem] flex-shrink-0 bg-white">
                                                                        <img src="__IMG__/iCommunity/icon_1.png" alt="" class="w-full h-full object-cover">
                                                                    </div>

                                                                    <div class="text-sm md:text-xl text-[#999]">
                                                                        <p>Junior Badge</p>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="tab-content-item-badge" style="display: {$vo.user.role_id == 5 || $vo.user.role_id == 6 ? 'block' : 'none'};">
                                                            <div class="flex items-center gap-x-2.5 md:gap-x-10 badge-item">
                                                                <div class=" flex flex-col gap-y-2 items-center">
                                                                    <div class="w-[2.5rem] md:w-[4.6875rem] md:h-[5.0625rem] flex-shrink-0 bg-white">
                                                                        <img src="__IMG__/iCommunity/icon_2.png" alt="" class="w-full h-full object-cover">
                                                                    </div>

                                                                    <div class="text-sm md:text-xl text-[#999]">
                                                                        <p>Intermediate Badge</p>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="tab-content-item-badge" style="display: {$vo.user.role_id == 6 ? 'block' : 'none'};">
                                                            <div class="flex items-center gap-x-2.5 md:gap-x-10 badge-item">
                                                                <div class=" flex flex-col gap-y-2 items-center">
                                                                    <div class="w-[2.5rem] md:w-[4.6875rem] md:h-[5.0625rem] flex-shrink-0 bg-white">
                                                                        <img src="__IMG__/iCommunity/icon_3.png" alt="" class="w-full h-full object-cover">
                                                                    </div>

                                                                    <div class="text-sm md:text-xl text-[#999]">
                                                                        <p>Senior Badge</p>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <div class="tab-content-item flex flex-col gap-x-2.5 md:gap-x-10 gap-y-3 md:gap-y-5 text-sm md:text-xl" style="display: none;">
                                                        <div class="about-item flex flex-col md:flex-row md:items-center justify-between gap-y-2 md:gap-y-0 md:gap-x-10">
                                                            <div class="about-item-left flex-1/2">
                                                                Registration Date
                                                            </div>
                                                            <div class="about-item-right Roboto_Bold flex-1/2">
                                                                <p>{:date('j F Y', strtotime($vo.user.create_time))}</p>
                                                            </div>
                                                        </div>
                                                        <div class="about-item flex flex-col md:flex-row md:items-center justify-between gap-y-2 md:gap-y-0 md:gap-x-10">
                                                            <div class="about-item-left flex-1/2">
                                                                Organization/Institution/Corporation
                                                            </div>
                                                            <div class="about-item-right Roboto_Bold flex-1/2">
                                                                <p>{$vo.user.organization}</p>
                                                            </div>
                                                        </div>
                                                        <div class="about-item flex flex-col md:flex-row md:items-center justify-between gap-y-2 md:gap-y-0 md:gap-x-10">
                                                            <div class="about-item-left flex-1/2">
                                                                Title
                                                            </div>
                                                            <div class="about-item-right Roboto_Bold flex-1/2">
                                                                <p>{$vo.user.title}</p>
                                                            </div>
                                                        </div>
                                                        <div class="about-item flex flex-col md:flex-row md:items-center justify-between gap-y-2 md:gap-y-0 md:gap-x-10">
                                                            <div class="about-item-left flex-1/2">
                                                                Location (City, Country, Earth)
                                                            </div>
                                                            <div class="about-item-right Roboto_Bold flex-1/2">
                                                                <p>{$vo.user.country}</p>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="iCommunity-right md:pt-5">
                                    <div class="iCommunity-right-info flex md:flex-row flex-col gap-x-2 mb-2 text-sm md:gap-x-3.5 md:text-xl md:mb-5">
                                        <div class="iCommunity-right-title-name">
                                            <a href="/iCommunity/user/{$vo.user.id}" class="text-[#155797]">{$vo.user.first_name} {$vo.user.last_name}</a>
                                        </div>
                                        <div class="iCommunity-right-title-time text-[#999]">
                                            {$vo.user.role_name} | Published in
                                            <a href="/iCommunity/topic/{$vo.topic}" class="underline text-[#999]">{$vo.topic}</a>
                                        </div>
                                    </div>
                                    <div class="iCommunity-right-content mb-2 md:mb-5">
                                        <div class="iCommunity-right-content-title mb-2 flex items-center gap-x-2">
                                            <a href="/iCommunity/{$vo.item_url}/{$vo.id}" class="text-base Roboto_Bold line-clamp-1 text-[#999] md:text-2xl">
                                                {$vo.title}
                                            </a>
                                            {if $vo.post_type==0}
                                            <span class="inline-block flex-shrink-0">
                                                <img src="__IMG__/icons/w.png" alt="" class="w-4 h-4">
                                            </span>
                                            {/if}
                                        </div>
                                        <div class="iCommunity-right-content-info text-sm line-clamp-3 md:text-xl">
                                           {$vo.content|strip_tags}
                                        </div>
                                    </div>
                                    <div class="iCommunity-right-time text-[#999] text-sm flex items-center justify-between md:justify-start md:gap-x-5 md:text-xl">
                                        <div class="iCommunity-right-time-left">
                                            {:date('F j, Y, g:i A', strtotime($vo.create_time))}
                                        </div>
                                        <div class="iCommunity-right-time-right">
                                            <a href="/iCommunity/{$vo.item_url}/{$vo.id}" class="text-[#999] flex items-center gap-x-1 md:gap-x-2">
                                                <img src="__IMG__/icons/pinglun.png" alt="" class="w-[1rem] md:w-[1.5rem]">
                                                <span>{$vo.reply_count}</span>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </li>
                            {/volist}
                        </ul>
                        <div class="text-sm p-3 md:py-[2.5rem] md:px-[3.5625rem] md:text-xl">
                            <button type="button"
                                class="show-more-activity-btn rounded-md bg-[#155797] text-white py-3.5 w-full cursor-pointer md:h-[5rem]">
                                SHOW MORE ACTIVITY
                            </button>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </main>

    {include file="public:footer"}

    {include file="public:foot"}

    <script>
        $('.iCommunity-left').hover(function(){
            $(this).find('.iCommunity-left-info').show();
        },function(){
            $(this).find('.iCommunity-left-info').hide();
        });

        $('.badge-about').each(function(){
            var $badgeAbout = $(this);
            $badgeAbout.find('.badge-about-btn').each(function(index){
                $(this).click(function(){
                    // 当前tab高亮，兄弟去除高亮
                    $(this).addClass('active').siblings().removeClass('active');
                    // 只切换当前区块下的tab内容
                    $badgeAbout.find('.tab-content-item').eq(index).show().siblings('.tab-content-item').hide();
                });
            });
        });

        //显示更多
        setupShowMoreActivity(
            '.iCommunity-content-item',   // 外层容器
            'ul[role="list"]',            // ul选择器
            '.show-more-activity-btn',                     // 按钮选择器
            4                             // 默认显示4个
        );
    </script>

</body>

</html>