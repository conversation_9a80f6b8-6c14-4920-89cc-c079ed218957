<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <title>Post-details - HitGen OpenDEL™</title>
    <link rel="shortcut icon" href="/favicon.ico" />
    <link href="__CSS__/vendors/aos.css" rel="stylesheet">
    <link href="__CSS__/style.css" rel="stylesheet" />
    <!-- <link href="__CSS__/user-info-card.css" rel="stylesheet" /> -->

    <link rel="stylesheet" href="__STATIC__/dist/ckeditor5/ckeditor5.css" crossorigin>
    <link rel="stylesheet" href="__STATIC__/ckeditor5/ckeditor5.css">

    <!-- 评论系统样式 -->
    <style>
        /* 加载动画 */
        .animate-fade-in {
            animation: fadeIn 0.3s ease-in-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* 按钮加载状态 */
        .btn-loading {
            opacity: 0.7;
            cursor: not-allowed;
        }

        /* 编辑器样式优化 */
        .edui-container:focus {
            outline: 2px solid #155797;
            outline-offset: 2px;
        }

        /* 用户信息卡片动画 */
        .iCommunity-left-info {
            transition: all 0.2s ease-in-out;
        }

        /* 回复区域动画 */
        .comment-reply {
            transition: all 0.3s ease-in-out;
        }

        /* 提示消息样式 */
        .alert-message {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            z-index: 9999;
        }

        /* 响应式优化 */
        @media (max-width: 768px) {
            .lzl_editor_container {
                margin: 0.5rem 0;
            }

            .edui-container {
                min-height: 4rem;
            }
        }

        /* 无评论提示样式 */
        .no-comment-tip {
            color: #999;
            text-align: center;
            padding: 2rem 0;
            font-size: 14px;
            font-style: italic;
        }

        /* 评论计数动画 */
        .comment-count-update {
            animation: pulse 0.3s ease-in-out;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
    </style>
</head>

<body>
    <section class="bg-[url(__IMG__/backgrounds/m_bj.jpg)] bg-no-repeat pb-5 bg-size-[100%] md:bg-[url(__IMG__/backgrounds/pc_bj.jpg)] md:bg-size-[100%] md:pb-8">

        {include file="public:header"}

        <div class="w-11/12 mx-auto md:w-10/12">
            <div class="mt-5 Navigation mb-3.5 md:mb-14">
                <ul class="text-[#999] flex items-center text-md md:text-xl">
                    <li>
                        <a href="/" class="text-[#155797]">
                            Home
                        </a>
                    </li>
                    <li>
                        <a href="/" class="text-[#155797]">
                            iCommunity
                        </a>
                    </li>
                    <li class="max-w-[6.25rem] line-clamp-1 md:max-w-2xs">
                        {$post.title}
                    </li>
                </ul>
            </div>
            <!-- data-aos="fade-up" -->
            <div class="flex flex-col gap-5" data-aos="fade-up">
                <div class="bg-[#f8fdff] border border-[#e0eaff] rounded-xl px-3 py-5 md:p-10">
                    <header class="border-b border-[#e0eaff] pb-3 mb-4 md:pb-8">
                        <h1 class="text-xl text-[#111111] Roboto_Bold mb-2.5 md:mb-5 md:text-3xl">
                            {$post.title}
                        </h1>
                        <time datetime="" class="text-sm md:text-base text-[#999999]">
                            {:date('F j, Y, g:i A', strtotime($vo.create_time))}
                        </time>
                    </header>
                    <div class="flex gap-x-2 md:gap-x-5 relative z-50">
                        <div class="iCommunity-left user-avatar-container main-comment-avatar w-[2.5rem] h-[2.5rem] rounded-md flex-shrink-0 relative md:w-[5rem] md:h-[5rem] border border-[#dae9ff]" data-user-id="1">
                            <div class="w-full h-full cursor-pointer user-avatar-trigger">
                                <img src="{$user.avatar ?? '__IMG__/user-1.jpg'}" alt="" class="w-full h-full rounded-md object-cover">
                            </div>
                            <div class="iCommunity-left-info user-info-card absolute left-0 top-full z-50 min-w-[300px] md:min-w-[400px]" style="display: block;">
                                <div class="bg-[#fafbff] rounded-xl" style="box-shadow: 0 0 1.25rem -0.125rem rgba(84, 111, 138, 0.3);">
                                    <div class="flex items-start gap-x-2 mb-3 md:mb-0 md:gap-x-8 p-4 md:p-10">
                                        <div class="w-[2.5rem] h-[2.5rem] md:w-[6.25rem] md:h-[6.25rem] flex-shrink-0">
                                            <img src="{$user.avatar ?? '__IMG__/user-1.jpg'}" alt="" class="w-full h-full rounded-md object-cover">
                                        </div>
                                        <!-- 右侧个人信息 -->
                                        <div class="flex flex-col gap-y-2 md:flex-1">
                                            <!-- 名称、经验、私信 -->
                                            <div class="flex w-full gap-x-2.5 md:gap-x-8 items-end">
                                                <div class="name-info">
                                                    <a href="/iCommunity/user/{$user.id}" class="text-base md:text-3xl Roboto_Bold ">{$user.first_name} {$user.last_name}</a>
                                                    <p class="text-xs text-nowrap md:text-xl text-[#999]">
                                                        {$user.role_name}
                                                    </p>
                                                </div>
                                                <!-- 私信 -->
                                                <div class="message-btn">
                                                    <a href="/iCommunity/message?user={$user.first_name}_{$user.last_name}" class="text-sm px-2 py-1 md:text-xl bg-[#155797] text-white text-nowrap rounded-md md:px-4 md:py-2 ">
                                                        Private message
                                                    </a>
                                                </div>
                                            </div>
                                            <!-- 发帖数量等 -->
                                            <div class="mt-3 px-2 md:px-0 md:mt-5">
                                                <ul class="text-sm justify-between md:text-2xl flex items-center gap-x-5 md:gap-x-10 text-center md:justify-start">
                                                    <li>
                                                        <span>{$user.question_count}</span>
                                                        <p class="text-[#155797]">Questions</p>
                                                    </li>
                                                    <li>
                                                        <span>{$user.posting_count}</span>
                                                        <p class="text-[#155797]">Posts</p>
                                                    </li>
                                                    <li>
                                                        <span>{$user.reply_count}</span>
                                                        <p class="text-[#155797]">Reply</p>
                                                    </li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="badge-about">
                                        <!-- 徽章-关于 tab按钮 -->
                                        <div class="flex items-center gap-x-10 border-b border-[#dae9ff] px-10">
                                            <div class="badge-about-btn active">
                                                Badge
                                            </div>
                                            <div class="badge-about-btn">
                                                About
                                            </div>
                                        </div>
                                        <!-- tab详情 -->
                                        <div class="tab-content p-5 md:p-10">
                                            <div class="tab-content-item flex gap-x-10">
                                                <!-- 没有徽章的时候显示 -->
                                                <div class="tab-content-item-no-badge text-sm md:text-2xl" style="display: {$user.role_id == 1 || $user.role_id == 2 || $user.role_id == 3 ? 'block' : 'none'};">
                                                    {$user.first_name} {$user.last_name} did not receive any badges yet.
                                                </div>
                                                <!-- 有徽章的时候 -->
                                                <div class="tab-content-item-badge" style="display: {$user.role_id == 4 || $user.role_id == 5 || $user.role_id == 6 ? 'block' : 'none'};">
                                                    <div class="flex items-center gap-x-2.5 md:gap-x-10 badge-item ">
                                                        <div class=" flex flex-col gap-y-2 items-center">
                                                            <div class="w-[2.5rem] md:w-[4.6875rem] md:h-[5.0625rem] flex-shrink-0 bg-white">
                                                                <img src="__IMG__/iCommunity/icon_1.png" alt="" class="w-full h-full object-cover">
                                                            </div>

                                                            <div class="text-sm md:text-xl text-[#999]">
                                                                <p>Junior Badge</p>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="tab-content-item-badge" style="display: {$user.role_id == 5 || $user.role_id == 6 ? 'block' : 'none'};">
                                                    <div class="flex items-center gap-x-2.5 md:gap-x-10 badge-item">
                                                        <div class=" flex flex-col gap-y-2 items-center">
                                                            <div class="w-[2.5rem] md:w-[4.6875rem] md:h-[5.0625rem] flex-shrink-0 bg-white">
                                                                <img src="__IMG__/iCommunity/icon_2.png" alt="" class="w-full h-full object-cover">
                                                            </div>

                                                            <div class="text-sm md:text-xl text-[#999]">
                                                                <p>Intermediate Badge</p>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="tab-content-item-badge" style="display: {$user.role_id == 6 ? 'block' : 'none'};">
                                                    <div class="flex items-center gap-x-2.5 md:gap-x-10 badge-item">
                                                        <div class=" flex flex-col gap-y-2 items-center">
                                                            <div class="w-[2.5rem] md:w-[4.6875rem] md:h-[5.0625rem] flex-shrink-0 bg-white">
                                                                <img src="__IMG__/iCommunity/icon_3.png" alt="" class="w-full h-full object-cover">
                                                            </div>

                                                            <div class="text-sm md:text-xl text-[#999]">
                                                                <p>Senior Badge</p>
                                                            </div>
                                                        </div>
                                                    </div>

                                                </div>
                                            </div>

                                            <div class="tab-content-item flex flex-col gap-x-2.5 md:gap-x-10 gap-y-3 md:gap-y-5 text-sm md:text-xl"
                                                style="display: none;">
                                                <div class="about-item flex flex-col md:flex-row md:items-center justify-between gap-y-2 md:gap-y-0 md:gap-x-10">
                                                    <div class="about-item-left flex-1/2">
                                                        Registration Date
                                                    </div>
                                                    <div class="about-item-right Roboto_Bold flex-1/2">
                                                        <p>{:date('j F Y', strtotime($user.create_time))}</p>
                                                    </div>
                                                </div>
                                                <div class="about-item flex flex-col md:flex-row md:items-center justify-between gap-y-2 md:gap-y-0 md:gap-x-10">
                                                    <div class="about-item-left flex-1/2">
                                                        Organization/Institution/Corporation
                                                    </div>
                                                    <div class="about-item-right Roboto_Bold flex-1/2">
                                                        <p>{$user.organization}</p>
                                                    </div>
                                                </div>
                                                <div class="about-item flex flex-col md:flex-row md:items-center justify-between gap-y-2 md:gap-y-0 md:gap-x-10">
                                                    <div class="about-item-left flex-1/2">
                                                        Title
                                                    </div>
                                                    <div class="about-item-right Roboto_Bold flex-1/2">
                                                        <p>{$user.title}</p>
                                                    </div>
                                                </div>
                                                <div class="about-item flex flex-col md:flex-row md:items-center justify-between gap-y-2 md:gap-y-0 md:gap-x-10">
                                                    <div class="about-item-left flex-1/2">
                                                        Location (City, Country, Earth)
                                                    </div>
                                                    <div class="about-item-right Roboto_Bold flex-1/2">
                                                        <p>{$user.country}</p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                </div>
                            </div>
                        </div>
                        <div class="iCommunity-right flex-1 md:pt-5">
                            <div class="iCommunity-right-info flex md:flex-row flex-col gap-x-2 mb-2 text-sm md:gap-x-3.5 md:text-xl md:mb-5">
                                <div class="iCommunity-right-title-name">
                                    <a href="/iCommunity/user/{$user.id}" class="text-[#155797]">{$user.first_name} {$user.last_name}</a>
                                </div>
                                <div class="iCommunity-right-title-time text-[#999]">
                                    {$user.role_name} | Published in
                                    <a href="/iCommunity/topic/{$post.topic}" class="underline text-[#999]">{$post.topic}</a>
                                </div>
                            </div>
                            <div class="iCommunity-right-content mb-2 md:mb-5">
                                <div class="iCommunity-right-content-info about-container text-sm md:text-xl">
                                    {$post.content|raw}
                                </div>
                            </div>
                            <!-- tag -->
                            <div class="post-tag-list">
                                <ul>
                                    {volist name="tags" id="vo"}
                                    <li>
                                        <a href="/iCommunity/tag">
                                            {$vo.tag_name}
                                        </a>
                                    </li>
                                    {/volist}
                                </ul>
                            </div>
                            <!-- 回复按钮 -->
                            <div class="iCommunity-right-time-right float-right bg-white border border-[#dae9ff] mt-7 rounded-sm scroll-to-reply" >
                                <a href="#reply" class="text-[#155797] flex items-center justify-center  gap-x-1.5 py-1 px-3 md:gap-x-2 text-sm">
                                    <svg t="1751520152503" class="w-[1rem] md:w-6" viewBox="0 0 1024 1024" version="1.1"
                                        xmlns="http://www.w3.org/2000/svg" p-id="4543">
                                        <path
                                            d="M191.825 155.411h639.133c31.777 0 60.641 12.979 81.549 33.885 20.908 20.908 33.886 49.78 33.886 81.547v386.981c0 31.773-12.978 60.642-33.886 81.55s-49.771 33.881-81.549 33.881H617.095c-51.358 65.264-86.005 97.265-115.505 96.842-34.341-0.48-51.522-33.026-64.004-96.842h-245.76c-31.77 0-60.641-12.973-81.547-33.881-20.908-20.908-33.885-49.776-33.885-81.55v-386.98c0-31.767 12.977-60.639 33.885-81.547 20.905-20.907 49.776-33.886 81.546-33.886zM321.3 397.295h4.778c26.955 0 48.999 22.043 48.999 48.999v0.006c0 26.955-22.043 49.005-48.999 49.005H321.3c-26.955 0-48.999-22.05-48.999-49.005v-0.006c0-26.955 22.044-48.999 48.999-48.999z m370.743 0h4.777c26.956 0 48.999 22.043 48.999 48.999v0.006c0 26.955-22.043 49.005-48.999 49.005h-4.777c-26.955 0-48.998-22.05-48.998-49.005v-0.006c0-26.955 22.043-48.999 48.998-48.999z m-188.393 0h4.779c26.953 0 48.997 22.043 48.997 48.999v0.006c0 26.955-22.044 49.005-48.997 49.005h-4.779c-26.953 0-48.999-22.05-48.999-49.005v-0.006c0-26.955 22.046-48.999 48.999-48.999z m327.308-190.478H191.825c-17.576 0-33.59 7.215-45.2 18.827-11.614 11.612-18.827 27.626-18.827 45.2v386.981c0 17.58 7.213 33.589 18.827 45.202 11.61 11.614 27.625 18.825 45.2 18.825H480.773l3.555 21.583c8.232 49.979 13.602 75.405 17.866 75.462 11.309 0.163 36.949-28.559 82.164-87.002l7.764-10.043h238.836c17.583 0 33.592-7.211 45.202-18.825 11.613-11.613 18.828-27.622 18.828-45.202V270.844c0-17.574-7.215-33.588-18.828-45.2-11.61-11.612-27.619-18.827-45.202-18.827z"
                                            fill="#155797" p-id="4544"></path>
                                    </svg>
                                    <span>Reply</span>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-[#f8fdff] border border-[#e0eaff] rounded-xl md:p-10">
                    <header class="border-b border-[#e0eaff] p-5 pb-3 text-sm font-bold md:text-2xl md:p-0 md:pb-8">
                        {$replys|count} replies
                    </header>

                    <!-- 评论列表 -->
                    <div class="p_postlist">
                        <div class="no-comment-tip py-10 text-center">No comments yet.</div>

                        <div class="item-reply-list">
                            <!-- 主评论 -->
                             {volist name="replys" id="vo"}
                             <div class="py-5 px-3 reply-list" data-comment-id="{$vo.id}">
                                <div class="p_postlist_c">
                                    <div class="flex gap-x-2 md:gap-x-5 reply-list-item mb-3">
                                        <div class="iCommunity-left user-avatar-container main-comment-avatar w-[2.5rem] h-[2.5rem] rounded-md flex-shrink-0 relative md:w-[5rem] md:h-[5rem] border border-[#dae9ff]" data-user-id="3">
                                            <div class="w-full h-full cursor-pointer user-avatar-trigger">
                                                <img src="{$vo.user.avatar ?? '__IMG__/user-1.jpg'}" alt="" class="w-full h-full rounded-md object-cover">
                                            </div>
                                            <div class="iCommunity-left-info user-info-card absolute left-0 top-full z-50 min-w-[300px] md:min-w-[400px]" style="display: none;">
                                                <div class="bg-[#fafbff] rounded-xl" style="box-shadow: 0 0 1.25rem -0.125rem rgba(84, 111, 138, 0.3);">
                                                    <div class="flex items-start gap-x-2 mb-3 md:mb-0 md:gap-x-8 p-4 md:p-10">
                                                        <div class="w-[2.5rem] h-[2.5rem] md:w-[8.125rem] md:h-[8.125rem] flex-shrink-0">
                                                            <img src="{$vo.user.avatar ?? '__IMG__/user-1.jpg'}" alt="" class="w-full h-full rounded-md object-cover">
                                                        </div>
                                                        <!-- 右侧个人信息 -->
                                                        <div class="flex flex-col gap-y-2 md:flex-1">
                                                            <!-- 名称、经验、私信 -->
                                                            <div class="flex w-full gap-x-2.5 md:gap-x-8 items-end">
                                                                <div class="name-info">
                                                                    <a href="/iCommunity/user/{$vo.user.id}" class="text-base md:text-3xl Roboto_Bold ">{$vo.user.first_name} {$vo.user.last_name}</a>
                                                                    <p class="text-xs text-nowrap md:text-xl text-[#999]">
                                                                        {$vo.user.role_name}
                                                                    </p>
                                                                </div>
                                                                <!-- 私信 -->
                                                                <div class="message-btn">
                                                                    <a href="/iCommunity/message?user={$vo.user.first_name}_{$vo.user.last_name}" class="text-sm px-2 py-1 md:text-xl bg-[#155797] text-white text-nowrap rounded-md md:px-4 md:py-2 ">
                                                                        Private message
                                                                    </a>
                                                                </div>
                                                            </div>
                                                            <!-- 发帖数量等 -->
                                                            <div class="mt-3 px-2 md:px-0 md:mt-5">
                                                                <ul class="text-sm justify-between md:text-2xl flex items-center gap-x-5 md:gap-x-10 text-center md:justify-start">
                                                                    <li>
                                                                        <span>{$vo.user.question_count}</span>
                                                                        <p class="text-[#155797]">Questions</p>
                                                                    </li>
                                                                    <li>
                                                                        <span>{$vo.user.posting_count}</span>
                                                                        <p class="text-[#155797]">Posts</p>
                                                                    </li>
                                                                    <li>
                                                                        <span>{$vo.user.reply_count}</span>
                                                                        <p class="text-[#155797]">Reply</p>
                                                                    </li>
                                                                </ul>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <div class="badge-about">
                                                        <!-- 徽章-关于 tab按钮 -->
                                                        <div class="flex items-center gap-x-10 border-b border-[#dae9ff] px-10">
                                                            <div class="badge-about-btn active">
                                                                Badge
                                                            </div>
                                                            <div class="badge-about-btn">
                                                                About
                                                            </div>
                                                        </div>
                                                        <!-- tab详情 -->
                                                        <div class="tab-content p-5 md:p-10">
                                                            <div class="tab-content-item flex gap-x-10">
                                                                <!-- 没有徽章的时候显示 -->
                                                                <div class="tab-content-item-no-badge text-sm md:text-2xl" style="display: {$vo.user.role_id == 1 || $vo.user.role_id == 2 || $vo.user.role_id == 3 ? 'block' : 'none'};">
                                                                    {$vo.user.first_name} {$vo.user.last_name} did not receive any badges yet.
                                                                </div>
                                                                <!-- 有徽章的时候 -->
                                                                <div class="tab-content-item-badge" style="display: {$vo.user.role_id == 4 || $vo.user.role_id == 5 || $vo.user.role_id == 6 ? 'block' : 'none'};">
                                                                    <div class="flex items-center gap-x-2.5 md:gap-x-10 badge-item ">
                                                                        <div class=" flex flex-col gap-y-2 items-center">
                                                                            <div class="w-[2.5rem] md:w-[4.6875rem] md:h-[5.0625rem] flex-shrink-0 bg-white">
                                                                                <img src="__IMG__/iCommunity/icon_1.png" alt="" class="w-full h-full object-cover">
                                                                            </div>

                                                                            <div class="text-sm md:text-xl text-[#999]">
                                                                                <p>Junior Badge</p>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                                <div class="tab-content-item-badge" style="display: {$vo.user.role_id == 5 || $vo.user.role_id == 6 ? 'block' : 'none'};">
                                                                    <div class="flex items-center gap-x-2.5 md:gap-x-10 badge-item">
                                                                        <div class=" flex flex-col gap-y-2 items-center">
                                                                            <div class="w-[2.5rem] md:w-[4.6875rem] md:h-[5.0625rem] flex-shrink-0 bg-white">
                                                                                <img src="__IMG__/iCommunity/icon_2.png" alt="" class="w-full h-full object-cover">
                                                                            </div>

                                                                            <div class="text-sm md:text-xl text-[#999]">
                                                                                <p>Intermediate Badge</p>
                                                                            </div>
                                                                        </div>
                                                                    </div>

                                                                </div>
                                                                <div class="tab-content-item-badge" style="display: {$vo.user.role_id == 6 ? 'block' : 'none'};">
                                                                    <div class="flex items-center gap-x-2.5 md:gap-x-10 badge-item">
                                                                        <div class=" flex flex-col gap-y-2 items-center">
                                                                            <div class="w-[2.5rem] md:w-[4.6875rem] md:h-[5.0625rem] flex-shrink-0 bg-white">
                                                                                <img src="__IMG__/iCommunity/icon_3.png" alt="" class="w-full h-full object-cover">
                                                                            </div>

                                                                            <div class="text-sm md:text-xl text-[#999]">
                                                                                <p>Senior Badge</p>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>

                                                            <div class="tab-content-item flex flex-col gap-x-2.5 md:gap-x-10 gap-y-3 md:gap-y-5 text-sm md:text-xl" style="display: none;">
                                                                <div class="about-item flex flex-col md:flex-row md:items-center justify-between gap-y-2 md:gap-y-0 md:gap-x-10">
                                                                    <div class="about-item-left flex-1/2">
                                                                        Registration Date
                                                                    </div>
                                                                    <div class="about-item-right Roboto_Bold flex-1/2">
                                                                        <p>{:date('j F Y', strtotime($vo.user.create_time))}</p>
                                                                    </div>
                                                                </div>
                                                                <div class="about-item flex flex-col md:flex-row md:items-center justify-between gap-y-2 md:gap-y-0 md:gap-x-10">
                                                                    <div class="about-item-left flex-1/2">
                                                                        Organization/Institution/Corporation
                                                                    </div>
                                                                    <div class="about-item-right Roboto_Bold flex-1/2">
                                                                        <p>{$vo.user.organization}</p>
                                                                    </div>
                                                                </div>
                                                                <div class="about-item flex flex-col md:flex-row md:items-center justify-between gap-y-2 md:gap-y-0 md:gap-x-10">
                                                                    <div class="about-item-left flex-1/2">
                                                                        Title
                                                                    </div>
                                                                    <div class="about-item-right Roboto_Bold flex-1/2">
                                                                        <p>{$vo.user.title}</p>
                                                                    </div>
                                                                </div>
                                                                <div class="about-item flex flex-col md:flex-row md:items-center justify-between gap-y-2 md:gap-y-0 md:gap-x-10">
                                                                    <div class="about-item-left flex-1/2">
                                                                        Location (City, Country, Earth)
                                                                    </div>
                                                                    <div class="about-item-right Roboto_Bold flex-1/2">
                                                                        <p>{$vo.user.country}</p>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="iCommunity-right pt-1 flex-1 md:pt-5">
                                            <div class="iCommunity-right-info flex justify-between  flex-col md:flex-row gap-y-1 text-sm md:gap-x-3.5 md:text-xl md:mb-5">
                                                <div class="flex gap-x-1 md:gap-x-3">
                                                    <div class="iCommunity-right-title-name">
                                                        <a href="/iCommunity/user/{$vo.user.id}" class="text-[#155797]">{$vo.user.first_name} {$vo.user.last_name}</a>
                                                    </div>
                                                    <div class="text-[#999]">
                                                        {$vo.user.role_name}
                                                    </div>
                                                </div>
                                                <div class="iCommunity-right-title-time text-[#999]">
                                                    {:date('F j, Y, g:i A', strtotime($vo.create_time))}
                                                </div>
                                            </div>
                                            <div class="iCommunity-right-content mb-2 md:mb-5">
                                                <div class="iCommunity-right-content-info about-container text-sm md:text-xl">
                                                    <p>
                                                        {$vo.content|raw}
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="text-right text-sm">
                                        <!-- 回复按钮 -->
                                        <div class="iCommunity-right-time-right flex justify-end">
                                            <button type="button"
                                                class="text-[#155797] flex items-center justify-center  gap-x-1.5 py-1 px-3 bg-white border border-[#dae9ff] md:gap-x-2 text-sm cursor-pointer pack-reply" style="display: none;">
                                                <svg t="1751520152503" class="w-[1rem] md:w-6" viewBox="0 0 1024 1024"
                                                    version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4543">
                                                    <path
                                                        d="M191.825 155.411h639.133c31.777 0 60.641 12.979 81.549 33.885 20.908 20.908 33.886 49.78 33.886 81.547v386.981c0 31.773-12.978 60.642-33.886 81.55s-49.771 33.881-81.549 33.881H617.095c-51.358 65.264-86.005 97.265-115.505 96.842-34.341-0.48-51.522-33.026-64.004-96.842h-245.76c-31.77 0-60.641-12.973-81.547-33.881-20.908-20.908-33.885-49.776-33.885-81.55v-386.98c0-31.767 12.977-60.639 33.885-81.547 20.905-20.907 49.776-33.886 81.546-33.886zM321.3 397.295h4.778c26.955 0 48.999 22.043 48.999 48.999v0.006c0 26.955-22.043 49.005-48.999 49.005H321.3c-26.955 0-48.999-22.05-48.999-49.005v-0.006c0-26.955 22.044-48.999 48.999-48.999z m370.743 0h4.777c26.956 0 48.999 22.043 48.999 48.999v0.006c0 26.955-22.043 49.005-48.999 49.005h-4.777c-26.955 0-48.998-22.05-48.998-49.005v-0.006c0-26.955 22.043-48.999 48.998-48.999z m-188.393 0h4.779c26.953 0 48.997 22.043 48.997 48.999v0.006c0 26.955-22.044 49.005-48.997 49.005h-4.779c-26.953 0-48.999-22.05-48.999-49.005v-0.006c0-26.955 22.046-48.999 48.999-48.999z m327.308-190.478H191.825c-17.576 0-33.59 7.215-45.2 18.827-11.614 11.612-18.827 27.626-18.827 45.2v386.981c0 17.58 7.213 33.589 18.827 45.202 11.61 11.614 27.625 18.825 45.2 18.825H480.773l3.555 21.583c8.232 49.979 13.602 75.405 17.866 75.462 11.309 0.163 36.949-28.559 82.164-87.002l7.764-10.043h238.836c17.583 0 33.592-7.211 45.202-18.825 11.613-11.613 18.828-27.622 18.828-45.202V270.844c0-17.574-7.215-33.588-18.828-45.2-11.61-11.612-27.619-18.827-45.202-18.827z"
                                                        fill="#155797" p-id="4544"></path>
                                                </svg>
                                                <span>Reply</span>
                                            </button>
                                            <button type="button"
                                                class="text-[#155797] flex items-center justify-center  gap-x-1.5 py-1 px-3 bg-white border border-[#dae9ff] md:gap-x-2 text-sm cursor-pointer lzl_link_unfold">
                                                <svg t="1751520152503" class="w-[1rem] md:w-6" viewBox="0 0 1024 1024"
                                                    version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4543">
                                                    <path
                                                        d="M191.825 155.411h639.133c31.777 0 60.641 12.979 81.549 33.885 20.908 20.908 33.886 49.78 33.886 81.547v386.981c0 31.773-12.978 60.642-33.886 81.55s-49.771 33.881-81.549 33.881H617.095c-51.358 65.264-86.005 97.265-115.505 96.842-34.341-0.48-51.522-33.026-64.004-96.842h-245.76c-31.77 0-60.641-12.973-81.547-33.881-20.908-20.908-33.885-49.776-33.885-81.55v-386.98c0-31.767 12.977-60.639 33.885-81.547 20.905-20.907 49.776-33.886 81.546-33.886zM321.3 397.295h4.778c26.955 0 48.999 22.043 48.999 48.999v0.006c0 26.955-22.043 49.005-48.999 49.005H321.3c-26.955 0-48.999-22.05-48.999-49.005v-0.006c0-26.955 22.044-48.999 48.999-48.999z m370.743 0h4.777c26.956 0 48.999 22.043 48.999 48.999v0.006c0 26.955-22.043 49.005-48.999 49.005h-4.777c-26.955 0-48.998-22.05-48.998-49.005v-0.006c0-26.955 22.043-48.999 48.998-48.999z m-188.393 0h4.779c26.953 0 48.997 22.043 48.997 48.999v0.006c0 26.955-22.044 49.005-48.997 49.005h-4.779c-26.953 0-48.999-22.05-48.999-49.005v-0.006c0-26.955 22.046-48.999 48.999-48.999z m327.308-190.478H191.825c-17.576 0-33.59 7.215-45.2 18.827-11.614 11.612-18.827 27.626-18.827 45.2v386.981c0 17.58 7.213 33.589 18.827 45.202 11.61 11.614 27.625 18.825 45.2 18.825H480.773l3.555 21.583c8.232 49.979 13.602 75.405 17.866 75.462 11.309 0.163 36.949-28.559 82.164-87.002l7.764-10.043h238.836c17.583 0 33.592-7.211 45.202-18.825 11.613-11.613 18.828-27.622 18.828-45.202V270.844c0-17.574-7.215-33.588-18.828-45.2-11.61-11.612-27.619-18.827-45.202-18.827z"
                                                        fill="#155797" p-id="4544"></path>
                                                </svg>
                                                <span>Reply</span>
                                            </button>
                                        </div>
                                    </div>

                                    {notempty name="vo.replys"}
                                    <div class="comment-reply md:pl-[6.25rem]">
                                        <div class="bg-white border border-[#dae9ff] p-3 md:p-5">
                                            <!-- 子评论 -->
                                            <div class="l_comment_list mb-3 md:mb-5">
                                                {volist name="vo.replys" id="v"}
                                                <div class="flex gap-x-2 md:gap-x-5 comment-reply-item mb-3" data-comment-id="{$v.id}">
                                                    <div class="iCommunity-left w-[2.5rem] h-[2.5rem] rounded-md flex-shrink-0 relative md:w-[5rem] md:h-[5rem] border border-[#dae9ff] user-avatar-container sub-comment-avatar" data-user-id="user_gba6cph3y">
                                                        <div class="w-full h-full cursor-pointer user-avatar-trigger">
                                                            <img src="{$v.user.avatar ?? '__IMG__/user-1.jpg'}" alt="" class="w-full h-full rounded-md object-cover">
                                                        </div>

                                                        <div class="iCommunity-left-info user-info-card absolute left-0 top-full z-50" style="display: none;">
                                                            <div class="bg-[#fafbff] rounded-xl border border-[#dae9ff]" style="box-shadow: 0 0 1.25rem -0.125rem rgba(84, 111, 138, 0.3);">
                                                                <div class="flex items-start gap-x-3 md:gap-x-4 p-4 md:p-6">
                                                                    <div class="w-[3rem] h-[3rem] md:w-[4rem] md:h-[4rem] flex-shrink-0">
                                                                        <img src="{$v.user.avatar ?? '__IMG__/user-1.jpg'}" alt="" class="w-full h-full rounded-md object-cover">
                                                                    </div>
                                                                    <div class="flex flex-col gap-y-2 flex-1 min-w-0">
                                                                        <div class="flex flex-col gap-y-1 md:flex-row md:items-center md:justify-between md:gap-x-3">
                                                                            <div class="name-info min-w-0">
                                                                                <a href="/iCommunity/user/{$v.user.id}" class="text-base md:text-lg font-bold text-[#155797] block truncate">{$v.user.first_name} {$v.user.last_name}</a>
                                                                                <p class="text-xs md:text-sm text-[#999]">{$v.user.role_name}</p>
                                                                            </div>
                                                                            <div class="message-btn flex-shrink-0">
                                                                                <a href="/iCommunity/message?user={$v.user.first_name}_{$v.user.last_name}" class="text-xs px-2 py-1 md:text-sm md:px-3 md:py-1.5 bg-[#155797] text-white rounded-md hover:bg-[#0d4a7a] transition-colors whitespace-nowrap">
                                                                                    Private message
                                                                                </a>
                                                                            </div>
                                                                        </div>
                                                                        <div class="mt-2 md:mt-3">
                                                                            <ul class="text-sm justify-between md:text-xl flex items-center gap-x-3 md:gap-x-6 text-center md:justify-start">
                                                                                <li><span class="block font-bold">{$v.user.question_count}</span><p class="text-[#155797] text-xs md:text-sm">Questions</p></li>
                                                                                <li><span class="block font-bold">{$v.user.posting_count}</span><p class="text-[#155797] text-xs md:text-sm">Posts</p></li>
                                                                                <li><span class="block font-bold">{$v.user.reply_count}</span><p class="text-[#155797] text-xs md:text-sm">Reply</p></li>
                                                                            </ul>
                                                                        </div>
                                                                    </div>
                                                                </div>

                                                                <div class="badge-about">
                                                                    <div class="flex items-center gap-x-10 border-b border-[#dae9ff] px-10">
                                                                        <div class="badge-about-btn active">Badge</div>
                                                                        <div class="badge-about-btn">About</div>
                                                                    </div>
                                                                    <div class="tab-content p-3 md:p-5">
                                                                        <!-- Badge标签页内容 -->
                                                                        <div class="tab-content-item" style="display: {$v.user.role_id == 1 || $v.user.role_id == 2 || $v.user.role_id == 3 ? 'block' : 'none'};">
                                                                            <div class="tab-content-item-no-badge text-sm md:text-base text-center py-4">
                                                                                <p class="text-gray-500">{$v.user.first_name} {$v.user.last_name} has not yet obtained any badges.</p>
                                                                            </div>
                                                                        </div>
                                                                        <!-- 有徽章的时候 -->
                                                                        <div class="tab-content-item-badge" style="display: {$v.user.role_id == 4 || $v.user.role_id == 5 || $v.user.role_id == 6 ? 'block' : 'none'};">
                                                                            <div class="flex items-center gap-x-2.5 md:gap-x-10 badge-item ">
                                                                                <div class=" flex flex-col gap-y-2 items-center">
                                                                                    <div class="w-[2.5rem] md:w-[4.6875rem] md:h-[5.0625rem] flex-shrink-0 bg-white">
                                                                                        <img src="__IMG__/iCommunity/icon_1.png" alt="" class="w-full h-full object-cover">
                                                                                    </div>

                                                                                    <div class="text-sm md:text-xl text-[#999]">
                                                                                        <p>Junior Badge</p>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                        <div class="tab-content-item-badge" style="display: {$v.user.role_id == 5 || $v.user.role_id == 6 ? 'block' : 'none'};">
                                                                            <div class="flex items-center gap-x-2.5 md:gap-x-10 badge-item">
                                                                                <div class=" flex flex-col gap-y-2 items-center">
                                                                                    <div class="w-[2.5rem] md:w-[4.6875rem] md:h-[5.0625rem] flex-shrink-0 bg-white">
                                                                                        <img src="__IMG__/iCommunity/icon_2.png" alt="" class="w-full h-full object-cover">
                                                                                    </div>

                                                                                    <div class="text-sm md:text-xl text-[#999]">
                                                                                        <p>Intermediate Badge</p>
                                                                                    </div>
                                                                                </div>
                                                                            </div>

                                                                        </div>
                                                                        <div class="tab-content-item-badge" style="display: {$v.user.role_id == 6 ? 'block' : 'none'};">
                                                                            <div class="flex items-center gap-x-2.5 md:gap-x-10 badge-item">
                                                                                <div class=" flex flex-col gap-y-2 items-center">
                                                                                    <div class="w-[2.5rem] md:w-[4.6875rem] md:h-[5.0625rem] flex-shrink-0 bg-white">
                                                                                        <img src="__IMG__/iCommunity/icon_3.png" alt="" class="w-full h-full object-cover">
                                                                                    </div>

                                                                                    <div class="text-sm md:text-xl text-[#999]">
                                                                                        <p>Senior Badge</p>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                        <!-- About标签页内容 -->
                                                                        <div class="tab-content-item" style="display: none;">
                                                                            <div class="flex flex-col gap-y-2">
                                                                                <div class="about-item flex flex-col md:flex-row md:items-center justify-between gap-y-1 md:gap-y-0 md:gap-x-4 py-2 border-b border-gray-100 last:border-b-0">
                                                                                    <div class="about-item-left text-sm md:text-base text-gray-600">Registration Date</div>
                                                                                    <div class="about-item-right text-sm md:text-base font-medium text-gray-800">{:date('j F Y', strtotime($v.user.create_time))}</div>
                                                                                </div>

                                                                                <div class="about-item flex flex-col md:flex-row md:items-center justify-between gap-y-1 md:gap-y-0 md:gap-x-4 py-2 border-b border-gray-100 last:border-b-0">
                                                                                    <div class="about-item-left text-sm md:text-base text-gray-600">Location</div>
                                                                                    <div class="about-item-right text-sm md:text-base font-medium text-gray-800">{$v.user.country}</div>
                                                                                </div>

                                                                                <div class="about-item flex flex-col md:flex-row md:items-center justify-between gap-y-1 md:gap-y-0 md:gap-x-4 py-2 border-b border-gray-100 last:border-b-0">
                                                                                    <div class="about-item-left text-sm md:text-base text-gray-600">Organization</div>
                                                                                    <div class="about-item-right text-sm md:text-base font-medium text-gray-800">{$v.user.organization}</div>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <div class="iCommunity-right pt-1 flex-1 md:pt-5">
                                                        <div class="iCommunity-right-info flex gap-y-1 text-sm md:text-xl md:mb-5">
                                                            <div class="flex gap-x-1 md:gap-x-3">
                                                                <div class="iCommunity-right-title-name">
                                                                    <a href="/iCommunity/user/{$v.user.id}" class="text-[#155797]">{$v.user.first_name} {$v.user.last_name}</a>
                                                                </div>
                                                                <div class="text-[#999]">{$v.user.role_name}</div>
                                                            </div>
                                                        </div>

                                                        <div class="iCommunity-right-content mb-2 md:mb-5">
                                                            <div class="iCommunity-right-content-info about-container text-sm md:text-xl">
                                                                <p>{$v.content|raw}</p>
                                                            </div>
                                                        </div>

                                                        <div class="flex gap-x-2 justify-end text-sm md:text-base">
                                                            <div class="iCommunity-right-title-time text-[#999]">{:date('F j, Y, g:i A', strtotime($v.create_time))}</div>
                                                            <button type="button" class="cursor-pointer text-[#155797] item-building">Reply</button>
                                                        </div>
                                                    </div>
                                                </div>
                                                {/volist}
                                            </div>

                                            <div class="flex flex-col reply-comment">
                                                <div class="flex justify-end md:text-base">
                                                    <button type="button" class="text-[#155797] flex items-center justify-center gap-x-1.5 py-1 px-3 bg-white border border-[#dae9ff] md:gap-x-2 text-sm cursor-pointer item-reply">
                                                        <svg t="1751520152503" class="w-[1rem] md:w-6" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4543">
                                                            <path d="M191.825 155.411h639.133c31.777 0 60.641 12.979 81.549 33.885 20.908 20.908 33.886 49.78 33.886 81.547v386.981c0 31.773-12.978 60.642-33.886 81.55s-49.771 33.881-81.549 33.881H617.095c-51.358 65.264-86.005 97.265-115.505 96.842-34.341-0.48-51.522-33.026-64.004-96.842h-245.76c-31.77 0-60.641-12.973-81.547-33.881-20.908-20.908-33.885-49.776-33.885-81.55v-386.98c0-31.767 12.977-60.639 33.885-81.547 20.905-20.907 49.776-33.886 81.546-33.886zM321.3 397.295h4.778c26.955 0 48.999 22.043 48.999 48.999v0.006c0 26.955-22.043 49.005-48.999 49.005H321.3c-26.955 0-48.999-22.05-48.999-49.005v-0.006c0-26.955 22.044-48.999 48.999-48.999z m370.743 0h4.777c26.956 0 48.999 22.043 48.999 48.999v0.006c0 26.955-22.043 49.005-48.999 49.005h-4.777c-26.955 0-48.998-22.05-48.998-49.005v-0.006c0-26.955 22.043-48.999 48.998-48.999z m-188.393 0h4.779c26.953 0 48.997 22.043 48.997 48.999v0.006c0 26.955-22.044 49.005-48.997 49.005h-4.779c-26.953 0-48.999-22.05-48.999-49.005v-0.006c0-26.955 22.046-48.999 48.999-48.999z m327.308-190.478H191.825c-17.576 0-33.59 7.215-45.2 18.827-11.614 11.612-18.827 27.626-18.827 45.2v386.981c0 17.58 7.213 33.589 18.827 45.202 11.61 11.614 27.625 18.825 45.2 18.825H480.773l3.555 21.583c8.232 49.979 13.602 75.405 17.866 75.462 11.309 0.163 36.949-28.559 82.164-87.002l7.764-10.043h238.836c17.583 0 33.592-7.211 45.202-18.825 11.613-11.613 18.828-27.622 18.828-45.202V270.844c0-17.574-7.215-33.588-18.828-45.2-11.61-11.612-27.619-18.827-45.202-18.827z" fill="#155797" p-id="4544"></path>
                                                        </svg>
                                                        <span>Reply</span>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    {/notempty}

                                </div>
                            </div>
                            {/volist}
                        </div>
                    </div>
                </div>

                <div id="reply" class="bg-[#f8fdff] border border-[#e0eaff] rounded-xl  p-5 md:p-10">
                    <header class="pb-3 text-sm font-bold md:text-2xl md:p-0 md:pb-8">
                        Comment
                    </header>

                    <form action="/iCommunity/reply" id="formId">
                        <div class="Textarea-container">
                            <textarea id="Textarea" name="content" class="tiny-editor w-full h-[5rem] border"></textarea>
                        </div>

                        <input type="hidden" name="mentioned_users" id="mentioned-users" value="">
                        <input type="hidden" name="post_id" value="{$post.id}">
                        <input type="hidden" name="type" value="{$type}">

                        <div class="mt-4 md:flex gap-4">
                            <button type="submit" id="send_btn" class="w-full bg-[#155797] h-[3.125rem] text-white text-sm rounded-sm cursor-pointer md:text-xl md:w-[14.375rem] md:h-[5rem] hover:bg-sky-700 md:rounded-xl" id="send_btn">
                                SEND
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <!-- 追加输入框 -->
    <script type="text/template" id="tpl-lzl-editor">
        <div class="lzl_editor_container" data-off="true" data-type="">
            <div class="edui-container h-[6.25rem] overflow-y-auto border border-[#dae9ff] rounded-sm p-3 bg-white text-sm mb-2 md:mb-5" contenteditable="true"></div>
            <div class="flex justify-end">
                <button type="button" class="bg-[#155797] py-2 px-3.5 text-[#fff] text-sm rounded-sm cursor-pointer md:text-base release-btn">
                    Release
                </button>
            </div>
        </div>
    </script>

    {include file="public:footer"}

    {include file="public:foot"}

    <!-- 评论系统模块 -->
    <script src="__JS__/modules/utils.js"></script>
    <script src="__JS__/modules/template-manager.js"></script>
    <script src="__JS__/modules/comment-system.js"></script>
    <script src="__JS__/modules/event-handler.js"></script>
    <script src="__JS__/post-details.js"></script>
    <script src="__JS__/user-card-verification.js"></script>

    <script src="__STATIC__/dist/ckeditor5/ckeditor5.umd.js" crossorigin></script>
    <script src="__STATIC__/dist/ckeditor5/zh-cn.umd.js" crossorigin></script>
    <script src="__STATIC__/ckeditor5/ckeditor5.js"></script>

    <script>
        var customEditor = new CKEditorManager();
        customEditor.initAll('.tiny-editor');
    </script>

    <script>
        // 获取表单和提交按钮
        const $form = $('#formId');
        const $send_btn = $('#send_btn');
        // 初始绑定提交事件
        if ($form.length) {
            $form.on('submit', handleSubmit);
        }
        // 提交处理函数
        async function handleSubmit(event) {
            event.preventDefault(); // 阻止默认表单提交

            // 确保所有编辑器内容已同步到 textarea
            customEditor.editors.forEach((editor, index) => {
                editor.updateSourceElement();

                // 提取提及的用户
                const mentionedUsers = getMentionedUsers(editor);
                console.log('被提及的用户:', mentionedUsers);

                // 可以将这些用户ID添加到表单数据中
                const userIds = mentionedUsers.map(user => user.userId).join(',');
                document.getElementById('formId').querySelector('[name="mentioned_users"]').value = userIds;
            });

            // 解绑提交事件（避免重复提交）
            $form.off('submit', handleSubmit);
            // 禁用提交按钮（防止重复点击）
            $send_btn.prop('disabled', true);
            try {
                const formData = new FormData($form[0]);
                const response = await fetch($form.attr('action'), {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'Accept': 'application/json',
                    },
                });
                const data = await response.json();
                console.log(data)
                if (data.code === 1) {
                    // 提交成功
                    layer.msg(data.msg, { icon: 1, time: 2000 }, () => {
                        location.reload();
                    });
                } else {
                    // 提交失败
                    layer.msg(data.msg, { icon: 2 });
                }
            } catch (error) {
                console.error('Error:', error);
                layer.msg("提交失败，请重试", { icon: 2 });
            } finally {
                // 无论成功或失败，重新绑定事件并恢复按钮状态
                $send_btn.prop('disabled', false);
                $form.on('submit', handleSubmit);
            }
        }
    </script>

</body>

</html>