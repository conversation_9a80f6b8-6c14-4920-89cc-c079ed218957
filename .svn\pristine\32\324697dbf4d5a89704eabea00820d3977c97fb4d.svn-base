<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
    <title>修改用户</title>

    <link rel="stylesheet" href="https://code.jquery.com/ui/1.13.1/themes/base/jquery-ui.css">
    <link rel="stylesheet" href="__DIST__/bootstrap-5.3.0/css/bootstrap.min.css">

    {include file="common:head"}
</head>

<body>
    <div class="menus_r">
        <div class="maps">
            <i></i>
            <p class="current">您当前的位置：后台管理&nbsp;>&nbsp;修改产品
                <a href="{:url('index')}" class="de_y_r" >返回</a>
            </p>
        </div>

        <div class="clas_box">
            <form action="{:url('edit')}" method="post" enctype="multipart/form-data" id="formId" autocomplete="off">
                <input type="hidden" name="id" value="{$getone.id}" />

                <div class="class_con">
                    <label>产品名称：</label>
                    <input type="text" name="name" value="{$getone.name}" />
                    <span class="must-input">*</span>
                </div>

                <div class="class_con">
                    <label>SEO链接：</label>
                    <input type="text" name="seo_url" value="{$getone.seo_url}" />
                </div>

                <div class="relation-selector">
                    <div class="selector-group class_con">
                        <label>选择服务</label>
                        <select class="relation-select form-control l_xiang" multiple data-target="#selected-service" data-name="services[]">
                            {volist name="service" id="vo"}
                                <option value="{$vo.id}">{$vo.name}</option>
                            {/volist}
                        </select>
                        <button type="button" class="add-btn btn btn-primary mt-2" data-target="#selected-service">添加</button>
                    </div>
                    <div class="selected-list class_con">
                        <ul id="selected-service" class="list-group selected-items"></ul>
                    </div>
                </div>

                <div class="class_con">
                    <label>标题：</label>
                    <input type="text" name="title" value="{$getone.title}" />
                    <span class="must-input">*</span>
                </div>

                <div class="class_con editor-container">
                    <label>产品描述：</label>
                    <textarea name="description" class="tiny-editor" style="height: 300px;">{$getone.description}</textarea>
                </div>

                <div class="class_con editor-container">
                    <label>产品详情：</label>
                    <textarea name="content" class="tiny-editor" style="height: 300px;">{$getone.content}</textarea>
                </div>

                <div class="class_con">
                    <label>图片：</label>
                    <input type="file" name="image" />
                </div>
                <div class="class_con layout_tip_css">
                    （建议图片 宽680px 高680px。格式：png、jpg、jpeg）
                </div>
                {if condition="$getone.image"}
                <div class="class_con">
                    <img src="{$getone.image}">
                </div>
                {/if}

                <div class="class_con">
                    <label>详情图片：</label>
                    <input type="file" name="image_detail" />
                </div>
                <div class="class_con layout_tip_css">
                    （建议图片 宽638px 高453px。格式：png、jpg、jpeg）
                </div>
                {if condition="$getone.image_detail"}
                <div class="class_con">
                    <img src="{$getone.image_detail}">
                </div>
                {/if}

                <div class="class_con">
                    <label>产品价格：</label>
                    <input type="text" name="price" value="{$getone.price}" />
                </div>

                <!-- <div class="relation-selector">
                    <div class="selector-group class_con">
                        <label>选择相关产品</label>
                        <select class="relation-select form-control l_xiang" multiple data-target="#selected-product" data-name="products[]">
                            {volist name="product" id="vo"}
                                <option value="{$vo.id}">{$vo.name}</option>
                            {/volist}
                        </select>
                        <button type="button" class="add-btn btn btn-primary mt-2" data-target="#selected-product">添加</button>
                    </div>
                    <div class="selected-list class_con">
                        <ul id="selected-product" class="list-group selected-items"></ul>
                    </div>
                </div> -->

                <div class="relation-selector">
                    <div class="selector-group class_con">
                        <label>选择资源</label>
                        <select class="relation-select form-control l_xiang" multiple data-target="#selected-resource" data-name="resources[]">
                            {volist name="resource" id="vo"}
                                <option value="{$vo.id}">{$vo.name}</option>
                            {/volist}
                        </select>
                        <button type="button" class="add-btn btn btn-primary mt-2" data-target="#selected-resource">添加</button>
                    </div>
                    <div class="selected-list class_con">
                        <ul id="selected-resource" class="list-group selected-items"></ul>
                    </div>
                </div>

                <div class="class_con">
                    <label>推荐：</label>
                    <input type="checkbox" name="is_recommend" {if condition="$getone.is_recommend eq 1"}checked{/if} />
                </div>

                <div class="de_y">
                    <button class="de_y_l" type="submit" id="submitBtn">确定</button>
                    <a href="{:url('index')}" class="de_y_r" >返回</a>
                </div>
            </form>
        </div>
    </div>

    {include file="common:foot"}

    <script src="https://code.jquery.com/ui/1.13.1/jquery-ui.min.js"></script>
    <script src="__DIST__/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></script>

    <script>
        // 通用的初始化已选项函数
        function initSelectedItems(items, listId, namePrefix) {
            if (!items || !Array.isArray(items)) return;

            const $list = $(`#${listId}`);
            $list.empty(); // 清空现有项

            items.forEach(function(item) {
                const id = item.id || item.service_id || item.related_id; // 兼容不同属性名
                const name = item.name;

                const newItem = $(`
                    <li class="list-group-item" data-id="${id}">
                        <span class="handle" style="cursor: move;">☰</span> ${name}
                        <button type="button" class="btn btn-sm btn-danger float-right remove-item">删除</button>
                        <input type="hidden" name="${namePrefix}[]" value="${id}">
                    </li>
                `);

                $list.append(newItem);
            });
        }

        $(document).ready(function() {
            // 初始化所有可排序列表
            $(".selected-items").sortable({
                handle: ".handle",
                update: function() {
                    // 排序更新时的逻辑（如果需要可以添加）
                }
            });

            // 添加项目处理
            $(".add-btn").click(function() {
                const targetList = $(this).data("target");
                const selectElement = $(this).siblings(".relation-select");
                const inputName = selectElement.data("name");

                selectElement.find("option:selected").each(function() {
                    const id = $(this).val();
                    const name = $(this).text();
                    const itemExists = $(`${targetList} li[data-id="${id}"]`).length > 0;

                    if (!itemExists) {
                        const newItem = $(`
                            <li class="list-group-item" data-id="${id}">
                                <span class="handle" style="cursor: move;">☰</span> ${name}
                                <button type="button" class="btn btn-sm btn-danger float-right remove-item">删除</button>
                                <input type="hidden" name="${inputName}" value="${id}">
                            </li>
                        `);
                        $(targetList).append(newItem);
                    }
                });
            });

            // 删除项目事件委托
            $(document).on("click", ".remove-item", function() {
                $(this).closest("li").remove();
            });

            // 初始化已选项
            initSelectedItems({$productService|raw}, 'selected-service', 'services');
            initSelectedItems({$productResource|raw}, 'selected-resource', 'resources');
        });
    </script>
</body>
</html>