<?php /*a:4:{s:62:"C:\phpstudy_pro\WWW\opendelclub\app\home\view\forum\topic.html";i:1752567493;s:64:"C:\phpstudy_pro\WWW\opendelclub\app\home\view\public\header.html";i:1752567493;s:64:"C:\phpstudy_pro\WWW\opendelclub\app\home\view\public\footer.html";i:1752052799;s:62:"C:\phpstudy_pro\WWW\opendelclub\app\home\view\public\foot.html";i:1752052798;}*/ ?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <title>iCommunity-Topic Name - HitGen OpenDEL™</title>
    <link rel="shortcut icon" href="/favicon.ico" />
    <link href="/static/home/<USER>/vendors/aos.css" rel="stylesheet">
    <link href="/static/home/<USER>/style.css" rel="stylesheet" />
</head>

<body>
    <section class="bg-[url(/static/home/<USER>/backgrounds/m_bj.jpg)] bg-no-repeat pb-5 bg-size-[100%] md:bg-[url(/static/home/<USER>/backgrounds/pc_bj.jpg)] md:bg-size-[100%] md:pb-8">

        <div id="header" class="relative z-[9999]">
    <header class="w-11/12 mx-auto md:w-10/12">
        <nav class="nav-wrapper flex justify-between items-center h-[3.125rem] md:h-24 top-0" role="navigation">
            <div class="flex-1 md:flex md:items-center md:gap-5 md:h-full">
                <a href="/">
                    <img src="/static/home/<USER>/logo.png" alt="HitGen OpenDEL™" class="w-24 md:w-[8.4375rem]" />
                </a>
                <ul class="fixed hidden items-center h-[calc(100%-3.125rem)] bottom-0 overflow-auto left-0 z-[99999] w-full bg-white text-base
                md:flex md:gap-x-[3.75rem] md:relative md:bg-transparent md:overflow-visible md:h-full md:flex-1 md:text-xl animate__animated animate__delay"
                    id="nav_list">
                    <li class="md:h-full flex">
                        <a href="/" class="text-[#000]  flex-1 leading-[3.125rem] px-5 border-b border-[#e0eaff]
                        md:border-0 md:leading-none md:flex md:items-center
                        ">Home</a>
                    </li>
                    <li class="relative cursor-pointer md:h-full flex flex-col">
                        <a href="/news/" class="text-[#000] cursor-pointer flex-1 h-full leading-[3.125rem] px-5 flex justify-between items-center border-b border-[#e0eaff] md:border-0">
                            <span>DELHunter</span>
                        </a>
                    </li>
                    <li class="relative flex justify-between items-center border-b border-[#e0eaff] cursor-pointer navigation flex-wrap group
                        md:border-0 md:h-full md:flex-nowrap">
                        <a href="/product/"
                            class="text-[#000] cursor-pointer leading-[3.125rem] px-5 md:px-0 md:leading-none">
                            Services
                        </a>
                        <i class="w-[1rem] h-[.5625rem] inline-block bg-[url(/static/home/<USER>/icons/dow.png)] bg-no-repeat bg-center ml-2 group-hover:rotate-180 transition-all mr-5
                        md:mr-0
                        "></i>
                        <ul class="
                            secondary static z-50 hidden bg-[#f8fdff]
                            w-full md:absolute md:top-24 md:left-1/2  md:-translate-x-1/2 md:bg-white md:w-[19.5rem] ">
                            <?php if(is_array($menu_product) || $menu_product instanceof \think\Collection || $menu_product instanceof \think\Paginator): $i = 0; $__LIST__ = $menu_product;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?>
                            <li class="<?php echo !empty($vo['service']) ? 'flex items-center justify-between flex-wrap border-b border-t border-[#e0eaff] relative group navigation-item' : 'grid grid-cols-1'; ?> md:pr-[1.25rem] md:px-0">
                                <a href="/product/<?php echo htmlentities((string) $vo['seo_url']); ?>"
                                    class="text-[#000] flex items-center  md:py-5 px-8 h-[3.125rem] md:h-auto">
                                    <?php echo htmlentities((string) $vo['name']); ?>
                                </a>
                                <i class="w-[1rem] h-[.5625rem] inline-block bg-[url(/static/home/<USER>/icons/dow.png)] bg-no-repeat rotate-0 md:-rotate-90 bg-center ml-2 transition-all mr-5
                                md:mr-0 "></i>
                                <ul class="three-menu w-full hidden bg-[#f8fdff] md:absolute md:top-0 Roboto_Regular md:left-full md:w-full md:bg-[#fff] md:shadow-lg ">
                                    <?php if(is_array($vo['service']) || $vo['service'] instanceof \think\Collection || $vo['service'] instanceof \think\Paginator): $i = 0; $__LIST__ = $vo['service'];if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$v): $mod = ($i % 2 );++$i;?>
                                    <li class="border-b border-t border-[#e0eaff]">
                                        <a href="/service/<?php echo htmlentities((string) $v['seo_url']); ?>" class="
                                        flex items-center px-8 h-[3.125rem] md:h-auto
                                        md:px-8 md:py-5 line-clamp-1 ">
                                            <?php echo htmlentities((string) $v['name']); ?>
                                        </a>
                                    </li>
                                    <?php endforeach; endif; else: echo "" ;endif; ?>
                                </ul>
                            </li>
                            <?php endforeach; endif; else: echo "" ;endif; ?>
                        </ul>
                    </li>

                    <li class="relative flex justify-between items-center border-b border-[#e0eaff] cursor-pointer navigation flex-wrap group
                        md:border-0 md:h-full md:flex-nowrap">
                        <a href="" class="text-[#000] cursor-pointer leading-[3.125rem] px-5 md:px-0 md:leading-none">
                            Resources
                        </a>
                        <i class="w-[1rem] h-[.5625rem] inline-block bg-[url(/static/home/<USER>/icons/dow.png)] bg-no-repeat bg-center ml-2 group-hover:rotate-180 transition-all mr-5
                        md:mr-0
                        "></i>
                        <ul class="
                        secondary static bg-[#f8fdff] z-50
                        hidden
                        w-full
                        md:absolute
                        md:top-24
                        md:left-1/2
                        md:-translate-x-1/2
                            md:bg-white
                            md:w-[19.5rem] ">
                            <?php if(is_array($menu_resources_category) || $menu_resources_category instanceof \think\Collection || $menu_resources_category instanceof \think\Paginator): $k = 0; $__LIST__ = $menu_resources_category;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($k % 2 );++$k;?>
                            <li class="grid grid-cols-1">
                                <a href="/resources/?tab=<?php echo htmlentities((string) $k); ?>" class="text-[#000] flex items-center md:py-5 px-8 h-[3.125rem] border-b border-[#e0eaff] md:h-auto">
                                    <?php echo htmlentities((string) $vo['name']); ?>
                                </a>
                            </li>
                            <?php endforeach; endif; else: echo "" ;endif; ?>

                            <li class="grid grid-cols-1">
                                <a href="/faq/" class="text-[#000] flex items-center md:py-5 px-8 h-[3.125rem] border-b border-[#e0eaff] md:h-auto">
                                    FAQ
                                </a>
                            </li>
                        </ul>
                    </li>
                    <li class="md:h-full grid grid-cols-1">
                        <a href="/iCommunity/" class="text-[#000] border-b border-[#e0eaff] leading-[3.125rem] px-5 md:border-0
                        md:leading-none
                        md:flex
                        md:items-center">iCommunity</a>
                    </li>
                </ul>
            </div>

            <div class="flex items-center gap-3.5 md:gap-8">
                <div class="md:flex md:items-center relative">
                    <div class="bg-white rounded-full p-1 h-8 w-8 border border-[#dfe7ff] md:w-12 md:h-12 "
                        id="search_btn">
                        <button type="button"
                            class="search-btn w-full h-full bg-[url(/static/home/<USER>/icons/sousuo.png)] bg-size-[1rem] rounded-full bg-no-repeat bg-center md:bg-auto cursor-pointer"
                            aria-label="搜索"></button>
                    </div>
                    <form action="" method="post"
                        class="hidden absolute w-full left-0 top-full md:relative z-10" id="search_form">
                        <div
                            class="bg-[#f8fdff] h-14 md:bg-white md:rounded-full md:ml-2 md:w-[300px] md:h-12 relative flex items-center px-2.5 md:p-0">
                            <input type="text"
                                class="border flex-1 rounded-md h-10 px-5 border-[#e0eaff] bg-white md:rounded-full md:ml-2 md:w-full md:h-12 md:px-5"
                                placeholder="Enter the keyword" />
                            <button type="submit" tabindex="-1"
                                class="w-8 h-8 md:absolute md:right-[5px] md:top-1/2 md:-translate-y-1/2 md:w-[2.375rem] md:h-[2.375rem] bg-[url(/static/home/<USER>/icons/sousuo.png)] rounded-full bg-no-repeat bg-center bg-size-[1rem] md:bg-auto z-10 cursor-pointer"
                                id="submit_btn"></button>
                        </div>
                    </form>
                </div>

                <?php if(session('userId')): ?>
                <div class="bg-white rounded-full p-1 h-8 w-8 border border-[#dfe7ff] relative cursor-pointer md:w-12 md:h-12 flex justify-center items-center"
                 id="message_btn" onmouseenter="showMessageDrop()" onmouseleave="hideMessageDrop()"
                    onclick="toggleMessageDrop()">
                    <div class="btn-message">
                        <div class="w-full h-full flex items-center justify-center">
                            <img src="/static/home/<USER>/icons/lingdang.png" alt="" class="w-[1rem] md:w-auto">
                        </div>
                        <?php if(count($user_message)>0): ?>
                        <div class="absolute -top-1 -right-[.525rem] text-white text-center" id="message_num">
                            <div class="min-w-[1rem] min-h-[1rem] px-1 text-[.75rem] bg-[#ff0000] rounded-full md:min-w-[1.25rem] md:h-[1.25rem] md:leading-[1.25rem]">
                                <span><?php echo htmlentities((string) count($user_message)); ?></span>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>

                    <!-- Message下拉通知 -->
                    <div class="absolute top-full left-[10%] -translate-x-1/2 z-20 min-w-[15.625rem] cursor-default pt-3
                        md:min-w-[25rem] md:-left-[100%]
                        " id="message_drop" onclick="event.stopPropagation()" style="display: none;">
                        <?php if(count($user_message)>0): ?>
                        <div class="bg-white shadow-2xl rounded-xl">
                            <div class="message-header flex items-center justify-between p-3 border-b border-[#e0eaff] text-base md:text-xl md:py-[1.25rem] md:px-[1.875rem]">
                                <h3 class="font-bold">Tooltip</h3>
                                <button type="button"
                                    class="w-[1rem] h-[1rem] bg-[url(/static/home/<USER>/icons/shanchu.png)] bg-no-repeat bg-size-[.95rem] cursor-pointer md:w-[1.25rem] md:h-[1.375rem] md:bg-auto"
                                    id="EmptyMessage"></button>
                            </div>
                            <div class="message-tips-list" id="message_cont">
                                <ul>
                                    <?php if(is_array($user_message) || $user_message instanceof \think\Collection || $user_message instanceof \think\Paginator): $i = 0; $__LIST__ = $user_message;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?>
                                    <li>
                                        <strong>[System]</strong>
                                        <span><?php echo htmlentities((string) $vo['content']); ?></span>
                                    </li>
                                    <?php endforeach; endif; else: echo "" ;endif; ?>
                                </ul>
                            </div>
                            <div class="flex items-center justify-center py-4 gap-2.5" id="message_more">
                                <a href="/user/message">
                                    <strong class="text-sm md:text-xl"> View All </strong>
                                </a>
                                <img src="/static/home/<USER>/icons/changjiantou-zuoshang2.png" alt="" class="w-[1rem]">
                            </div>
                            <!-- 清空/暂无消息时候显示 -->
                            <div class="text-center py-4 text-[#f08411] no-message hidden" id="no_message">
                                <span class="text-sm md:text-xl">No message</span>
                            </div>
                        </div>
                        <?php endif; if(count($user_icommunity)>0): ?>
                        <div class="bg-white shadow-2xl rounded-xl">
                            <div class="message-header flex items-center justify-between p-3 border-b border-[#e0eaff] text-base md:text-xl md:py-[1.25rem] md:px-[1.875rem]">
                                <h3 class="font-bold">iCommunity</h3>
                                <button type="button"
                                    class="w-[1rem] h-[1rem] bg-[url(/static/home/<USER>/icons/shanchu.png)] bg-no-repeat bg-size-[.95rem] cursor-pointer md:w-[1.25rem] md:h-[1.375rem] md:bg-auto"
                                    id="EmptyMessage"></button>
                            </div>
                            <div class="message-tips-list" id="message_cont">
                                <ul>
                                    <?php if(is_array($user_icommunity) || $user_icommunity instanceof \think\Collection || $user_icommunity instanceof \think\Paginator): $i = 0; $__LIST__ = $user_icommunity;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?>
                                    <li>
                                        <strong>[<?php echo htmlentities((string) $vo['first_name']); ?> <?php echo htmlentities((string) $vo['last_name']); ?>]</strong>
                                        <span><?php echo htmlentities((string) $vo['content']); ?></span>
                                    </li>
                                    <?php endforeach; endif; else: echo "" ;endif; ?>
                                </ul>
                            </div>
                            <div class="flex items-center justify-center py-4 gap-2.5" id="message_more">
                                <a href="/user/private-message">
                                    <strong class="text-sm md:text-xl"> View All </strong>
                                </a>
                                <img src="/static/home/<USER>/icons/changjiantou-zuoshang2.png" alt="" class="w-[1rem]">
                            </div>
                            <!-- 清空/暂无消息时候显示 -->
                            <div class="text-center py-4 text-[#f08411] no-message hidden" id="no_message">
                                <span class="text-sm md:text-xl">No message</span>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="w-8 h-8 rounded-full md:w-[3.125rem] md:h-[3.125rem] relative cursor-pointer btn-name">
                    <div class="user-name">
                        <img src="<?php echo isset($user['avatar']) ? htmlentities((string) $user['avatar']) : '/static/home/<USER>/user-1.jpg'; ?>" class="w-full h-full rounded-full object-cover" alt="<?php echo session('userEmail'); ?>" />
                    </div>
                    <!-- 登录以后的下拉 -->
                    <div class="user-name-drop absolute top-full -left-[50%] -translate-x-1/2 min-w-[12.5rem] pt-3 z-20 md:right-0 md:min-w-[15.625rem] md:-left-full cursor-default" onclick="event.stopPropagation()">
                        <div class="bg-white p-[1.25rem] shadow-2xl rounded-xl  md:p-6 ">
                            <div class="text-center mb-3">
                                <img src="<?php echo isset($user['avatar']) ? htmlentities((string) $user['avatar']) : '/static/home/<USER>/user-1.jpg'; ?>" alt="" class="w-[4rem] h-[4rem] mx-auto rounded-full" />
                                <p class="mt-[.625rem] text-lg">
                                    <strong class="line-clamp-1"><?php echo htmlentities((string) $user['first_name']); ?> <?php echo htmlentities((string) $user['last_name']); ?></strong>
                                </p>
                            </div>
                            <div class="text-sm md:text-xl">
                                <div class="border-b py-2  px-2 border-[#e0eaff]">
                                    <a href="/user/">
                                        Personal Center
                                    </a>
                                </div>
                                <div class="py-2 px-2">
                                    <a href="/logout">
                                        Sign Out
                                    </a>
                                </div>
                            </div>
                        </div>
                <?php else: ?>
                <div class="flex items-center h-8 min-w-20">
                    <button aria-label="登录账户"
                        class="bg-[#f08411] h-full w-full text-white cursor-pointer bg-[url(/static/home/<USER>/icons/yonghu.png)] bg-no-repeat bg-size-[13px] bg-[12px_center] rounded-lg text-sm pl-6 pr-2 md:w-32 md:h-12 md:bg-auto md:text-xl">
                        <a href="/login/">Login</a>
                    </button>
                </div>
                <?php endif; ?>

                <div class="menu min-w-8 h-5 ml-2 md:hidden">
                    <button aria-label="打开菜单"
                        class="h-full w-full bg-[url(/static/home/<USER>/icons/menu.png)] bg-no-repeat bg-center bg-size-[26px] cursor-pointer"
                        id="menu_btn"></button>
                </div>
            </div>
        </nav>
    </header>
</div>

        <div class="w-11/12 mx-auto md:w-10/12">
            <div class="mt-5 Navigation mb-3.5 md:mb-14">
                <ul class="text-[#999] flex items-center text-md md:text-xl">
                    <li>
                        <a href="/" class="text-[#155797]">
                            Home
                        </a>
                    </li>
                    <li class="max-w-[12.5rem] line-clamp-1 md:max-w-2xs">
                        <a href="/iCommunity/" class="text-[#155797]">
                            iCommunity
                        </a>
                    </li>
                    <li class="max-w-[12.5rem] line-clamp-1 md:max-w-2xs">
                        <?php echo htmlentities((string) $name); ?>
                    </li>
                </ul>
            </div>
            <!-- data-aos="fade-up" -->
            <div class="contact" data-aos="fade-up">
                <h1 class="text-2xl md:text-5xl md:mb-10 mb-5 Roboto_Bold"> <?php echo htmlentities((string) $name); ?></h1>

                <form action="/iCommunity/topic/<?php echo htmlentities((string) $name); ?>" method="get" class="relative w-full">
                    <div class="browsing-search-box relative md:max-w-[68.75rem]">
                        <input type="text" name="q" value="<?php echo htmlentities((string) $keyword); ?>" placeholder="Search Opendel resources" class="w-full bg-[#f8fdff] h-10 rounded-md border border-[#e0eaff] p-2 pl-4 text-xs pr-10 md:rounded-xl md:w-full md:h-[5rem] md:text-xl md:pr-[5.625rem] md:pl-[2.5rem]">

                        <button type="submit" class="w-10 h-10 bg-[url(/static/home/<USER>/icons/sousuo.png)] bg-no-repeat bg-center bg-size-[1rem] rounded-full cursor-pointer absolute right-0 top-1/2 -translate-y-1/2 md:w-[5rem] md:h-[5rem] md:bg-auto">
                            <span class="sr-only">Search</span>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </section>
    <main class="w-full">
        <div class="w-11/12 mx-auto md:w-10/12" data-aos="fade-up">
        <!-- 数据集合 -->
            <div class="flex bg-white py-3.5 flex-wrap gap-3 md:h-[6.25rem] md:items-center md:gap-x-5">
                <div class="browsing-item">
                    <strong><?php echo htmlentities((string) number_format(count($all_post))); ?></strong>
                    <span>Posts</span>
                </div>
                <div class="browsing-item">
                    <strong><?php echo htmlentities((string) number_format($reply_count)); ?></strong>
                    <span>Replies</span>
                </div>
            </div>
        </div>

        <div class="bg-[#f8fdff] pb-7">
            <div class="w-11/12 mx-auto py-6 mb-5 md:w-10/12 md:py-20  relative z-50" data-aos="fade-up" data-aos-delay="100">

                <div class="iCommunity-content-all bg-white border border-[#c4d7ff] rounded-md">

                    <div data-tab="discussion" class="iCommunity-content-item">
                        <ul class="grid grid-cols-1 mb-2" role="list">
                            <?php if(is_array($all_post) || $all_post instanceof \think\Collection || $all_post instanceof \think\Paginator): $i = 0; $__LIST__ = $all_post;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?>
                            <li>
                                <div class="iCommunity-left w-[2.5rem] h-[2.5rem] rounded-md flex-shrink-0 relative md:w-[5rem] md:h-[5rem] border border-[#dae9ff]">
                                    <div class="w-full h-full cursor-pointer">
                                        <img src="<?php echo isset($vo['user']['avatar']) ? htmlentities((string) $vo['user']['avatar']) : '/static/home/<USER>/user-1.jpg'; ?>" alt="" class="w-full h-full rounded-md object-cover">
                                    </div>
                                    <div class="iCommunity-left-info absolute left-0 top-full z-10" style="display: none;">
                                        <div class="bg-[#fafbff] rounded-xl" style="box-shadow: 0 0 1.25rem -0.125rem rgba(84, 111, 138, 0.3);">
                                            <div class="flex items-start gap-x-2 mb-3 md:mb-0 md:gap-x-8 p-4 md:p-10">
                                                <div class="w-[2.5rem] h-[2.5rem] md:w-[8.125rem] md:h-[8.125rem] flex-shrink-0">
                                                    <img src="<?php echo isset($vo['user']['avatar']) ? htmlentities((string) $vo['user']['avatar']) : '/static/home/<USER>/user-1.jpg'; ?>" alt="" class="w-full h-full rounded-md object-cover">
                                                </div>
                                                <!-- 右侧个人信息 -->
                                                <div class="flex flex-col gap-y-2 md:flex-1">
                                                    <!-- 名称、经验、私信 -->
                                                    <div class="flex w-full gap-x-2.5 md:gap-x-8 items-end">
                                                        <div class="name-info">
                                                            <a href="/iCommunity/user/<?php echo htmlentities((string) $vo['user']['id']); ?>" class="text-base md:text-3xl Roboto_Bold "><?php echo htmlentities((string) $vo['user']['first_name']); ?> <?php echo htmlentities((string) $vo['user']['last_name']); ?></a>
                                                            <p class="text-xs text-nowrap md:text-xl text-[#999]"><?php echo htmlentities((string) $vo['user']['role_name']); ?></p>
                                                        </div>
                                                        <!-- 私信 -->
                                                        <div class="message-btn">
                                                            <a href="/iCommunity/message?user=<?php echo htmlentities((string) $vo['user']['first_name']); ?>_<?php echo htmlentities((string) $vo['user']['last_name']); ?>" class="text-sm px-2 py-1 md:text-xl bg-[#155797] text-white text-nowrap rounded-md md:px-4 md:py-2 ">
                                                                Private message
                                                            </a>
                                                        </div>
                                                    </div>
                                                    <!-- 发帖数量等 -->
                                                    <div class="mt-3 px-2 md:px-0 md:mt-5">
                                                        <ul class="text-sm justify-between md:text-2xl flex items-center gap-x-5 md:gap-x-10 text-center md:justify-start">
                                                            <li>
                                                                <span><?php echo htmlentities((string) $vo['user']['question_count']); ?></span>
                                                                <p class="text-[#155797]">Questions</p>
                                                            </li>
                                                            <li>
                                                                <span><?php echo htmlentities((string) $vo['user']['posting_count']); ?></span>
                                                                <p class="text-[#155797]">Posts</p>
                                                            </li>
                                                            <li>
                                                                <span><?php echo htmlentities((string) $vo['user']['reply_count']); ?></span>
                                                                <p class="text-[#155797]">Reply</p>
                                                            </li>
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="badge-about">
                                                <!-- 徽章-关于 tab按钮 -->
                                                <div class="flex items-center gap-x-10 border-b border-[#dae9ff] px-10">
                                                    <div class="badge-about-btn active">
                                                        Badge
                                                    </div>
                                                    <div class="badge-about-btn">
                                                        About
                                                    </div>
                                                </div>
                                                <!-- tab详情 -->
                                                <div class="tab-content p-5 md:p-10">
                                                    <div class="tab-content-item flex gap-x-10">
                                                        <!-- 没有徽章的时候显示 -->
                                                        <div class="tab-content-item-no-badge text-sm md:text-2xl" style="display: <?php echo $vo['user']['role_id']==1 || $vo['user']['role_id'] == 2 || $vo['user']['role_id'] == 3 ? 'block'  :  'none'; ?>;">
                                                            <?php echo htmlentities((string) $vo['user']['first_name']); ?> <?php echo htmlentities((string) $vo['user']['last_name']); ?> did not receive any badges yet.
                                                        </div>
                                                        <!-- 有徽章的时候 -->
                                                        <div class="tab-content-item-badge" style="display: <?php echo $vo['user']['role_id']==4 || $vo['user']['role_id'] == 5 || $vo['user']['role_id'] == 6 ? 'block'  :  'none'; ?>;">
                                                            <div class="flex items-center gap-x-2.5 md:gap-x-10 badge-item ">
                                                                <div class=" flex flex-col gap-y-2 items-center">
                                                                    <div class="w-[2.5rem] md:w-[4.6875rem] md:h-[5.0625rem] flex-shrink-0 bg-white">
                                                                        <img src="/static/home/<USER>/iCommunity/icon_1.png" alt="" class="w-full h-full object-cover">
                                                                    </div>

                                                                    <div class="text-sm md:text-xl text-[#999]">
                                                                        <p>Junior Badge</p>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="tab-content-item-badge" style="display: <?php echo $vo['user']['role_id']==5 || $vo['user']['role_id'] == 6 ? 'block'  :  'none'; ?>;">
                                                            <div class="flex items-center gap-x-2.5 md:gap-x-10 badge-item">
                                                                <div class=" flex flex-col gap-y-2 items-center">
                                                                    <div class="w-[2.5rem] md:w-[4.6875rem] md:h-[5.0625rem] flex-shrink-0 bg-white">
                                                                        <img src="/static/home/<USER>/iCommunity/icon_2.png" alt="" class="w-full h-full object-cover">
                                                                    </div>

                                                                    <div class="text-sm md:text-xl text-[#999]">
                                                                        <p>Intermediate Badge</p>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="tab-content-item-badge" style="display: <?php echo $vo['user']['role_id']==6 ? 'block'  :  'none'; ?>;">
                                                            <div class="flex items-center gap-x-2.5 md:gap-x-10 badge-item">
                                                                <div class=" flex flex-col gap-y-2 items-center">
                                                                    <div class="w-[2.5rem] md:w-[4.6875rem] md:h-[5.0625rem] flex-shrink-0 bg-white">
                                                                        <img src="/static/home/<USER>/iCommunity/icon_3.png" alt="" class="w-full h-full object-cover">
                                                                    </div>

                                                                    <div class="text-sm md:text-xl text-[#999]">
                                                                        <p>Senior Badge</p>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <div class="tab-content-item flex flex-col gap-x-2.5 md:gap-x-10 gap-y-3 md:gap-y-5 text-sm md:text-xl" style="display: none;">
                                                        <div class="about-item flex flex-col md:flex-row md:items-center justify-between gap-y-2 md:gap-y-0 md:gap-x-10">
                                                            <div class="about-item-left flex-1/2">
                                                                Registration Date
                                                            </div>
                                                            <div class="about-item-right Roboto_Bold flex-1/2">
                                                                <p><?php echo date('j F Y', strtotime($vo['user']['create_time'])); ?></p>
                                                            </div>
                                                        </div>
                                                        <div class="about-item flex flex-col md:flex-row md:items-center justify-between gap-y-2 md:gap-y-0 md:gap-x-10">
                                                            <div class="about-item-left flex-1/2">
                                                                Organization/Institution/Corporation
                                                            </div>
                                                            <div class="about-item-right Roboto_Bold flex-1/2">
                                                                <p><?php echo htmlentities((string) $vo['user']['organization']); ?></p>
                                                            </div>
                                                        </div>
                                                        <div class="about-item flex flex-col md:flex-row md:items-center justify-between gap-y-2 md:gap-y-0 md:gap-x-10">
                                                            <div class="about-item-left flex-1/2">
                                                                Title
                                                            </div>
                                                            <div class="about-item-right Roboto_Bold flex-1/2">
                                                                <p><?php echo htmlentities((string) $vo['user']['title']); ?></p>
                                                            </div>
                                                        </div>
                                                        <div class="about-item flex flex-col md:flex-row md:items-center justify-between gap-y-2 md:gap-y-0 md:gap-x-10">
                                                            <div class="about-item-left flex-1/2">
                                                                Location (City, Country, Earth)
                                                            </div>
                                                            <div class="about-item-right Roboto_Bold flex-1/2">
                                                                <p><?php echo htmlentities((string) $vo['user']['country']); ?></p>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="iCommunity-right md:pt-5">
                                    <div class="iCommunity-right-info flex md:flex-row flex-col gap-x-2 mb-2 text-sm md:gap-x-3.5 md:text-xl md:mb-5">
                                        <div class="iCommunity-right-title-name">
                                            <a href="/iCommunity/user/<?php echo htmlentities((string) $vo['user']['id']); ?>" class="text-[#155797]"><?php echo htmlentities((string) $vo['user']['first_name']); ?> <?php echo htmlentities((string) $vo['user']['last_name']); ?></a>
                                        </div>
                                        <div class="iCommunity-right-title-time text-[#999]">
                                            <?php echo htmlentities((string) $vo['user']['role_name']); ?> | Published in
                                            <a href="/iCommunity/topic/<?php echo htmlentities((string) $vo['topic']); ?>" class="underline text-[#999]"><?php echo htmlentities((string) $vo['topic']); ?></a>
                                        </div>
                                    </div>
                                    <div class="iCommunity-right-content mb-2 md:mb-5">
                                        <div class="iCommunity-right-content-title mb-2 flex items-center gap-x-2">
                                            <a href="/iCommunity/<?php echo htmlentities((string) $vo['item_url']); ?>/<?php echo htmlentities((string) $vo['id']); ?>" class="text-base Roboto_Bold line-clamp-1 text-[#999] md:text-2xl">
                                                <?php echo htmlentities((string) $vo['title']); ?>
                                            </a>
                                            <?php if($vo['post_type']==0): ?>
                                            <span class="inline-block flex-shrink-0">
                                                <img src="/static/home/<USER>/icons/w.png" alt="" class="w-4 h-4">
                                            </span>
                                            <?php endif; ?>
                                        </div>
                                        <div class="iCommunity-right-content-info text-sm line-clamp-3 md:text-xl">
                                           <?php echo htmlentities((string) strip_tags($vo['content'])); ?>
                                        </div>
                                    </div>
                                    <div class="iCommunity-right-time text-[#999] text-sm flex items-center justify-between md:justify-start md:gap-x-5 md:text-xl">
                                        <div class="iCommunity-right-time-left">
                                            <?php echo date('F j, Y, g:i A', strtotime($vo['create_time'])); ?>
                                        </div>
                                        <div class="iCommunity-right-time-right">
                                            <a href="/iCommunity/<?php echo htmlentities((string) $vo['item_url']); ?>/<?php echo htmlentities((string) $vo['id']); ?>" class="text-[#999] flex items-center gap-x-1 md:gap-x-2">
                                                <img src="/static/home/<USER>/icons/pinglun.png" alt="" class="w-[1rem] md:w-auto">
                                                <span><?php echo htmlentities((string) $vo['reply_count']); ?></span>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </li>
                            <?php endforeach; endif; else: echo "" ;endif; ?>
                        </ul>
                        <div class="text-sm p-3 md:py-[2.5rem] md:px-[3.5625rem] md:text-xl">
                            <button type="button"
                                class="show-more-activity-btn rounded-md bg-[#155797] text-white py-3.5 w-full cursor-pointer md:h-[5rem]">
                                SHOW MORE ACTIVITY
                            </button>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </main>

    <section class="flex items-center justify-center py-6 md:py-10 border-t border-[#e0eaff]">
    <figure>
        <img src="/static/home/<USER>/logo.png" alt="logo" class="w-[9.375rem] md:w-auto" />
        <figcaption class="sr-only">logo</figcaption>
    </figure>
</section>

<footer class="bg-[#155290]">
    <div
        class="flex flex-wrap justify-around max-w-2xs mx-auto text-white pt-8 pb-6 gap-y-3 md:max-w-max md:gap-x-10 md:text-xl">
        <a href="/">
            Open DEL
        </a>
        <a href="/about">
            About
        </a>
        <a href="/contact">
            Contact Us
        </a>
        <a href="/privacy/terms">
            Terms of Service
        </a>
        <a href="/privacy/">
            Privacy Agreement
        </a>
    </div>
    <div class="border-t border-[#2873bf] py-4">
        <p class="text-xs text-center text-white md:text-xl">
            © 2025 HitGen Inc. All Rights Reserved. <a href="https://www.miit.gov.cn/" target="_blank"
                rel="noopener noreferrer">蜀ICP备16024889-1号</a>
        </p>
    </div>

    <!-- 机器人 -->
    <div class="robot fixed right-5 top-1/2 z-50 -translate-y-1/2">
        <div class="robot-img relative w-14 h-14 border border-white rounded-full md:w-28 md:h-28">
            <span
                class="absolute inline-flex h-full w-full animate-ping rounded-full bg-[#105eb3] opacity-75 -z-[1]"></span>
            <a href="" _target="_blank">
                <img src="/static/home/<USER>/robot.png" alt="robot" class="w-full h-full object-cover" />
                <span class="absolute w-5 h-5 bg-[#f08411] text-xs rounded-full text-white flex items-center justify-center -top-2 right-0
                    md:w-6 md:h-6 md:text-base md:right-3">1</span>
            </a>
        </div>
        <div
            class="robot-text absolute bg-white right-[110%] bottom-0 min-w-[13.5rem] rounded-xl rounded-br-none p-4 text-sm md:min-w-[25.9375rem] md:p-7 md:text-xl">
            <span
                class="absolute w-5 h-5 md:w-7 md:h-7 rounded-full bg-[url(/static/home/<USER>/icons/close.png)] bg-no-repeat bg-center bg-cover -left-6 top-0 cursor-pointer md:-left-10"
                id="btn_close"></span>
            <p class="leading-6 md:leading-8">
                welcome back! still wondering if we are a good match?<img src="/static/home/<USER>/icons/515.png" alt=""
                    class="mx-1.5 inline-block"> How can we help you today?
            </p>
        </div>
    </div>

    <!-- 置顶 -->
    <div class="fixed cursor-pointer right-5 bottom-5 w-10 h-10 bg-[#f08411] md:w-20 md:h-20 rounded-full justify-center items-center z-50 hidden"
        id="popup">
        <img src="/static/home/<USER>/icons/xiazai.png" alt="" class="w-3 md:w-auto" />
    </div>
</footer>

<div class="modal-container fixed top-0 left-0 bottom-0 right-0 bg-[rgba(21,82,144,0.5)] hidden z-50" id="pop_container">
    <section class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white border-t-2 border-[#155290] rounded-xl overflow-hidden min-w-[90%] flex flex-col justify-center items-center py-10
    md:min-w-[43.75rem] md:min-h-[28.125rem]
    ">
        <div class="mb-6">
            <h1 class="text-2xl">
                Already have an account?
            </h1>
        </div>
        <div class="mb-7 flex flex-col gap-y-4 text-center min-w-[16.25rem]">
            <a href="/login" class="bg-[#155290] text-[#fff] text-lg py-3">
                Log In
            </a>
            <a href="/quote" class="bg-[#e0eaff] text-[#155290] text-lg py-3">
                Not now
            </a>
        </div>
        <div class="text-base text-[#333]">
            Don t have an account? <a href="/login/register" class="underline text-[#f08411] ml-1">Sign Up</a>
        </div>
        <div class="close_btn absolute w-8 h-8 rounded-full top-2 right-3 flex items-center justify-center bg-[#155290] text-white cursor-pointer"  data-close-modal>x</div>
    </section>
</div>

    <script src="/static/home/<USER>/vendors/jquery-1.8.3.min.js"></script>
<script src="/static/home/<USER>/vendors/swiper-bundle.min.js"></script>
<script src="/static/home/<USER>/vendors/aos.js"></script>

<script src="/static/layer/layer.js"></script>
<script src="/static/home/<USER>/encapsulate.js"></script>
<script src="/static/home/<USER>/index.js"></script>

<script src="/static/home/<USER>/TabSwitch.js"></script>
<script src="/static/home/<USER>/ShowMore.js"></script>
<script src="/static/home/<USER>/MultiSelect.js"></script>

<script>
    AOS.init(); //延迟加载动画效果
</script>

<script>
    // 显示弹窗的函数
    function showQuotePopup() {
        $('#pop_container').removeClass('hidden').addClass('block');
        return false; // 阻止默认行为
    }

    // 统一关闭所有弹窗
    $(document).on('click', '[data-close-modal]', function() {
        $(this).closest('.modal-container').removeClass('block').addClass('hidden');
    });
</script>

<script>
    $('#EmptyMessage').on('click', function() {
        $.ajax({
            url: '/clear-message/',
            type: 'GET',
            dataType: 'json',
            success: function(response) {
                console.log(response)
                if (response.code === 1) {
                    // 清空消息内容
                    $('#message_cont ul').empty();
                    // 隐藏查看更多按钮
                    $('#message_more').hide();
                    // 显示无消息提示（移除hidden类）
                    $('#no_message').removeClass('hidden');
                }
            },
            error: function(xhr, status, error) {
                console.error("error:", error);
            }
        });
    });
</script>


    <script>
        $('.iCommunity-left').hover(function(){
            $(this).find('.iCommunity-left-info').show();
        },function(){
            $(this).find('.iCommunity-left-info').hide();
        });

        $('.badge-about').each(function(){
            var $badgeAbout = $(this);
            $badgeAbout.find('.badge-about-btn').each(function(index){
                $(this).click(function(){
                    // 当前tab高亮，兄弟去除高亮
                    $(this).addClass('active').siblings().removeClass('active');
                    // 只切换当前区块下的tab内容
                    $badgeAbout.find('.tab-content-item').eq(index).show().siblings('.tab-content-item').hide();
                });
            });
        });

        //显示更多
        setupShowMoreActivity(
            '.iCommunity-content-item',   // 外层容器
            'ul[role="list"]',            // ul选择器
            '.show-more-activity-btn',                     // 按钮选择器
            4                             // 默认显示4个
        );
    </script>

</body>

</html>