<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <title>{$tdk.seo_title?$tdk.seo_title:$tdk.name}</title>
    <meta name="keywords" content="{$tdk.seo_keywords}" />
    <meta name="description" content="{$tdk.seo_description}" />

    <link rel="shortcut icon" href="/favicon.ico" />
    <link href="__CSS__/vendors/aos.css" rel="stylesheet">
    <link href="__CSS__/style.css" rel="stylesheet" />
</head>

<body>
    <section class=" md:bg-size-[100%] md:pb-0 bg-[url(__IMG__/backgrounds/m_bj.jpg)] bg-no-repeat pb-10 bg-size-[100%] md:bg-[url(__IMG__/backgrounds/pc_bj.jpg)]">

        {include file="public:header"}

        <div class="w-11/12 mx-auto md:w-10/12">
            <div class="mt-5 Navigation mb-2.5 md:mb-10">
                <ul class="text-[#999] flex items-center text-md md:text-xl">
                    <li>
                        <a href="/" class="text-[#155797]">
                            Home
                        </a>
                    </li>
                    <li class="max-w-[12.5rem] line-clamp-1 md:max-w-2xs">
                        Privacy
                    </li>
                </ul>
            </div>
        </div>
        <main class="w-full">
            <div class="w-11/12 mx-auto py-6
            md:w-10/12 md:pb-20 md:pt-0" data-aos="fade-up">
                <div class="flex flex-col gap-5">
                    <div class="bg-[#f8fdff] border border-[#e0eaff] rounded-xl p-5 md:p-10">
                        <header class="border-b border-[#e0eaff] pb-3 mb-4 md:pb-8">
                            <h1 class="text-xl text-[#155797] mb-2.5 md:mb-5 md:text-3xl">
                                Privacy Agreement
                            </h1>
                        </header>
                        <div class="text-sm text-[#111111] about-container">
                            {$tdk.content|raw}
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </section>

    {include file="public:footer"}

    {include file="public:foot"}

</body>

</html>