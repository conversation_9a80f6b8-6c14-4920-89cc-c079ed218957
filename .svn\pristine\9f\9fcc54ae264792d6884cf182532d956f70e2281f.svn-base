<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
<head>
	<meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
	<title>后台管理</title>

	<script>
        // 从父窗口获取 CSS，并插入到当前子框架
        if (window.parent && window.parent.getParentCSS) {
            document.write(window.parent.getParentCSS());
        }
    </script>
</head>
<body>
	<div class="backstage">
		<div class="webs fl" >网站内容管理系统</div>
        <div class="webs_r fr">
        	<ul class="webs_con">
 				<li class="adminid"></li>
                <li class="adminids">登陆者&nbsp;:&nbsp;{:session('adminUsername')}</li>
                <li><a href="{:url('Admins/myeditpwd')}" target="mainframe"><span>修改密码</span></a></li>
                <li><a href="{:url('Login/logout')}" title="退出"><span>退出</span></a></li>
        	</ul>
        </div>
	</div>
</body>
</html>