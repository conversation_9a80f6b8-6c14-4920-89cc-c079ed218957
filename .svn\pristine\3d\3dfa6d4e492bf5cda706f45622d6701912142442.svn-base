<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
    <title>修改订单</title>

    {include file="common:head"}
</head>

<body>
    <div class="menus_r">
        <div class="maps">
            <i></i>
            <p class="current">您当前的位置：后台管理&nbsp;>&nbsp;修改订单
                <a href="{:url('index')}" class="de_y_r" >返回</a>
            </p>
        </div>

        <div class="clas_box order-info">
            <form action="{:url('edit')}" method="post" enctype="multipart/form-data" id="formId" autocomplete="off">
                <input type="hidden" name="id" value="{$getone.id}" />
                <input type="hidden" name="user_id" value="{$getone.user_id}" />

                <div class="cnt-basic cnt-information">
                    <div class="cnt-title">
	                    订单基本信息
	                </div>
                    <div class="cnt-basic-list">
                        <div class="cnt-basic-item">
                            <div class="cnt-basic-i class_con">
                                <label>Email：</label>
                                <input type="text" name="email" value="{$order_info.email}" readonly class="input-readonly" />
                            </div>

                            <div class="cnt-basic-i class_con">
                                <label>Country/Region：</label>
                                <select name="country" class="l_xiang">
                                    <option value="">
                                        Please select your country/region
                                    </option>
                                    {volist name="country" id="vo"}
                                    <option value="{$vo.en_name}" {if $order_info.country == $vo.en_name}selected{/if}>{$vo.en_name} {$vo.cn_name}</option>
                                    {/volist}
                                </select>
                                <span class="must-input">*</span>
                            </div>

                            <div class="cnt-basic-i class_con">
                                <label>First Name：</label>
                                <input type="text" name="first_name" value="{$order_info.first_name}" />
                                <span class="must-input">*</span>
                            </div>

                            <div class="cnt-basic-i class_con">
                                <label>Last Name：</label>
                                <input type="text" name="last_name" value="{$order_info.last_name}" />
                                <span class="must-input">*</span>
                            </div>

                            <div class="cnt-basic-i class_con">
                                <label>City：</label>
                                <input type="text" name="city" value="{$order_info.city}" />
                                <span class="must-input">*</span>
                            </div>

                            <div class="cnt-basic-i class_con">
                                <label>订单金额：</label>
                                <input type="text" name="money" value="{$getone.money}" />
                                <span class="must-input">*</span>
                            </div>

                            <div class="cnt-basic-i class_con">
                                <label>产品：</label>
                                <select name="product_id" id="product" class="l_xiang">
                                    <option value="">请选择</option>
                                    {volist name="product" id="vo"}
                                        <option value="{$vo.id}" {if $getone.product_id==$vo.id}selected{/if}>{$vo.name}</option>
                                    {/volist}
                                </select>
                                <span class="must-input">*</span>
                            </div>

                            <div class="cnt-basic-i class_con">
                                <label>产品进度：</label>
                                <select name="product_progress_id" id="product_progress_id" class="l_xiang">
                                    <option value="">请选择</option>
                                    {volist name="product_progress" id="vo"}
                                        <option value="{$vo.id}" {if $getone.product_progress_id==$vo.id}selected{/if}>{$vo.name}</option>
                                    {/volist}
                                </select>
                                <span class="must-input">*</span>
                            </div>

                            <div class="cnt-basic-i class_con">
                                <label>物流单号：</label>
                                <input type="text" name="tracking_no" value="{$getone.tracking_no}" />
                            </div>
                        </div>

                        <div class="cnt-basic-item">
                            <div class="cnt-basic-i class_con">
                                <label>Title：</label>
                                <input type="text" name="title" value="{$order_info.title}" />
                            </div>

                            <div class="cnt-basic-i class_con">
                                <label>Phone：</label>
                                <input type="text" name="phone" value="{$order_info.phone}" />
                                <span class="must-input">*</span>
                            </div>

                            <div class="cnt-basic-i class_con">
                                <label>Organization：</label>
                                <input type="text" name="organization" value="{$order_info.organization}" />
                                <span class="must-input">*</span>
                            </div>

                            <div class="cnt-basic-i class_con">
                                <label>Shipping Address：</label>
                                <input type="text" name="shipping_address" value="{$order_info.shipping_address}" />
                                <span class="must-input">*</span>
                            </div>

                            <div class="cnt-basic-i class_con">
                                <label>Postcode：</label>
                                <input type="text" name="postcode" value="{$order_info.postcode}" />
                                <span class="must-input">*</span>
                            </div>

                            <div class="cnt-basic-i class_con">
                                <label>订单状态：</label>
                                <select name="order_status" class="l_xiang">
                                    <option value="0" {if $getone.order_status==0}selected{/if}>进行中</option>
                                    <option value="1" {if $getone.order_status==1}selected{/if}>已完成</option>
                                    <option value="2" {if $getone.order_status==2}selected{/if}>已取消</option>
                                </select>
                            </div>

                            <div class="cnt-basic-i class_con">
                                <label>服务：</label>
                                <select name="service_id" id="service_id" class="l_xiang">
                                    <option value="">请选择</option>
                                    {volist name="service" id="vo"}
                                        <option value="{$vo.id}" {if $getone.service_id==$vo.id}selected{/if}>{$vo.name}</option>
                                    {/volist}
                                </select>
                            </div>

                            <div class="cnt-basic-i class_con">
                                <label>服务进度：</label>
                                <select name="service_progress_id" id="service_progress_id" class="l_xiang">
                                    <option value="">请选择</option>
                                    {volist name="service_progress" id="vo"}
                                        <option value="{$vo.id}" {if $getone.service_progress_id==$vo.id}selected{/if}>{$vo.name}</option>
                                    {/volist}
                                </select>
                                <span class="must-input">*</span>
                            </div>

                            <div class="cnt-basic-i class_con">
                                <label>订单编号：</label>
                                <input type="text" name="order_no" value="{$getone.order_no}" readonly class="input-readonly" />
                            </div>
                        </div>
                    </div>

                    <div class="de_y">
                        <button class="de_y_l" type="submit" id="submitBtn">修改订单</button>
                        <a href="{:url('index')}" class="de_y_r" >返回</a>
                    </div>
                </div>

                <div class="cnt-basic cnt-information">
                    <div class="cnt-basic-l">
                        <div class="cnt-basic-f">
                            <div class="cnt-title">试用券使用记录</div>
                            <button type="button" class="cnt-label add-btn layer-trigger" data-layer="layer_coupon" data-title="使用试用券" data-width="674px" data-height="300px">
                                <span class="add-layer">+</span>使用试用券
                            </button>
                        </div>
                        <div class="cnt-basic-f-table">
                            <div class="class-table-item">
                                <table class="class-table" cellspacing="0">
                                    <thead>
                                        <tr>
                                            <th>订单编号</th>
                                            <th>试用券</th>
                                            <th>使用时间</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {volist name="used_coupon" id="vo"}
                                        <tr data-id="{$vo.id}">
                                            <td>{$vo.order_no}</td>
                                            <td>{$vo.name}% {$vo.description}</td>
                                            <td>{$vo.update_time ?? $vo.create_time}</td>
                                            <td class="mid_s">
                                                <a href="{:url('cancel_coupon', ['id'=>$vo['id']])}" onclick="return confirm('您确定要删除吗?')" class="delete-c">删除</a>
                                            </td>
                                        </tr>
                                        {/volist}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="cnt-basic cnt-information">
                    <div class="cnt-basic-l">
                        <div class="cnt-basic-f">
                            <div class="cnt-title">主订单保密文件</div>
                            <button type="button" class="cnt-label add-btn layer-trigger" data-layer="layer_orderfile" data-title="新增保密文件" data-width="674px" data-height="500px">
                                <span class="add-layer">+</span>新增保密文件
                            </button>
                        </div>
                        <div class="cnt-basic-f-table">
                            <div class="class-table-item">
                                <table class="class-table" cellspacing="0">
                                    <thead>
                                        <tr>
                                            <th>产品/服务</th>
                                            <th>文件/链接</th>
                                            <th>类型</th>
                                            <th>创建时间</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {volist name="order_file" id="vo"}
                                        <tr data-id="{$vo.id}">
                                            <td>{$vo.main_name}</td>
                                            <td>{if $vo.file_name}{$vo.file_name}{else }{$vo.url}{/if}</td>
                                            <td>{$vo.file_type==0?"保密文件":"用户资料"}</td>
                                            <td>{$vo.create_time}</td>
                                            <td class="mid_s">
                                                {if $vo.file_type==0}
                                                <!--保密文件-->
                                                <a href="{:url('del_file', ['id'=>$vo['id']])}" onclick="return confirm('您确定要删除吗?')" class="delete-c">删除</a>
                                                <div class="button edit-files basic">修改</div>
                                                {else }
                                                <!--用户资料-->
                                                <a href="{$vo.file}" download="{$vo.file_name}" class="w-[2.1875rem]">
                                                    <img src="__IMG__/icons/xiazai1.png" alt="" class="w-[0.8rem] md:w-auto">
                                                </a>
                                                {/if}
                                            </td>
                                        </tr>
                                        {/volist}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="cnt-basic cnt-information">
                    <div class="cnt-basic-l">
                        <div class="cnt-basic-f">
                            <div class="cnt-title">后续服务</div>
                            <button type="button" class="cnt-label add-btn layer-trigger" data-layer="layer_order_subs" data-title="新增后续服务" data-width="674px" data-height="300px" style="margin-right: -450px;">
                                <span class="add-layer">+</span>新增后续服务
                            </button>
                            <button type="button" class="cnt-label add-btn layer-trigger" data-layer="layer_orderfile_subs" data-title="新增保密文件" data-width="674px" data-height="500px">
                                <span class="add-layer">+</span>新增保密文件
                            </button>
                        </div>
                        <div class="cnt-basic-f-table">
                            <div class="class-table-item">
                                <table class="class-table" cellspacing="0">
                                    <thead>
                                        <tr>
                                            <th>订单编号</th>
                                            <th>服务</th>
                                            <th>服务进度</th>
                                            <th>金额</th>
                                            <th>创建时间</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {volist name="order_subs" id="vo"}
                                        <tr class="toggle-row" data-target="services-{$vo.id}" data-id="{$vo.id}">
                                            <td>{$vo.order_no}</td>
                                            <td>{$vo.service_name}</td>
                                            <td>{$vo.service_progress}</td>
                                            <td>{$vo.money}</td>
                                            <td>{$vo.create_time}</td>
                                            <td class="mid_s">
                                                <a href="{:url('del_service', ['id'=>$vo['id']])}" onclick="return confirm('您确定要删除吗?')" class="delete-c">删除</a>
                                                <div class="button edit-orderservice basic">修改</div>
                                            </td>
                                        </tr>

                                        {if condition="!empty($vo.order_file)"}
                                        <tr class="services-row">
                                            <td colspan="6">
                                                <table class="class-table services" id="services-{$vo.id}" style="width: 85%;margin: 0 auto;">
                                                    <thead>
                                                        <tr>
                                                            <th>文件/链接</th>
                                                            <th>类型</th>
                                                            <th>创建时间</th>
                                                            <th>操作</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                    {volist name="vo.order_file" id="v"}
                                                        <tr data-id="{$v.id}">
                                                            <td>{if $v.file_name}{$v.file_name}{else }{$v.url}{/if}</td>
                                                            <td>{$v.file_type==0?"保密文件":"用户资料"}</td>
                                                            <td>{$v.create_time}</td>
                                                            <td class="mid_s">
                                                                {if $v.file_type==0}
                                                                <!--保密文件-->
                                                                <a href="{:url('del_file', ['id'=>$v['id']])}" onclick="return confirm('您确定要删除吗?')" class="delete-c">删除</a>
                                                                <div class="button edit-files-sub basic">修改</div>
                                                                {else }
                                                                <!--用户资料-->
                                                                <a href="{$v.file}" download="{$v.file_name}" class="w-[2.1875rem]" title="点击下载">
                                                                    <img src="__STATIC__/home/<USER>/icons/xiazai1.png" alt="" class="w-[0.8rem] md:w-auto">
                                                                </a>
                                                                {/if}
                                                            </td>
                                                        </tr>
                                                    {/volist}
                                                    </tbody>
                                                </table>
                                            </td>
                                        </tr>
                                        {/if}
                                        {/volist}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <div class="cnt-basic-list cnt-basic-layer order-info" id="layer_order_subs">
    	<form action="{:url('add_service')}" method="post" class="layer-form" enctype="multipart/form-data">
	    	<input type="hidden" name="parent_id" value="{$getone.id}" />
            <input type="hidden" name="info_id" value="{$getone.info_id}" />
            <input type="hidden" name="user_id" value="{$getone.user_id}" />
	        <div class="cnt-basic-item">
	        	<div class="cnt-basic-i class_con">
	                <label>选择服务：</label>
                    <select name="service_id" class="l_xiang">
                        <option value="">请选择</option>
                        {volist name="service" id="vo"}
                            <option value="{$vo.id}">{$vo.name}</option>
                        {/volist}
                    </select>
	            </div>

                <div class="cnt-basic-i class_con">
                    <label>订单金额：</label>
                    <input type="text" name="money" />
                    <span class="must-input">*</span>
                </div>

                <div class="de_y">
                    <button type="button" class="layer-close-btn cnt-label">取消</button>
                    <button type="button" class="layer-submit-btn cnt-label">提交</button>
                </div>
	        </div>
        </form>
    </div>

    <div class="cnt-basic-list cnt-basic-layer change-files order-info" id="layer_orderfile">
    	<form action="{:url('add_file')}" method="post" class="layer-form" enctype="multipart/form-data">
	    	<input type="hidden" name="order_id" value="{$getone.id}" />
	        <div class="cnt-basic-item">
                <div class="cnt-basic-i class_con">
                    <label>选择主体：</label>
                    <span class="selecttype type active" data-type="0" data-main-id="{$getone.product_id}">{$getone.product_name}</span>
                    {if condition="$getone.service_name"}
                    <span class="selecttype type" data-type="1" data-main-id="{$getone.service_id}">{$getone.service_name}</span>
                    {/if}
                    <input type="hidden" name="type" value="0" />
                    <input type="hidden" name="main_id" value="{$getone.product_id}" />
                </div>

                <div class="cnt-basic-i class_con">
                    <label>选择类型：</label>
                    <span class="selecttype up_type active" data-value="url">网址</span>
                    <span class="selecttype up_type" data-value="file">文件</span>
                    <input type="hidden" name="up_type" value="url" />
                </div>

                <div class="url" style="display:block;">
                    <div class="cnt-basic-i class_con">
                        <label>网址：</label>
                        <input type="text" name="url" />
                        <span class="must-input">*</span>
                    </div>

                    <div class="cnt-basic-i class_con">
                        <label>校验说明：</label>
                        <input type="text" name="url_describe" />
                    </div>
                </div>

                <div class="file" style="display:none;">
                    <div class="cnt-basic-i class_con">
                        <label>文件：</label>
                        <input type="file" name="file" />
                        <span class="must-input">*</span>
                    </div>
                    <div class="cnt-basic-i class_con">
                        <span class="input-tips">（建议PDF，不超过30M）</span>
                    </div>
                </div>

                <div class="de_y">
                    <button type="button" class="layer-close-btn cnt-label">取消</button>
                    <button type="button" class="layer-submit-btn cnt-label">提交</button>
                </div>
	        </div>
        </form>
    </div>

    <div class="cnt-basic-list cnt-basic-layer change-files order-info" id="layer_orderfile_subs">
    	<form action="{:url('add_file')}" method="post" class="layer-form" enctype="multipart/form-data">
	        <div class="cnt-basic-item">
                <div class="cnt-basic-i class_con">
                    <label>选择子订单：</label>
                    <select name="order_service_id" class="l_xiang">
                        <option value="">请选择</option>
                        {volist name="order_subs" id="vo"}
                        <option value="{$vo.id}">{$vo.order_no} / {$vo.service_name}</option>
                        {/volist}
                    </select>
                    <input type="hidden" name="type" value="1" />
                </div>

                <div class="cnt-basic-i class_con">
                    <label>选择类型：</label>
                    <span class="selecttype up_type active" data-value="url">网址</span>
                    <span class="selecttype up_type" data-value="file">文件</span>
                    <input type="hidden" name="up_type" value="url" />
                </div>

                <div class="url" style="display:block;">
                    <div class="cnt-basic-i class_con">
                        <label>网址：</label>
                        <input type="text" name="url" />
                        <span class="must-input">*</span>
                    </div>

                    <div class="cnt-basic-i class_con">
                        <label>校验说明：</label>
                        <input type="text" name="url_describe" />
                    </div>
                </div>

                <div class="file" style="display:none;">
                    <div class="cnt-basic-i class_con">
                        <label>文件：</label>
                        <input type="file" name="file" />
                        <span class="must-input">*</span>
                    </div>
                    <div class="cnt-basic-i class_con">
                        <span class="input-tips">（建议PDF，不超过30M）</span>
                    </div>
                </div>

                <div class="de_y">
                    <button type="button" class="layer-close-btn cnt-label">取消</button>
                    <button type="button" class="layer-submit-btn cnt-label">提交</button>
                </div>
	        </div>
        </form>
    </div>

    <div class="cnt-basic-list cnt-basic-layer order-info" id="layer_coupon">
    	<form action="{:url('use_coupon')}" method="post" class="layer-form" enctype="multipart/form-data">
	        <div class="cnt-basic-item">
	        	<div class="cnt-basic-i class_con">
	                <label>选择订单：</label>
                    <select name="order_id" class="l_xiang">
                        <option value="">请选择</option>
                        <option value="{$getone.id}">{$getone.order_no}</option>
                        {volist name="order_subs" id="vo"}
                            <option value="{$vo.id}">{$vo.order_no} / {$vo.service_name}</option>
                        {/volist}
                    </select>
	            </div>

                <div class="cnt-basic-i class_con">
	                <label>选择试用券：</label>
                    <select name="id" class="l_xiang">
                        <option value="">请选择</option>
                        {volist name="user_coupon" id="vo"}
                            <option value="{$vo.id}">{$vo.name}% {$vo.description}</option>
                        {/volist}
                    </select>
	            </div>

                <div class="de_y">
                    <button type="button" class="layer-close-btn cnt-label">取消</button>
                    <button type="button" class="layer-submit-btn cnt-label">使用</button>
                </div>
	        </div>
        </form>
    </div>

    {include file="common:foot"}

    <script>
        //修改后续服务弹窗
        $(".edit-orderservice").click(function () {
            var pid = $(this).parents("tr").data("id");
            layer.open({
                type: 2,
                title: ["修改后续服务", 'background-color: #0f2950; font-size:14px;text-align: center;color: #fff'],
                closeBtn: false,
                skin: 'OrderLayer',
                content: "/admin/Order/edit_service?id="+pid,
                shadeClose: true,
                area: ['674px', '350px'],
            });
        });

        //修改主订单保密文件
        $(".edit-files").click(function () {
            var pid = $(this).parents("tr").data("id");
            layer.open({
                type: 2,
                title: ["修改保密文件", 'background-color: #0f2950; font-size:14px;text-align: center;color: #fff'],
                closeBtn: false,
                skin: 'OrderLayer',
                content: "/admin/Order/edit_file?id="+pid,
                shadeClose: true,
                area: ['674px', '500px'],
            });
        });

        $(".edit-files-sub").click(function () {
            var pid = $(this).parents("tr").data("id");
            layer.open({
                type: 2,
                title: ["修改保密文件", 'background-color: #0f2950; font-size:14px;text-align: center;color: #fff'],
                closeBtn: false,
                skin: 'OrderLayer',
                content: "/admin/Order/edit_file?id="+pid,
                shadeClose: true,
                area: ['674px', '500px'],
            });
        });
    </script>

    <script>
        $(document).ready(function() {
            $('.toggle-row').click(function(e) {
                if($(e.target).closest('.mid_s').length) return;

                const targetId = $(this).data('target');
                $('#' + targetId).toggle();
                $('#' + targetId).parents(".services-row").toggle();
            });
        });
    </script>

    <script>
    $(document).ready(function() {
        // 主体选择
        $('.type').click(function() {
            $('.type').removeClass('active');
            $(this).addClass('active');
            $('input[name="type"]').val($(this).data('type'));
            $('input[name="main_id"]').val($(this).data('main-id'));
        });

        // 类型选择
        $('.up_type').click(function() {
            var this_parent = $(this).parent(".cnt-basic-i");
            this_parent.find('.up_type').removeClass('active');
            $(this).addClass('active');
            var upType = $(this).data('value');
            this_parent.find('input[name="up_type"]').val(upType);

            // 显示/隐藏对应的输入区域
            var this_parent_parent = this_parent.parent(".cnt-basic-item");
            if(upType === 'url') {
                this_parent_parent.find('.url').show();
                this_parent_parent.find('.file').hide();
            } else {
                this_parent_parent.find('.url').hide();
                this_parent_parent.find('.file').show();
            }
        });
    });
    </script>

    <script>
        // 监听 product 变化
        $('#product').on('change', function() {
            var productId = $(this).val();
            if (!productId) return; // 如果没有选择 product，直接返回

            // 发送 AJAX 请求获取 services
            $.ajax({
                url: "/admin/Order/getServices",
                type: 'GET',
                data: {product_id: productId},
                dataType: 'json',
                success: function(response) {
                    if (response.code === 1) {
                        //更新服务选项列表
                        var $serviceSelect = $('#service_id');
                        $serviceSelect.empty();
                        $serviceSelect.append('<option value="" disabled selected>请选择</option>');
                        $.each(response.data.service, function(index, service) {
                            $serviceSelect.append(
                                '<option value="' + service.id + '">' + service.name + '</option>'
                            );
                        });
                        $serviceSelect.css('color', '#333');

                        //更新产品进度列表
                        var $serviceSelect = $('#product_progress_id');
                        $serviceSelect.empty();
                        $serviceSelect.append('<option value="" disabled selected>请选择</option>');
                        $.each(response.data.progress, function(index, service) {
                            $serviceSelect.append(
                                '<option value="' + service.id + '">' + service.name + '</option>'
                            );
                        });
                        $serviceSelect.css('color', '#333');

                        //清空服务进度列表
                        $('#service_progress_id').empty().append('<option value="" disabled selected>请选择</option>');
                    }
                },
                error: function(xhr, status, error) {
                    console.error("AJAX 请求失败:", error);
                    $('#service').html('<option value="">加载失败，请重试</option>');
                }
            });
        });

        //服务变化，服务进度列表变化
        $('#service_id').on('change', function() {
            var serviceId = $(this).val();
            if (!serviceId) return; // 如果没有选择 product，直接返回

            // 发送 AJAX 请求获取 services
            $.ajax({
                url: "/admin/Order/getServiceProgress",
                type: 'GET',
                data: {service_id: serviceId},
                dataType: 'json',
                success: function(response) {
                    if (response.code === 1) {
                        //更新服务进度列表
                        var $serviceSelect = $('#service_progress_id');
                        $serviceSelect.empty();
                        $serviceSelect.append('<option value="" disabled selected>请选择</option>');
                        $.each(response.data, function(index, service) {
                            $serviceSelect.append(
                                '<option value="' + service.id + '">' + service.name + '</option>'
                            );
                        });
                        $serviceSelect.css('color', '#333');
                    }
                },
                error: function(xhr, status, error) {
                    console.error("AJAX 请求失败:", error);
                    $('#service').html('<option value="">加载失败，请重试</option>');
                }
            });
        });
    </script>
</body>
</html>