<?php
namespace app\home\controller;

use app\home\controller\Common;

use app\services\MailService;
use think\facade\Validate;
use think\facade\Cache;
use think\facade\Db;

class Email extends Common
{
    //测试优惠券邮件发送
    public function test(){
        $email = "<EMAIL>";
        $data = [
            "system_name" => config('app.system_name'),
            "username" => strstr($email, '@', true),
            "order_no" => "202506161507101560",
            "order_date" => date("Y-m-d"),
            "money" => 10000,
            "login_link" => config('app.site_url')."login",
        ];

        $result = MailService::sendEmail("create-order", $email, $data);
        var_dump($result);
    }

}