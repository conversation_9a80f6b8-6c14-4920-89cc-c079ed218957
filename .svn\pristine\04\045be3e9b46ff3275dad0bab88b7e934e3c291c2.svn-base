<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <title>Message - HitGen OpenDEL™</title>
    <link rel="shortcut icon" href="/favicon.ico" />
    <link href="__CSS__/vendors/aos.css" rel="stylesheet">
    <link href="__CSS__/style.css" rel="stylesheet" />
</head>

<body>
    <section class="bg-[url(__IMG__/backgrounds/m_bj.jpg)] bg-no-repeat pb-10 bg-size-[100%] md:bg-[url(__IMG__/backgrounds/pc_bj.jpg)] md:bg-size-[100%] md:pb-20">

        {include file="public:header"}

        <div class="w-11/12 mx-auto md:w-10/12">
            <div class="mt-5 Navigation mb-3.5 md:mb-14">
                <ul class="text-[#999] flex items-center text-md md:text-xl">
                    <li>
                        <a href="/" class="text-[#155797]">
                            Home
                        </a>
                    </li>
                    <li class="max-w-[12.5rem] line-clamp-1 md:max-w-2xs">
                        Personal Center
                    </li>
                </ul>
            </div>

            <!-- data-aos="fade-up" -->
            <div class="md:flex md:flex-row md:gap-x-4">

                {include file="user:left"}

                <div class="bg-white border border-[var(--border-color)] rounded-lg shadow-2xs
                md:w-2/3 md:rounded-2xl
                ">
                    <div
                        class="grid grid-cols-2 text-sm md:text-[1.5625rem] md:px-[1.875rem] md:pt-4 md:flex Roboto_Bold md:text-2xl md:gap-x-[4.375rem] user-tab-link">
                        <span class="user-tab-item active">
                            Voucher
                        </span>
                    </div>
                    <div class="user-profile">
                        <div class="profile-item">
                            <div class="p-4 md:p-[1.875rem]">
                                <div class="voucher-list">
                                    <div class="used-list mb-5 md:mb-[3.75rem]">
                                        <h2 class="text-base font-bold md:text-3xl">Active</h2>
                                        <div class="used-item-list mt-4 grid grid-cols-1 items-start gap-y-4 md:grid-cols-3 md:gap-x-[1.25rem]  md:mt-[1.875rem]">
                                            {volist name="coupon_active" id="vo"}
                                            <div class="used-item py-[.625rem] pr-[.625rem] text-white relative z-10">
                                                <div class="bg-[#f08411] rounded-md flex">
                                                    <div class="felx-shrink-0 w-1/3 md:w-2/5 border-dashed border-r border-white flex items-center justify-center relative used-item-l">
                                                        <div class="flex items-baseline md:text-xl">
                                                            <span class="text-2xl font-bold mr-1.5 md:text-3xl">{$vo.name}</span>
                                                            %
                                                        </div>
                                                    </div>
                                                    <div class="flex flex-col items-center w-2/3 px-[.9375rem] py-[.625rem] ">
                                                        <div class="bg-[rgba(255,255,255,0.3)] w-full text-center py-[.375rem] rounded-md font-bold text-base mb-2">
                                                            Voucher
                                                        </div>
                                                        <div class="text-left w-full">
                                                            <p class="text-xs mb-0.5">
                                                                {$vo.description}
                                                            </p>
                                                            <p class="text-sm">
                                                                Due date: {:date('M j, Y', strtotime($vo.end_time))}
                                                            </p>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            {/volist}
                                        </div>
                                    </div>

                                    {notempty name="coupon_used"}
                                    <div class="used-list mb-5 expired  md:mb-[3.75rem]">
                                        <h2 class="text-base font-bold md:text-3xl">Used</h2>
                                        <div class="used-item-list mt-4 grid grid-cols-1 items-start gap-y-4 md:grid-cols-3 md:gap-x-[1.25rem] md:mt-[1.875rem]">
                                            {volist name="coupon_used" id="vo"}
                                            <div class="used-item py-[.625rem] pr-[.625rem] text-white relative z-10">
                                                <div class="bg-[#e6e6e6] rounded-md flex">
                                                    <div class="felx-shrink-0 w-1/3 border-dashed border-r border-white flex items-center justify-center relative used-item-l">
                                                        <div class="flex items-baseline md:text-xl">
                                                            <span class="text-2xl font-bold mr-1.5 md:text-3xl">{$vo.name}</span>
                                                            %
                                                        </div>
                                                    </div>
                                                    <div class="flex flex-col items-center w-2/3 px-[.9375rem] py-[.625rem] ">
                                                        <div class="bg-[#eeeeee] w-full text-center py-[.375rem] rounded-md font-bold text-base mb-2 text-[#999999]">
                                                            Voucher
                                                        </div>
                                                        <div class="text-left w-full">
                                                            <p class="text-xs mb-0.5">
                                                                {$vo.description}
                                                            </p>
                                                            <p class="text-sm">
                                                                Due date: {:date('M j, Y', strtotime($vo.end_time))}
                                                            </p>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            {/volist}
                                        </div>
                                    </div>
                                    {/notempty}

                                    {notempty name="coupon_exchange"}
                                    <div class="used-list">
                                        <h2 class="text-base font-bold flex justify-between md:text-3xl">
                                            <span>Points Redemption</span>
                                            <a href="/user/points/" class="text-[#155797] md:text-2xl">Points Center</a>
                                        </h2>
                                        <div class="used-item-list mt-4 grid grid-cols-1 items-start gap-y-4 md:grid-cols-3 md:gap-x-[1.25rem] md:mt-[1.875rem]">
                                            {volist name="coupon_exchange" id="vo"}
                                            <div class="flex flex-col gap-y-4 mb-5">
                                                <div class="used-item py-[.625rem] pr-[.625rem] text-white relative z-10">
                                                    <div class="bg-[#f08411] rounded-md flex">
                                                        <div class="felx-shrink-0 w-1/3 border-dashed border-r border-white flex items-center justify-center relative used-item-l">
                                                            <div class="flex items-baseline md:text-xl">
                                                                <span class="text-2xl font-bold mr-1.5 md:text-3xl">{$vo.name}</span>
                                                                %
                                                            </div>
                                                        </div>
                                                        <div class="flex flex-col items-center w-2/3 px-[.9375rem] py-[.625rem] ">
                                                            <div class="bg-[rgba(255,255,255,0.3)] w-full text-center py-[.375rem] rounded-md font-bold text-base mb-2">
                                                                Voucher
                                                            </div>
                                                            <div class="text-left w-full">
                                                                <p class="text-xs mb-0.5">
                                                                    {$vo.description}
                                                                </p>
                                                                <!-- <p class="text-sm">
                                                                    {if $vo.exchange_status==1}Due date: {:date('M j, Y', strtotime($vo.end_time))}{/if}
                                                                </p> -->
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="flex justify-between items-center">
                                                    <p class="text-sm text-[#666]">
                                                        Points Needed ：<span class="text-[#f08411]">{$vo.points}</span>
                                                    </p>
                                                    {if $vo.exchange_status==0}
                                                    <button type="button" data-id="{$vo.id}" class="text-white bg-[#155797] py-[.25rem] px-[.625rem] rounded-md border-0 text-sm cursor-pointer btn-exchange">
                                                        Exchange
                                                    </button>
                                                    {else }
                                                    <button type="button" disabled
                                                        class="text-white bg-[#e6e6e6] py-[.25rem] px-[.625rem] rounded-md border-0 text-sm cursor-pointer">
                                                        Exchange
                                                    </button>
                                                    {/if}
                                                </div>
                                            </div>
                                            {/volist}
                                        </div>
                                    </div>
                                    {/notempty}
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>

        </div>
    </section>

    {include file="public:footer"}

    {include file="public:foot"}
    <script>
        $(document).ready(function() {
            $('.btn-exchange').click(function() {
                var that = $(this);
                layer.confirm('Are you sure you want to exchange this voucher?', {
                    title: "Information",
                    btn: ['Yes', 'No'] //按钮
                }, function() {
                    $.ajax({
                        type: "POST",
                        url: "{:url('user/coupon_exchange')}",
                        data: {
                            id: that.data('id')
                        },
                        success: function(res) {
                            if (res.code == 1) {
                                layer.msg(res.msg, {
                                    time: 2000,
                                    icon: 1
                                });
                                setTimeout(function() {
                                    window.location.reload();
                                }, 2000);
                            } else {
                                layer.msg(res.msg, {
                                    time: 2000,
                                    icon: 2
                                });
                            }
                        },
                        error: function() {
                            layer.msg('Network error, please try again later', {
                                time: 2000,
                                icon: 2
                            });
                        }
                    });
                }, function() {});
            });
        });
    </script>
</body>

</html>