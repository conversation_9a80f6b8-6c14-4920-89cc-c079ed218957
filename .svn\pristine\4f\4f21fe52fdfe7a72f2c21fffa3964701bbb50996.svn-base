@import "tailwindcss";
@media (min-width: 1920px) {
  :root {
    font-size: 16px;
  }

  body {
    max-width: 1920px;
    margin: 0 auto;
  }
}

@media (max-width: 1680px) and (min-resolution: 120dpi) {
  :root {
    font-size: 12.8px;
  }

  body {
    max-width: 1536px;
    margin: 0 auto;
  }
}

@media (max-width: 760px) {
  :root {
    font-size: 18px;
  }
}


:root {
  --border-color: #dae9ff;
}

@font-face {
  font-family: 'Roboto_Regular';
  src: url('/static/home/<USER>/roboto/Roboto-Regular.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'Roboto_LightItalic';
  src: url('/static/home/<USER>/roboto/Roboto-LightItalic.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'Roboto_Bold';
  src: url('/static/home/<USER>/roboto/Roboto-Bold.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
}

/* @font-face {
    font-family: 'Roboto_Regular';
    src: url('/font/roboto/Roboto-Regular.ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
  } */

body {
  font-family: 'Roboto_Regular', sans-serif;
  /* font-size: 1rem; */
}

.Roboto_Light {
  font-family: 'Roboto_Light', sans-serif;
}

.Roboto_LightItalic {
  font-family: 'Roboto_LightItalic', sans-serif;
}

.Roboto_Regular {
  font-family: 'Roboto_Regular', sans-serif;
}

.Roboto_Bold {
  font-family: 'Roboto_Bold', sans-serif;
}

.slide-thumb-active {
  @apply bg-[#105eb3] text-white
}

.Digital-l {
  @apply text-base md:text-xl;
}

.Digital-r {
  @apply text-sm text-[#999] md:text-base;
}

.swiper.swiper-banner {
  --swiper-pagination-bullet-inactive-color: #fff;
  --swiper-pagination-color: #fff;
}

.swiper-banner-pagination {
  bottom: .75rem !important;
}

.hollow-number {
  -webkit-text-stroke: 2px white;
  text-stroke: 2px white;
  /* 兼容部分浏览器 */
}

.product-introduction dt {
  @apply relative;
}

/* .product-introduction dt::after {
  content: '';
  @apply absolute w-1.5 h-1.5 rounded-full bg-[#000] top-1/2 -left-4 -translate-y-1/2;
} */

.hollow-number-ripple {
  position: relative;
  display: inline-block;
  z-index: 1;
}

.service-pagination .Digital-r {
  @apply text-[#d5eaff]
}

:root {
  --animate-duration: 800ms;
  --animate-delay: 0.4s;
}

.navigation:hover .secondary {
  display: block;
}

.navigation-item:hover>ul {
  display: block;
}

.Navigation li::after {
  content: '/';
  display: inline-block;
  margin: 0 .3125rem;
}

.Navigation li:last-child::after {
  content: '';
}

.content-txt p {
  margin-block-start: 1em;
  margin-block-end: 1em;
  color: #666;
  @apply text-base md:text-xl;
}

.item-btn.active {
  @apply bg-[#e0eaff] border border-[#155290] text-[#030000]
}

.user-tab-link{
  @apply border-b-2 border-[#dae9ff]
}

.user-tab-link > span,
.user-tab-link a {
  @apply p-4 text-[#999] relative block cursor-pointer md:py-6
}

.user-tab-link > span.active,
.user-tab-link a.active {
  @apply text-[#111111]
}

.user-tab-link > span.active::after,
.user-tab-link a.active::after{
  content: '';
  @apply border-[#155797] border-b-[.0625rem] absolute w-full left-0 -bottom-[.125rem] md:border-b-2
}

.navigation-bar a {
  font-family: 'Roboto_Bold', sans-serif;
  @apply block;
}

.Points-list li {
  @apply border-b border-[#e0eaff] py-3 md:py-5
}

.Points-list li:last-child {
  @apply border-b-0
}

.Points-list li:first-child {
  @apply pt-0
}

.Points-list li>span {
  @apply text-sm text-[#999] md:text-base
}

.Points-list li>p {
  @apply md:text-xl md:mt-2
}

.message-list li {
  @apply border-b border-[#e0eaff] py-3 flex items-start gap-x-3 md:py-5 md:gap-x-[1.25rem]
}

.message-img {
  @apply w-10 h-10 rounded-full flex-shrink-0 items-center justify-center md:w-[5rem] md:h-[5rem]
}

.message-list li:last-child {
  @apply border-b-0
}

.message-info strong {
  @apply mr-2.5
}

.message-content p a {
  @apply text-[#f08411]
}

.message-tips-list li {
  @apply border-b border-[#e0eaff] p-3 flex items-center text-sm gap-x-1 md:px-[1.875rem] md:py-[1.25rem] md:text-base
}

.message-tips-list li span {
  @apply line-clamp-1
}

.used-item::after {
  content: '';
  @apply absolute right-0 top-0 w-11/12 h-full rounded-tr-lg rounded-br-lg rounded-bl-4xl rounded-tl-4xl bg-[#fff2e4] -z-[1]
}

.used-item-l::after {
  content: '';
  background: linear-gradient(180deg, transparent 50%, #fff2e4 50%);
  @apply absolute -right-[0.625rem] -top-[0.725rem] w-[1.25rem] h-[1.25rem] rounded-full
}

.used-item-l::before {
  content: '';
  background: linear-gradient(0deg, transparent 50%, #fff2e4 50%);
  @apply absolute -right-[0.625rem] -bottom-[0.725rem] w-[1.25rem] h-[1.25rem] rounded-full
}

.used-list.expired .used-item-l::after {
  background: linear-gradient(180deg, transparent 50%, #fafbff 50%);
}

.used-list.expired .used-item-l::before {
  background: linear-gradient(0deg, transparent 50%, #fafbff 50%);
}

.used-list.expired .used-item::after {
  @apply bg-[#fafbff]
}

.order-tab-item{
  @apply py-2 px-3.5 rounded-tl-xl rounded-tr-xl cursor-pointer text-[#999] md:px-[1.25rem] md:py-[.625rem] md:rounded-tr-2xl md:rounded-tl-2xl
}

.order-tab-item.active{
  @apply bg-[#f08411] text-white
}

.order-right-text-item{
  @apply border-b border-[#e0eaff] flex items-center justify-between py-2 md:py-3
}

.item-status{
  @apply text-xs text-right w-1/2 md:w-1/3 md:text-base
}

.item-left-text-name{
  @apply w-1/2 line-clamp-1 text-balance md:text-xl md:w-2/3
}

.order-list-item-container-item{
  @apply pb-3 mb-5
}
.user-name-drop {
  display: none;
}

.product-list-tab-item{
  @apply h-[2.5rem] leading-[2.5rem] px-5 text-center relative
}

.arrow-container {
  display: flex;
  align-items: center;
  font-family: sans-serif;
}

.arrow-step {
  position: relative;
  background-color: #dddddd;
  flex-shrink: 0;
  cursor: pointer;
  @apply h-[2.5rem] leading-[2.5rem] pr-[1.5625rem] pl-[2rem] mr-[0.125rem] text-[#999] md:h-[3.75rem] md:leading-[3.75rem] md:px-[3.75rem]
}

.arrow-step::before,
.arrow-step::after {
  content: "";
  position: absolute;
  top: 0;
  width: 0;
  height: 0;
  border-top: 1.25rem solid transparent;
  border-bottom: 1.25rem solid transparent;
  @apply md:border-t-[1.875rem] md:border-b-[1.875rem]
}

.arrow-step::before {
  left: 0;
  /* border-left: none; */
  border-left: 1rem solid white;
  z-index: 0;
  @apply border-l-[1rem] md:border-l-[1.5rem]
}

.arrow-step::after {
  right: -1rem;
  border-left: 1rem solid #ddd;
  z-index: 1;
  @apply border-l-[1rem] md:border-l-[1.5rem] md:-right-[1.45rem]
}

/* 第一个步骤不需要左边三角 */
.arrow-step:first-child{
  @apply pl-[1rem] md:pl-[3.75rem];
}
.arrow-step:first-child::before {
  display: none;
  padding-left: 1rem;
}

.arrow-step.active {
  color: #fff !important;
}

/* 服务开启 */
.arrow-step.service-proceed {
  background-color: #dae9ff;
}

.arrow-step.service-proceed::after {
  border-left-color: #dae9ff;
}

/* 服务完成 */

.arrow-step.service-completed {
  background-color: #f08411;
  /* color: white; */
}

.arrow-step.service-completed::after {
  border-left-color: #f08411;
}

/* 进行中步骤 */
.arrow-step.proceed {
  background-color: #f08411;
  color: #f5f5f5;
}

.arrow-step.proceed::after {
  border-left-color: #f08411;
}

/* 已完成步骤 */
.arrow-step.completed {
  background-color: #28a745;
  /* color: white; */
}

.arrow-step.completed::after {
  border-left-color: #28a745;
}

.download-list ul{
  @apply grid grid-cols-1 md:grid-cols-4
}

.download-list ul>li{
  @apply py-3 px-3 border-b border-[#dae9ff] border-r md:p-5
}

.action-buttons button{
  @apply px-5 py-1.5 text-white rounded-sm
}

.about-container p{
  margin-block-start: 1em;
  margin-block-end: 1em;
  @apply text-sm text-[#111] md:text-xl
}

.about-container h2{
  font-size: 1.5rem;
  @apply text-xl text-[#111] md:text-3xl
}

.about-container h3{
  font-size: 1.17rem;
  @apply text-xl text-[#111] md:text-2xl
}

.btn-terms button{
  @apply py-2.5 rounded-full 
}

.discover-news-screening span{
  @apply px-4 py-1.5 rounded-md text-[#999] border border-transparent md:py-3 md:px-10 cursor-pointer
}

.discover-news-screening span.active{
  @apply text-[#155797] border border-[#155797]
}

.news-content-item h3 a{
  @apply text-[#666]
}

.news-content-item h3 a:hover{
  @apply text-[#155797]
}

.news-content-item li{
  @apply md:rounded-2xl
}

.news-content-item li:hover{
  @apply border-[#155797]
}

.resources span{
  @apply px-4 py-1.5 rounded-md text-[#999] border border-[#e0eaff] md:py-4 md:px-8 cursor-pointer md:rounded-xl
}

.resources-list::after{
  content: '';
  @apply absolute h-full w-[1px] right-1/2 -translate-1/2 top-1/2 border-[#dae9ff] border-r
}

.resources span.active{
  @apply bg-[#e0eaff] text-[#030000] border-[#155290]
}

#btn_agree.active{
  @apply bg-[#5399ff]
}

.browsing-item{
  @apply flex gap-x-1.5 items-baseline
}

.browsing-item strong{
  @apply text-base text-[#111] md:text-3xl
}

.browsing-item span{
  @apply text-sm text-[#666] md:text-xl
}

.iCommunity-tab .iCommunity-btn{
  @apply border-b-2 border-transparent pb-2 cursor-pointer
}

.iCommunity-tab .iCommunity-btn.active{
 @apply border-[#155797]
}

.iCommunity-content-item > ul > li{
  @apply border-b border-[#e0eaff] p-3 flex gap-x-2 md:p-7 md:gap-x-5
}

.iCommunity-membership-content-item {
  @apply bg-white py-3 px-4 rounded-md flex flex-col gap-y-3 border border-[#c4d7ff] md:py-[1.875rem] md:px-[1.875rem] md:gap-y-[1.25rem]
}

.badge-about .badge-about-btn{
  @apply pb-4 text-base relative cursor-pointer font-bold md:text-2xl
}

.user-info-card .badge-about-btn,
.user-card-portal .badge-about-btn {
  font-size: 1rem;
}

.badge-about .badge-about-btn.active::after{
  content: '';
  @apply absolute bottom-0 left-0 border-b-2 border-[#155797] w-full
}

.iCommunity-content-item > ul >li:last-child{
  @apply border-b-0
}

.list-posts >ul >li{
  @apply border-b border-[#dae9ff] md:relative
}

.list-posts >ul >li .item-posts{
  @apply p-4 flex items-start gap-x-2.5 md:px-[3.75rem] md:py-[1.5rem] md:max-w-[80%] md:gap-x-5
}

.list-posts >ul >li:last-child{
  @apply border-b-0
}

.tag-list > ul > li{
  @apply cursor-pointer border border-[#bdcbe9] bg-white rounded-sm px-3 py-1.5 text-sm text-[#666] md:text-xl md:py-2
}

.tag-list > ul > li.active{
  @apply text-[#155797] border-[#155797] bg-[#f8fdff]
}

.tag-search-right-header > ul > li{
  @apply cursor-pointer bg-[#155797] text-white flex items-center gap-x-2.5 px-3 py-1.5 rounded-sm text-sm md:text-xl md:py-2
}

.post-tag-list > ul{
  @apply flex gap-x-2.5 flex-wrap
}

.post-tag-list > ul>li{
  @apply cursor-pointer border border-[#dae9ff] bg-[#fafbff] rounded-sm text-sm text-[#111] md:text-xl md:py-0.5 md:px-2.5
}

.post-tag-list >ul>li a{
  @apply block px-3 py-1.5
}

.multi-select-container {
  position: relative;
}
.multi-select-display {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  cursor: pointer;
  background: #fff;
  position: relative;
}
.multi-select-display:focus-within {
  border-color: #40a9ff;
}
.multi-select-input {
  border: none;
  outline: none;
  flex: 1;
  background: transparent;
  cursor: pointer;
}
.multi-select-arrow {
  margin-left: 0.25rem;
  color: #999;
  pointer-events: none;
}
.multi-select-tag {
  background: #f0f0f0;
  color: #1890ff;
  border-radius: 0.125rem;
  padding: 0 0.375rem;
  display: flex;
  align-items: center;
}
.multi-select-tag .close {
  margin-left: 0.125rem;
  cursor: pointer;
  color: #999;
}
.multi-select-dropdown {
  position: absolute;
  left: 0;
  top: 100%;
  width: 100%;
  background: #fff;
  border: 1px solid #e6e6e6;
  border-radius: 0.25rem;
  box-shadow: 0 0.125rem 0.5rem rgba(0,0,0,0.08);
  z-index: 10;
  max-height: 12.5rem;
  overflow-y: auto;
}
.multi-select-option {
  padding: 0.5rem 1rem;
  cursor: pointer;
}
.multi-select-option.selected {
  color: #1890ff;
  background: #e6f7ff;
}
.multi-select-option:hover {
  background: #f5f5f5;
}

.result-list .result-item{ 
  @apply border-b border-[#dae9ff] pb-3 mb-3;
}

.result-list .result-item:last-child{ 
  @apply border-b-0 mb-0
}


@media screen and (min-width: 768px) {
  .swiper-banner-pagination {
    bottom: 2.5rem !important;
  }

  .secondary {
    box-shadow: 0 0 1.25rem -0.125rem rgba(84, 111, 138, 0.3)
  }

}

@media (hover: hover) and (pointer: fine) {
  .btn-name:hover .user-name-drop {
    display: block;
  }
}