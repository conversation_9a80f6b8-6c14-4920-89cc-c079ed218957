<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <title>{$tdk.seo_title?$tdk.seo_title:"HitGen OpenDEL™"}</title>
    <!-- SEO 信息 -->
    <meta name="robots" content="index,follow" />
    <meta name="author" content="HitGen" />
    <meta name="copyright" content="Copyright © 2024 HitGen Inc." />
    <meta name="revisit-after" content="7 days" />
    <meta name="generator" content="HitGen OpenDEL™" />
    <meta name="keywords" content="{$tdk.seo_keywords}" />
    <meta name="description" content="{$tdk.seo_description}" />

    <link rel="shortcut icon" href="/favicon.ico" />
    <link href="__CSS__/vendors/swiper-bundle.min.css" rel="stylesheet" />
    <link href="__CSS__/vendors/animate.min.css" rel="stylesheet" />
    <link href="__CSS__/vendors/aos.css" rel="stylesheet">
    <link href="__CSS__/style.css" rel="stylesheet" />
</head>

<body>
    <section class="bg-[url(__IMG__/backgrounds/m_bj.jpg)] bg-no-repeat pb-10 bg-size-[100%] relative md:bg-[url(__IMG__/backgrounds/pc_bj.jpg)] md:bg-cover md:pb-20">

        {include file="public:header"}

        <article class="w-11/12 mx-auto pt-8 md:pt-28 md:mb-20 md:w-10/12">
            <div class="Roboto_LightItalic flex justify-center mb-4 md:mb-12" data-aos="fade-right">
                <span
                    class="bg-[rgba(102,102,102,0.1)] text-[#333] inline-block px-4 py-2 rounded-full rounded-bl-none text-base md:w-[18.75rem] md:h-12 md:text-xl md:text-center"
                    role="doc-subtitle">
                    “ Welcome to OpenDEL™ ”</span>
            </div>
            <div class="w-11/12 text-center mx-auto flex flex-col md:items-center" data-aos="fade-up">
                <h1 class="text-6xl text-[#333333] md:text-8xl md:mb-10">HitGen OpenDEL™</h1>
                <p class="text-[#666] text-sm text-center mt-4 md:text-xl md:max-w-[37.5rem] md:mb-10">
                    Facilitate internal management and customer communication, enhance
                    customer stickiness, improve customer experience, and expand
                    influence.
                </p>
                <div class="bg-[#105eb3] relative mt-8 rounded-md md:w-[18.75rem]">
                    <a href="/about" class="flex justify-center items-center gap-2 text-white text-sm py-3 md:text-xl md:h-16 md:justify-evenly">
                        <span> Learn More </span>
                        <img src="__IMG__/icons/changjiantou-zuoshang.png" alt="箭头图标"
                            class="w-3 absolute right-7 md:w-auto md:relative md:right-0" />
                    </a>
                </div>
            </div>
        </article>

        <section class="swiper swiper-banner mt-7 w-11/12 mx-auto relative pb-11 md:w-10/12" data-aos="fade-up">
            <div class="swiper-wrapper">
                {volist name="banner" id="vo"}
                <div class="swiper-slide">
                    <a href="/">
                        <picture class="w-full">
                            <source media="(min-width: 37.5625rem)" srcset="{$vo.image}"
                                class="w-full rounded-xl object-cover" />
                            <img src="{$vo.smt_image}" alt="banner_01" class="w-full rounded-xl object-cover" />
                        </picture>
                    </a>
                </div>
                {/volist}
            </div>

            <div class="swiper-pagination swiper-banner-pagination bottom-3 md:bottom-10"></div>
            <div class="md:bottom-10 absolute bottom-3 right-4 z-9 flex items-center justify-center gap-5 md:right-10">
                <div
                    class=" swiper-banner-prev w-5 h-5 md:w-[0.75rem] md:h-[1.1875rem] bg-[url(__IMG__/icons/banner_l.png)] bg-no-repeat bg-center bg-size-[0.5rem_0.75rem] md:bg-size-[0.7rem] cursor-pointer">
                </div>
                <div
                    class="swiper-banner-next w-5 h-5 md:w-[0.75rem] md:h-[1.1875rem] bg-[url(__IMG__/icons/banner_r.png)] bg-no-repeat bg-center bg-size-[0.5rem_0.75rem] md:bg-size-[0.7rem] cursor-pointer">
                </div>
            </div>

            <div
                class="number-pagination absolute left-4 bottom-3 z-50 text-white Roboto_Light flex items-center gap-2 text-[0.75rem] md:left-10 md:bottom-10">
            </div>
        </section>
    </section>

    <main class="w-full">
        <section class=" bg-[#f5f9ff]">
            <div class="w-11/12 mx-auto flex flex-col pb-6 md:w-10/12 relative">
                <header class="pt-8 pb-6 md:pt-20 md:pb-14">
                    <h2 class="Roboto_Bold text-2xl text-[#111] md:text-5xl">
                        Latest Progress of DEL
                    </h2>
                </header>
                <section class="flex flex-col gap-y-4 md:flex-row md:gap-x-8">
                    <article class="md:flex-1" data-aos="fade-right">
                        <ul role="list" class="grid grid-cols-1 border border-[#e0eaff] md:grid-cols-2 md:h-[37.4375rem]">
                            {volist name="news" id="vo" key="k"}
                            <li class="flex group flex-col bg-white px-5 py-4 gap-3 border-b border-[#e0eaff] md:order-1 md:px-10 md:py-7 md:gap-0 md:relative {:$k % 2 === 1 ? 'md:border-r border-[#e0e0ff]' : ''}">
                                <div class="text-sm md:mb-6 md:text-base">
                                    <span class="text-[{$vo.color}] min-w-12 bg-[{$vo.backgroud_color}] text-center py-0.5 px-1.5 rounded-sm mr-1" style="color: {$vo.color}; background-color: {$vo.backgroud_color}">{$vo.name}</span>
                                    <time>{:date('j F Y', strtotime($vo.publish_date))}</time>
                                </div>
                                <div>
                                    <h3 class="text-xl Roboto_Bold mb-2.5 line-clamp-1 md:text-3xl md:mb-5">
                                        <a href="/news/{$vo.seo_url}">
                                            {$vo.title}
                                        </a>
                                    </h3>
                                    <p class="line-clamp-2 text-[#999] text-sm md:text-xl">
                                        {$vo.content|strip_tags}
                                    </p>
                                </div>
                                <div class="flex justify-end mt-2.5 md:absolute md:bottom-7 md:right-10 transition-all group-hover:right-5">
                                    <a href="/news/{$vo.seo_url}" class="flex items-center gap-1.5 md:gap-x-3">
                                        <span class="text-[#333] text-sm md:text-base">Learn More</span>
                                        <img src="__IMG__/icons/changjiantou-r.png" alt="more" class="w-4 inline-block md:w-[1.2rem] md:opacity-30 group-hover:opacity-100" />
                                    </a>
                                </div>
                            </li>
                            {/volist}
                        </ul>
                    </article>
                    <aside class="md:w-[34.5rem] md:h-[37.4375rem]" data-aos="fade-left">
                        <figure>
                            <img src="__IMG__/BHEEHGGIAFIJB-a0bqJwnT0B.jpg" alt="Latest DEL Progress Image" class="object-cover w-full rounded-tr-3xl" />
                            <figcaption class="sr-only">
                                Latest Progress Visual Overview
                            </figcaption>
                        </figure>
                    </aside>
                </section>

                <div class="bg-[#ffffff] relative mt-4 rounded-md border border-[#dae9ff] md:absolute md:right-0 md:top-14 md:w-[16.25rem] group ">
                    <a href="/news" class="flex justify-center items-center gap-2 text-[#155797] text-sm py-3 md:text-xl md:h-[3.75rem] md:justify-evenly">
                        <span> Learn More </span>
                        <img src="__IMG__/icons/changjiantou-zuoshang_b.png" alt="more" class="w-3 absolute right-7 transition-all md:w-[1rem] group-hover:right-2 md:relative md:right-0" />
                    </a>
                </div>
            </div>
        </section>

        <section class="relative" data-aos="fade-up">
            <header class="w-11/12 mx-auto pt-8 pb-6 md:w-10/12 md:p-0 md:flex md:justify-between md:items-center md:pt-20 md:pb-14">
                <h2 class="Roboto_Bold text-2xl text-[#111] md:text-5xl">User Service</h2>
                <div class="bg-[#ffffff] relative mt-4 rounded-md border border-[#dae9ff] group md:w-[16.25rem] md:block hidden">
                    <a href="/product" class="flex justify-center items-center gap-2 text-[#155797] text-sm py-3 md:text-xl md:h-[3.75rem] md:justify-evenly">
                        <span> Learn More </span>
                        <img src="__IMG__/icons/changjiantou-zuoshang_b.png" alt="more"
                            class="w-3 absolute right-7 transition-all md:w-[1rem] group-hover:right-2 md:relative md:right-0">
                    </a>
                </div>
            </header>
            <div class="swiper swiper-service relative">
                <div class="swiper-wrapper">
                    <div class="swiper-slide">
                        <style>
                            .swiper-1{
                                background-image: url('{$product.image_background_smt}');

                            }
                            @media (min-width: 768px) {
                               .swiper-1{ background-image: url('{$product.image_background}');
                               }
                            }
                        </style>
                        <div class="swiper-1 swiper-slide-content min-h-lvh bg-no-repeat bg-center bg-cover py-10 md:h-[62.5rem] md:min-h-auto md:bg-center overflow-hidden md:pt-24">
                            <div class="w-11/12 mx-auto md:w-10/12 flex flex-col gap-y-20 md:flex-row md:items-center md:pr-[9.375rem]">
                                <!-- 文字 -->
                                <div class="order-2 flex-1/2 md:order-1">
                                    <article class="product-introduction">
                                        <h3 class="text-lg Roboto_Bold text-[#111] mb-2 md:text-4xl md:mb-5">{$product.name}</h3>
                                        <p class="text-sm line-clamp-2 mb-3.5 md:text-xl">
                                            {$product.title}
                                        </p>
                                        {$product.content|raw}
                                    </article>
                                    <section class="flex items-center gap-3 mt-5 md:mt-10 relative z-30">
                                        <a href="/product/{$product.seo_url}" class="flex justify-around items-center border border-[#155797] gap-2 text-[#155797] text-sm py-2.5 min-w-36 rounded-md md:w-[16.25rem] md:h-[3.75rem] md:text-xl md:rounded-lg">
                                            <span> Learn More </span>
                                            <img src="__IMG__/icons/changjiantou-zuoshang_b.png" alt="more" class="w-3 right-7 md:w-[1rem]" />
                                        </a>
                                        <a class="quote-btn flex min-w-36 justify-around items-center border border-[#f08411] gap-2 text-[#f08411] text-sm py-2.5 cursor-pointer rounded-md md:w-[16.25rem] md:h-[3.75rem] md:text-xl md:rounded-lg"
                                            href="{:session('userId') ? url('/quote/logged') : 'javascript:;'}"
                                            onclick="return {:session('userId') ? 'true' : 'showQuotePopup()'}">
                                            <span> Quote </span>
                                            <img src="__IMG__/icons/gouwuche-2.png" alt="more" class="w-4 right-7 md:w-[1.25rem]" />
                                        </a>
                                    </section>
                                </div>
                                <!-- 图片 -->
                                <div class="order-1 flex-1/2 flex items-center md:order-2">
                                    <div class="w-80 h-80 mx-auto rounded-full relative hollow-number-ripple md:w-[42.5rem] md:h-[42.5rem]">
                                        <figure class="animate__animated animate__delay-.5s hollow-number border border-white rounded-full animate__fadeInRight">
                                            <img src="{$product.image}" alt="case_01"
                                                class="w-full object-cover rounded-full" />
                                            <figcaption class="sr-only">{$product.name}</figcaption>
                                        </figure>
                                        <span
                                            class="animate__animated animate__delay-1s hollow-number Roboto_Bold absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 text-9xl text-transparent md:text-[12.5rem] animate__fadeInRight">
                                            01
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {volist name="product.services" id="vo" key="k"}
                        <style>
                            .swiper-1-{$k+1}{
                                background-image: url('{$vo.image_background_smt}');

                            }
                            @media (min-width: 768px) {
                               .swiper-1-{$k+1}{ background-image: url('{$vo.image_background}');
                               }
                            }
                        </style>
                    <div class="swiper-slide">

                        <div class="swiper-1-{$k+1} swiper-slide-content min-h-lvh bg-no-repeat bg-center bg-cover py-10 md:h-[62.5rem] md:min-h-auto md:bg-center overflow-hidden md:pt-24">
                            <div class="w-11/12 mx-auto md:w-10/12 flex flex-col gap-y-20 md:flex-row md:items-center md:pr-[9.375rem]">
                                <!-- 文字 -->
                                <div class="order-2 flex-1/2 md:order-1">
                                    <article class="product-introduction">
                                        <h3 class="text-lg Roboto_Bold text-[#111] mb-2 md:text-4xl md:mb-5">{$vo.name}</h3>
                                        {$vo.description|raw}
                                    </article>
                                    <section class="flex items-center gap-3 mt-5 md:mt-10 relative z-30">
                                        <a href="/service/{$vo.seo_url}"
                                            class="flex justify-around items-center border border-[#155797] gap-2 text-[#155797] text-sm py-2.5 min-w-36 rounded-md md:w-[16.25rem] md:h-[3.75rem] md:text-xl md:rounded-lg">
                                            <span> Learn More </span>
                                            <img src="__IMG__/icons/changjiantou-zuoshang_b.png" alt="more"
                                                class="w-3 right-7 md:w-auto" />
                                        </a>
                                        <a
                                            class="quote-btn flex min-w-36 justify-around items-center border border-[#f08411] gap-2 text-[#f08411] text-sm py-2.5 cursor-pointer rounded-md md:w-[16.25rem] md:h-[3.75rem] md:text-xl md:rounded-lg">
                                            <span> Quote </span>
                                            <img src="__IMG__/icons/gouwuche-2.png" alt="more"
                                                class="w-4 right-7 md:w-auto" />
                                        </a>
                                    </section>
                                </div>
                                <!-- 图片 -->
                                <div class="order-1 flex-2/3 flex items-center md:order-2">
                                    <div
                                        class="w-80 h-80 mx-auto rounded-full relative hollow-number-ripple md:w-[42.5rem] md:h-[42.5rem]">
                                        <!-- <span
                                            class="absolute inline-flex h-full w-full animate-ping rounded-full bg-white opacity-75 -z-[1]"></span> -->
                                        <figure class="animate__animated animate__delay-.5s hollow-number border border-white rounded-full animate__fadeInRight">
                                            <img src="{$vo.image}" alt="case_01"
                                                class="w-full object-cover rounded-full" />
                                            <figcaption class="sr-only">{$vo.name}</figcaption>
                                        </figure>
                                        <span
                                            class="animate__animated animate__delay-1s hollow-number Roboto_Bold absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 text-9xl text-transparent md:text-[12.5rem] animate__fadeInRight">
                                            {$k+1|str_pad=2,0,STR_PAD_LEFT}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {/volist}
                </div>
                <div class="absolute flex items-center justify-center w-full top-96 z-50 gap-x-10 md:right-1/12 md:top-1/3 md:-translate-y-1/3 md:w-20 md:flex-col md:gap-y-10">
                    <div class=" swiper-service-prev w-10 h-10 border border-white rounded-full bg-[url(__IMG__/icons/banner_l.png)] bg-no-repeat bg-center bg-size-[0.5rem_0.75rem] cursor-pointer md:order-2 md:w-20 md:h-20 md:bg-size-[.8rem]">
                    </div>
                    <div class="service-pagination text-white Roboto_Light flex items-center gap-2 text-[0.75rem] md:order-1">
                    </div>

                    <div class="swiper-service-next w-10 h-10 border border-white rounded-full bg-[url(__IMG__/icons/banner_r.png)] bg-no-repeat bg-center bg-size-[0.5rem_0.75rem] cursor-pointer md:order-2 md:w-20 md:h-20 md:bg-size-[.8rem]">
                    </div>
                </div>
            </div>
            <!-- 缩略图 -->
            <div class="hidden md:block h-[9.375rem] absolute bottom-0 z-10 w-full bg-[rgba(208,221,234,0.2)]">
                <div class="w-10/12  mx-auto">
                    <div class="swiper swiper-service-thumb">
                        <div class="swiper-wrapper">
                            <div class="swiper-slide text-[#feffff]">
                                <div
                                    class=" h-[9.375rem] cursor-pointer flex flex-col justify-center items-center gap-y-2 Roboto_Bold">
                                    <h3 class="text-4xl">01</h3>
                                    <p class="text-xl">
                                        {$product.name}
                                    </p>
                                </div>
                            </div>
                            {volist name="product.services" id="vo" key="k"}
                            <div class="swiper-slide text-[#feffff]">
                                <div
                                    class=" h-[9.375rem] cursor-pointer flex flex-col justify-center items-center gap-y-2 Roboto_Bold">
                                    <h3 class="text-4xl">{$k+1|str_pad=2,0,STR_PAD_LEFT}</h3>
                                    <p class="text-xl">
                                        {$vo.name}
                                    </p>
                                </div>
                            </div>
                            {/volist}
                        </div>
                    </div>
                </div>
            </div>
            <div class="bg-[#ffffff] relative mt-4 rounded-md border border-[#dae9ff] group w-11/12 mx-auto md:hidden">
                <a href="" class="flex justify-center items-center gap-2 text-[#155797] text-sm py-3 md:text-xl md:h-[3.75rem] md:justify-evenly">
                    <span> Learn More </span>
                    <img src="__IMG__/icons/changjiantou-zuoshang_b.png" alt="more"
                        class="w-3 absolute right-7 transition-all md:w-[1rem] group-hover:right-2 md:relative md:right-0">
                </a>
            </div>
        </section>

        <section class="flex flex-col gap-y-6 w-11/12 mx-auto pt-8 pb-6 md:flex-row md:gap-x-24 md:p-0 md:w-10/12">
            <header class="md:border-r md:border-[#dae9ff] md:flex-1" data-aos="fade-right">
                <div class="md:max-w-[35.1875rem] md:flex flex-col md:gap-y-4 md:justify-center md:h-full">
                    <h2 class="text-xl Roboto_Bold text-[#333] md:text-5xl">
                        What are people in the community saying?
                    </h2>
                    <p class="text-sm text-[#999] mt-2 md:text-xl md:leading-9 md:mb-10">
                        our clients feedback speaks volumes.discover their stories of success,collaboration,and the
                        impactful results we ve delivered together
                    </p>
                    <div class="bg-[#ffffff] relative hidden md:block group">
                        <a href="/iCommunity/" class="flex justify-center items-center gap-2 text-[#155797] text-sm py-3 md:text-xl md:justify-evenly md:w-[21.625rem] md:h-[3.75rem] border border-[#dae9ff] rounded-md">
                            <span> Explore the community </span>
                            <img src="__IMG__/icons/changjiantou-zuoshang_b.png" alt="more" class="w-3 absolute right-7 group-hover:right-5 md:right-0 md:w-[1rem] md:h-auto md:relative transition-all">
                        </a>
                    </div>
                </div>
            </header>
            <div class="flex flex-col md:flex-row md:gap-x-5 md:flex-1">
                <div class="l myscroll h-[25rem] overflow-hidden md:h-[62.5rem]">
                    <ul class="flex flex-col gap-y-3 md:gap-y-5">
                        {volist name="all_post[0]" id="vo"}
                        <li class="border border-[#dae9ff] rounded-sm px-5 py-5 flex flex-col gap-y-3 bg-white md:p-10 md:min-h-[28.125rem] overflow-hidden md:rounded-xl">
                            <div class="flex gap-x-3 items-center md:mb-5">
                                <img src="{$vo.avatar ?? '__IMG__/user-1.jpg'}" alt="{$vo.user.first_name}" class="w-10 h-10 rounded-lg md:w-20 md:h-20 border border-[#dae9ff]">
                                <div class="md:ml-2.5">
                                    <strong class="text-[#1f1f1f] md:text-xl">{$vo.user.first_name} {$vo.user.last_name}</strong>
                                    <p class="text-[#999] text-sm md:text-base">
                                        {$vo.user.role_name}
                                    </p>
                                </div>
                            </div>
                            <div class="text-[#666] text-sm md:text-lg">
                                <p>
                                    {$vo.content|raw}
                                </p>
                            </div>
                        </li>
                        {/volist}
                    </ul>
                </div>
                <div class="l myscroll-down h-[25rem] overflow-hidden hidden md:block md:h-[62.5rem]">
                    <ul class="flex flex-col gap-y-3 md:gap-y-5">
                        {volist name="all_post[1]" id="vo"}
                        <li class="border border-[#dae9ff] rounded-sm px-5 py-5 flex flex-col gap-y-3 bg-white md:p-10 md:min-h-[28.125rem] overflow-hidden md:rounded-xl">
                            <div class="flex gap-x-3 items-center md:mb-5">
                                <img src="{$vo.avatar ?? '__IMG__/user-1.jpg'}" alt="{$vo.user.first_name}"
                                    class="w-10 h-10 rounded-lg md:w-20 md:h-20 border border-[#dae9ff]">
                                <div class="md:ml-2.5">
                                    <strong class="text-[#1f1f1f] md:text-xl">{$vo.user.first_name} {$vo.user.last_name}</strong>
                                    <p class="text-[#999] text-sm md:text-base">
                                        {$vo.user.role_name}
                                    </p>
                                </div>
                            </div>
                            <div class="text-[#666] text-sm md:text-lg">
                                <p>
                                    {$vo.content|raw}
                                </p>
                            </div>
                        </li>
                        {/volist}
                    </ul>
                </div>
            </div>
            <div class="bg-[#ffffff] relative rounded-md border border-[#dae9ff] md:hidden">
                <a href="" class="flex justify-center items-center gap-2 text-[#155797] text-sm py-3">
                    <span> Explore the community </span>
                    <img src="__IMG__/icons/changjiantou-zuoshang_b.png" alt="more"
                        class="w-3 absolute right-7 :hover:right-6">
                </a>
            </div>
        </section>

        <!--
        <section class="bg-[#f5f9ff] py-6 md:py-44">
            <div class="w-11/12 mx-auto md:w-10/12 md:flex md:justify-between md:gap-40">
                <div class="bg-[url(__IMG__/img_01.jpg)] bg-no-repeat bg-cover h-[18.75rem] flex flex-col px-8 justify-around mb-5 md:w-[33.75rem] md:h-[42.5rem] md:rounded-tl-4xl md:px-16">
                    <header class="text-white">
                        <h2 class="text-xl Roboto_Bold mb-2.5 md:text-5xl md:mb-8">OpenDEL FAQ</h2>
                        <p class="text-sm md:text-xl md:leading-10">
                            Here you can find all the answers you don't understand. Welcome to ask new questions and we
                            will help you with them.
                        </p>
                    </header>
                    <div class="bg-[#ffffff] relative rounded-md border border-[#dae9ff] w-full md:w-[16.25rem]">
                        <a href="/faq"
                            class="flex justify-center items-center gap-2 text-[#155797] text-sm py-3 md:text-xl md:h-[3.75rem]">
                            <span> Learn More </span>
                            <img src="__IMG__/icons/changjiantou-zuoshang_b.png" alt="more"
                                class="w-3 absolute right-7 :hover:right-6">
                        </a>
                    </div>
                </div>
                <div class="flex flex-col md:flex-1" data-aos="fade-left">
                    <ul role="list" class="flex flex-col gap-y-2 md:gap-y-[1.125rem]">
                        {volist name="faq" id="vo"}
                        <li class="bg-white border border-[#dae9ff] rounded-md group">
                            <a href="/faq/{$vo.seo_url}"
                                class="flex justify-between items-center px-4 py-3 gap-x-3 h-[3.125rem] md:h-24 md:px-12">
                                <div
                                    class="text-sm line-clamp-1 flex-1 md:text-xl group-hover:opacity-45 transition-all">
                                    {$vo.question}
                                </div>
                                <img src="__IMG__/icons/add.png" alt="more"
                                    class="w-3 h-3 text-[#333] md:w-auto  md:h-auto" />
                            </a>
                        </li>
                        {/volist}
                    </ul>
                </div>
            </div>
        </section>
        -->

        <section class="w-11/12 mx-auto pb-6 md:w-10/12 relative md:pb-44" data-aos="fade-up">
            <header class="pt-8 pb-6 md:pt-20 md:pb-14">
                <h2 class="text-2xl Roboto_Bold mb-2.5 text-[#111] md:text-5xl">
                    OpenDEL™ Resources
                </h2>
            </header>

            <div class="flex flex-col gap-y-6 mb-6 md:flex-row md:gap-x-6">
                {volist name="resource" id="vo" key="k"}
                <div class="bg-white p-6 border border-[#e0eaff] rounded-lg md:flex-1 md:rounded-xl md:p-10" style="box-shadow: 0 0 1.25rem -0.125rem rgba(196,220,255,0.6);">
                    <div class="flex items-start justify-between mb-2">
                        <figure>
                            <div class="w-10 h-10 rounded-full bg-[rgba(21,87,151,0.1)] flex items-center justify-center mb-2 md:w-20 md:h-20 md:mb-6">
                                <img src="{$vo.icon}" alt="" class="w-4 md:w-[1.8rem]">
                            </div>
                            <figcaption class="text-[#111111] md:text-3xl Roboto_Bold">{$vo.name}</figcaption>
                        </figure>
                        <div class="w-10 h-10 rounded-full bg-[#fff] border border-[#dae9ff] md:w-20 md:h-20">
                            <a href="/resources/?tab={$k}" class="h-full w-full flex items-center justify-center">
                                <img src="__IMG__/icons/changjiantou-zuoshang_b.png" alt="" class="w-4 h-4 md:w-[1.4rem] md:h-auto">
                            </a>
                        </div>
                    </div>
                    <ul role="list">
                        {volist name="vo.files" id="v"}
                        <li class="border-[#e0eaff] border-b py-4 flex items-center justify-between gap-x-2 md:py-6">
                            <div class="flex items-center flex-1">
                                <span class="uppercase text-xs border border-[#155797] text-[#155797] rounded-4xl px-2 py-0.5 mr-2 scale-75 md:scale-100">
                                    {$v.file_type}
                                </span>
                                <h3 class="text-[#666] text-sm md:text-xl md:ml-2.5">
                                    <a href="{$v.file}" download="{$v.file_name}" class="line-clamp-1"> {$v.name}</a>
                                </h3>
                            </div>
                            <div class="w-8 h-8 md:w-10 md:h-10 rounded-full bg-[rgba(21,87,151,0.1)] border border-[#e0eaff] flex items-center justify-center scale-75">
                                <a href="{$v.file}" download="{$v.file_name}">
                                    <img src="__IMG__/icons/xiazai1.png" alt="" class="w-3.5 md:w-[1.1rem]" />
                                </a>
                            </div>
                        </li>
                        {/volist}
                    </ul>
                </div>
                {/volist}
            </div>
            <div class="bg-[#ffffff] relative mt-4 rounded-md border border-[#dae9ff] md:absolute md:right-0 md:top-14 md:w-[16.25rem] group ">
                <a href="/resources/" class="flex justify-center items-center gap-2 text-[#155797] text-sm py-3 md:text-xl md:h-[3.75rem] md:justify-evenly">
                    <span> Learn More </span>
                    <img src="__IMG__/icons/changjiantou-zuoshang_b.png" alt="more" class="w-3 absolute right-7 transition-all md:w-[1rem] group-hover:right-2 md:relative md:right-0" />
                </a>
            </div>
        </section>

        <section class="bg-[url(__IMG__/backgrounds/bg_01.jpg)] bg-no-repeat bg-cover bg-center py-8 md:h-[62.5rem] md:bg-[left_top]" data-aos="fade-up">
            <div class="md:flex md:justify-end md:items-center md:w-10/12 md:mx-auto md:h-full">
                <form action=""
                    class="w-11/12 mx-auto md:mx-0 py-6 px-4 flex flex-col gap-3 bg-white rounded-md md:w-[51.25rem] md:h-[51.625rem] md:p-14"
                    id="feedbackForm">
                    <h2 class="text-[#111111] text-2xl Roboto_Bold mb-2 md:text-center md:text-5xl md:mb-12">
                        Messages and Feedback
                    </h2>
                    <div class="flex flex-col gap-3 md:grid md:grid-cols-2 md:gap-5">
                        <div class="input-item group">
                            <input type="text" name="name" class="w-full px-4 py-2 border border-[#dae9ff] bg-white focus:border-[#dae9ff] rounded-md text-sm
                            md:h-[5rem] md:text-xl md:rounded-lg" required placeholder="Name*"
                                oninvalid="setCustomValidity('This field is required.');"
                                oninput="setCustomValidity('') ">
                        </div>
                        <div class="input-item">
                            <input type="tel" name="phone" class="w-full px-4 py-2 border border-[#dae9ff] bg-white focus:border-[#dae9ff] rounded-md text-sm
                            md:h-[5rem] md:text-xl md:rounded-lg" required placeholder="Phone*"
                                oninvalid="setCustomValidity('This field is required.');"
                                oninput="setCustomValidity('') ">
                        </div>
                        <div class="input-item">
                            <input type="Email" name="email" class="w-full px-4 py-2 border border-[#dae9ff] bg-white focus:border-[#dae9ff] rounded-md text-sm
                            md:h-[5rem] md:text-xl md:rounded-lg" required placeholder="Email*"
                                oninvalid="setCustomValidity('This field is required.');"
                                oninput="setCustomValidity('') ">
                        </div>
                        <div class="input-item">
                            <input type="text" name="company" class="w-full px-4 py-2 border border-[#dae9ff] bg-white focus:border-[#dae9ff] rounded-md text-sm
                            md:h-[5rem] md:text-xl md:rounded-lg" required placeholder="Company*"
                                oninvalid="setCustomValidity('This field is required.');"
                                oninput="setCustomValidity('') ">
                        </div>
                        <div class="input-item md:col-span-2">
                            <textarea name="feedback" class="w-full px-4 py-2 h-[6.25rem] border border-[#dae9ff] bg-white focus:border-[#dae9ff] rounded-md text-sm
                            md:h-[12.5rem] md:text-xl md:rounded-lg resize-none "
                                placeholder="Feedback message..."></textarea>
                        </div>
                    </div>

                    <div class="input-agree mb-4">
                        <p class="text-[#999] text-xs mb-1 md:text-base">
                            By submitting your information, you acknowledge having received, read and understood our
                            Privacy
                            Notice as made available above.
                        </p>
                        <label for="agree">
                            <input type="checkbox" name="agree" id="agree" class="mr-2 w-5 h-5 align-middle">
                            <span class="text-[#111] text-sm md:text-base">I agree</span>
                            </input>
                        </label>
                    </div>
                    <button type="submit" id="submitBtn"
                        class="flex justify-center items-center gap-2 text-white text-sm py-3 bg-[#155290] rounded-md relative md:h-20 cursor-pointer">
                        <span class="text-white text-sm md:text-3xl">Send</span>
                        <img src="__IMG__/icons/changjiantou-zuoshang.png" alt="more"
                            class="w-3 absolute right-7 md:w-auto md:right-10">
                    </button>
                </form>
            </div>
        </section>
    </main>

    {include file="public:footer"}

</body>

{include file="public:foot"}

<script>
$(document).ready(function() {
    $('#feedbackForm').on('submit', function(e) {
        //阻止表单提交
        e.preventDefault();

        // 检查是否同意条款
        if (!$('#agree').is(':checked')) {
            layer.msg('Please check the "I agree" clause first and then submit', { icon: 2 });
            return false;
        }

        // 禁用提交按钮
        var $submitBtn = $('#submitBtn');
        $submitBtn.prop('disabled', true).addClass('opacity-50 cursor-not-allowed');

        // 发送AJAX请求
        $.ajax({
            url: '/submit-feedback',
            type: 'POST',
            data: $(this).serialize(),
            dataType: 'json',
            success: function(data) {
                if (data.code === 1) {
                    //提交成功
                    layer.msg(data.msg, { icon: 1, time: 2000 }, () => {
                        $('#feedbackForm')[0].reset(); // 重置表单
                    });
                } else {
                    //提交失败
                    layer.msg('Error: ' + data.msg, { icon: 2 });
                }
            },
            error: function(xhr, status, error) {
                layer.msg('An error occurred: ' + error, { icon: 2 });
            },
            complete: function() {
                // 无论成功失败，都重新启用按钮
                $submitBtn.prop('disabled', false).removeClass('opacity-50 cursor-not-allowed');
            }
        });
    });
});
</script>

<script>
    var bannerSwiper = new Swiper(".swiper-banner", {
            autoHeight: true,
            spaceBetween: 20,
            effect: "fade",
            autoplay: {
                delay: 3000,
                disableOnInteraction: false,
            },
            loop: true,

            pagination: {
                el: ".swiper-banner-pagination",
                dynamicBullets: true
            },
            navigation: {
                nextEl: ".swiper-banner-next",
                prevEl: ".swiper-banner-prev",
            },
        });
        let numberPagination = document.querySelector(".number-pagination");
        setupNumberPagination(bannerSwiper, numberPagination);
        bannerSwiper.init();


        var swiperService = new Swiper(".swiper-service", {
            slidesPerView: 1,
            spaceBetween: 20,
            effect: "fade",
            navigation: {
                nextEl: ".swiper-service-next",
                prevEl: ".swiper-service-prev",
            },
            thumbs: { // 缩略图
                swiper: {
                    el: '.swiper-service-thumb',
                    slidesPerView: 5,
                },
                slideThumbActiveClass: 'slide-thumb-active',
            },
            autoHeight: true,
            on: {
                slideChangeTransitionStart: function () {
                    // 获取当前slide中的所有数字元素
                    var activeSlide = this.slides[this.activeIndex];
                    if (activeSlide) {
                        var numberEls = activeSlide.querySelectorAll('.hollow-number');
                        numberEls.forEach(function (el) {
                            // 移除并重新添加动画类
                            el.classList.remove('animate__fadeInRight');
                            void el.offsetWidth; // 触发重绘
                            el.classList.add('animate__fadeInRight');
                        });
                    }
                }
            }
        });
        let servicePagination = document.querySelector(".service-pagination");
        setupNumberPagination(swiperService, servicePagination);
        bannerSwiper.init();



    // 向上无缝滚动
    var upScrollBox = document.querySelector('.myscroll');
    if (upScrollBox) seamlessScroll(upScrollBox, { direction: "up" });

    // 向下无缝滚动（假设有另一个容器类名为myscroll-down）
    var downScrollBox = document.querySelector('.myscroll-down');
    if (downScrollBox) seamlessScroll(downScrollBox, { direction: "down" });
</script>

</html>