<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <title>Message - HitGen OpenDEL™</title>
    <link rel="shortcut icon" href="/favicon.ico" />
    <link href="__CSS__/vendors/aos.css" rel="stylesheet">
    <link href="__CSS__/style.css" rel="stylesheet" />
</head>

<body>
    <section class="bg-[url(__IMG__/backgrounds/m_bj.jpg)] bg-no-repeat pb-10 bg-size-[100%] md:bg-[url(__IMG__/backgrounds/pc_bj.jpg)] md:bg-size-[100%] md:pb-20">

        {include file="public:header"}

        <div class="w-11/12 mx-auto md:w-10/12">
            <div class="mt-5 Navigation mb-3.5 md:mb-14">
                <ul class="text-[#999] flex items-center text-md md:text-xl">
                    <li>
                        <a href="/" class="text-[#155797]">
                            Home
                        </a>
                    </li>
                    <li class="max-w-[12.5rem] line-clamp-1 md:max-w-2xs">
                        Personal Center
                    </li>
                </ul>
            </div>
            <!-- data-aos="fade-up" -->
            <div class="md:flex md:flex-row md:gap-x-4">

                {include file="user:left"}

                <div class="bg-white border border-[var(--border-color)] rounded-lg shadow-2xs
                md:w-2/3 md:rounded-2xl
                ">
                    <div class="w-full overflow-x-auto">
                        <div class="text-sm md:text-[1.5625rem] md:px-[2.5rem] md:pt-4 md:flex Roboto_Bold md:text-2xl md:gap-x-[4.375rem] user-tab-link flex flex-nowrap min-w-max">
                            <a href="/user/post" class="user-tab-item relative">
                                My post
                            </a>
                            <a href="/user/comment" class="user-tab-item relative active">
                                Comment/Reply
                                {if $reply_count>0}
                                <span class="absolute bg-[#ff1919] text-white rounded-3xl px-1 top-1 md:text-base md:min-w-[1.5rem] text-center">{$reply_count}</span>
                                {/if}
                            </a>
                            <a href="/user/private" class="user-tab-item relative">
                                Private message
                            </a>
                        </div>
                    </div>

                    <div class="user-profile">
                        <div class="profile-item">
                            <div class="py-4 md:p-[3.125rem] md:pt-[1.875rem]">
                                <div class="message-list">
                                    <ul role="list">
                                        {volist name="reply" id="vo"}
                                        <li>
                                            <div class="message-img">
                                                <img src="{$vo.avatar ?? '__IMG__/user-1.jpg'}" alt="" class="object-cover rounded-full w-full h-full">
                                            </div>
                                            <div class="message-user flex flex-col">
                                                <div class="message-info mb-2">
                                                    <strong class="text-[#999] text-base md:text-xl md:pr-7">
                                                        {$vo.fist_name} {$vo.last_name}
                                                    </strong>
                                                    <span class="text-[#999] text-sm md:text-base">
                                                        {:date('F j, Y, g:i A', strtotime($vo.create_time))}
                                                    </span>
                                                </div>
                                                <div class="message-content text-wrap text-sm md:text-xl">
                                                    <p>
                                                        {$vo.content|strip_tags}
                                                        <a href="/iCommunity/{$vo.type==0?'post':'news'}/{$vo.post_id}" rel="noopener noreferrer">{$Request.domain}/iCommunity/{$vo.type==0?'post':"news"}/{$vo.post_id}</a>
                                                    </p>
                                                </div>
                                            </div>
                                        </li>
                                        {/volist}
                                    </ul>
                                    <div class="text-sm p-3 md:py-[40px] md:px-[57px] md:text-xl">
                                        <button type="button"
                                            class="show-more-activity-btn rounded-md bg-[#155797] text-white py-3.5 w-full cursor-pointer md:h-[80px]">
                                            SHOW MORE ACTIVITY
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>

        </div>
    </section>

    {include file="public:footer"}

    {include file="public:foot"}

    <script>
        //显示更多
        setupShowMoreActivity(
            '.message-list',   // 外层容器
            'ul[role="list"]',            // ul选择器
            '.show-more-activity-btn',                     // 按钮选择器
            4                             // 默认显示4个
        );
    </script>

</body>

</html>