<div class="flex flex-col gap-5 mb-5 md:w-1/3">
    <div class="border border-[var(--border-color)] bg-white rounded-lg shadow-2xs flex items-center flex-col py-6 md:py-[2.75rem] md:rounded-2xl">
        <div class="w-[4.375rem] h-[4.375rem] rounded-full mb-4
        md:w-[8.75rem] md:h-[8.75rem] md:mb-[2.5rem]
        ">
            <img src="{$user.avatar ?? '__IMG__/user-1.jpg'}" alt="user" class="w-full h-full rounded-full object-cover" />
        </div>
        <div class="Roboto_Bold text-xl mb-6 md:text-[1.875rem]">
            {$user.first_name} {$user.last_name}
        </div>
        <div class="grid grid-cols-2 gap-5 text-center">
            <div class="border-r border-[#dfe7ff] relative">
                <span class="Roboto_Bold text-2xl md:text-4xl">
                    {$coupon_count}
                </span>
                <p class="text-sm text-[#999] md:text-xl md:mt-2.5">
                    Voucher
                </p>
                <a href="/user/coupon" class="absolute top-0 left-0 w-full h-full"></a>
            </div>
            <div class="relative">
                <span class="Roboto_Bold text-2xl md:text-4xl">
                    {$user.points ?? 0}
                </span>
                <p class="text-sm text-[#999] md:text-xl md:mt-2.5">
                    HitGen Points
                </p>
                <a href="/user/points" class="absolute top-0 left-0 w-full h-full"></a>
            </div>
        </div>
    </div>
    <div
        class="w-full overflow-x-auto mb-3 bg-white
    md:border md:border-[var(--border-color)] md:rounded-2xl md:p-3.5 md:overflow-hidden md:m-0 md:px-[1.25rem]">
        <div class="flex flex-nowrap min-w-max gap-2 text-abse
        md:flex-col md:text-[1.5625rem] md:min-w-auto navigation-bar
        ">
            <div class="border border-[var(--border-color)] relative py-3 px-4 rounded-lg {if request()->action()=='index'}text-[#f08411]{/if}
                md:border-0 md:border-b md:border-[#dae9ff] md:rounded-none md:py-6 navigation-item md:bg-[url(__IMG__/icons/xiala.png)] md:bg-no-repeat md:bg-[95%_center]
            ">
                <a href="/user">
                    <span class="line-clamp-1">User Profile Management</span>
                </a>
            </div>
            <div
                class="border border-[var(--border-color)] relative py-3 px-4 rounded-lg {if request()->action()=='order'}text-[#f08411]{/if}
            md:border-0 md:border-b md:border-[#dae9ff] md:rounded-none md:py-6 navigation-item md:bg-[url(__IMG__/icons/xiala.png)] md:bg-no-repeat md:bg-[95%_center]">
                <a href="/user/order">
                    <span class="line-clamp-1">Order Management</span>
                </a>
            </div>
            <div class="border border-[var(--border-color)] relative py-3 px-4 rounded-lg {if request()->action()=='message'}text-[#f08411]{/if}
            md:border-0 md:border-b md:border-[#dae9ff] md:rounded-none md:py-6 navigation-item md:bg-[url(__IMG__/icons/xiala.png)] md:bg-no-repeat md:bg-[95%_center]">
                <a href="/user/message">
                    <span>Tooltip</span>

                    {if count($user_message)>0}
                    <div class="absolute top-0 -right-[.525rem] text-white text-center
                    md:top-1/2 md:-translate-y-1/2 md:right-[4.375rem]
                    ">
                        <div class="min-w-[1rem] min-h-[1rem] text-[.75rem] bg-[#ff0000] rounded-full px-1 md:px-2
                            md:min-w-[1.875rem] md:min-h-[1.875rem] md:text-xl">
                            <span>{$user_message|count}</span>
                        </div>
                    </div>
                    {/if}
                </a>
            </div>
            <div class="border border-[var(--border-color)] relative py-3 px-4 rounded-lg {if request()->action()=='post' || request()->action()=='comment' || request()->action()=='private_message'}text-[#f08411]{/if}
            md:border-0 md:rounded-none md:py-6 navigation-item md:bg-[url(__IMG__/icons/xiala.png)] md:bg-no-repeat md:bg-[95%_center]">
                <a href="/user/post">
                    <span>iCommunity</span>

                    {if $private_count>0}
                    <div class="absolute top-0 -right-[.525rem] text-white text-center
                    md:top-1/2 md:-translate-y-1/2 md:right-[4.375rem]
                    ">
                        <div class="min-w-[1rem] min-h-[1rem] text-[.75rem] bg-[#ff0000] rounded-full px-1 md:px-2
                            md:min-w-[1.875rem] md:min-h-[1.875rem] md:text-xl">
                            <span>{$private_count}</span>
                        </div>
                    </div>
                    {/if}
                </a>
            </div>
        </div>
    </div>
</div>