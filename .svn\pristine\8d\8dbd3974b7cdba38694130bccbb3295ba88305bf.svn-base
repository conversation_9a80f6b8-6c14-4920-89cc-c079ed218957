<?php

declare(strict_types=1);

namespace app\admin\controller;

use app\admin\controller\Common;
use think\facade\Db;

class Info extends Common
{
    public function index()
    {
        $List = Db::name('Info')
            ->order("id desc")
            ->paginate([
                'query'     =>  request()->param(), //url额外参数
                'list_rows' => 20, //每页数量
            ])
            ->each(function ($item) {
                $item['user'] = Db::name("User")->where("id", $item['user_id'])->field("id, email, phone")->find();

                return $item;
            });

        return view("", [
            "List" => $List
        ]);
    }

    public function reply_info()
    {
        if ($this->request->isPost()) {
            $data = input('post.');

            if(!$data['reply_content']){
                $this->error("必填项未填！");
            }

            $data['is_read'] = 0;  //未读
            $data['reply_time'] = date("Y-m-d H:i:s");
            $s = Db::name("Info")->strict(false)->save($data);
            if ($s) {
                $this->success('回复成功！');
            } else {
                $this->error("回复失败，请重试！");
            }
        } else {
            $id = input('id');
            $getone = Db::name("Info")->where("id", $id)->find();

            $user = Db::name("User")->where("id", $getone['user_id'])->find();

            return view("", [
                "getone" => $getone,
                "user" => $user
            ]);
        }
    }

    public function system()
    {
        $List = Db::name('Info_system')
            ->order("id desc")
            ->paginate([
                'query'     =>  request()->param(), //url额外参数
                'list_rows' => 20, //每页数量
            ])
            ->each(function ($item) {
                $item['code'] = Db::name("Info_template")->where("id", $item['template_id'])->value('code');

                $item['phone'] = Db::name("User")->where("id", $item['user_id'])->value('phone');

                return $item;
            });

        return view("", [
            "List" => $List
        ]);
    }
    public function add_system()
    {
        if ($this->request->isPost()) {
            $data = input('post.');

            if(!$data['main_id'] || empty($data['user_ids'])){
                $this->error("必填项未填！");
            }

            $content = Db::name('Info_template')->where("id", $data['main_id'])->value("content");

            foreach($data['user_ids'] as $val){
                $info_data = [
                    "user_id" => $val,
                    "sender_id" => session("adminId"),
                    "content" => $content,
                    "main_id" => $data['main_id'],
                    "type" => 1,
                    "create_time" => date("Y-m-d H:i:s")
                ];
                $s = Db::name("User_message")->insertGetId($info_data);
            }

            if ($s) {
                $this->success('发送成功！');
            } else {
                $this->error("发送失败，请重试！");
            }
        } else {
            $id = input('id');

            $users = Db::name("User")->where("status", 1)->column("id, email, phone");

            return view("", [
                "id" => $id,
                "users" => $users
            ]);
        }
    }

    public function template()
    {
        $List = Db::name('Info_template')
            ->order("id desc")
            ->paginate([
                'query'     =>  request()->param(), //url额外参数
                'list_rows' => 20, //每页数量
            ]);

        return view("", [
            "List" => $List
        ]);
    }

    public function add_template()
    {
        if ($this->request->isPost()) {
            $data = input('post.');

            if(!$data['code'] || !$data['title']){
                $this->error("必填项未填！");
            }

            $data['create_time'] = date("Y-m-d H:i:s");
            $s = Db::name("Info_template")->insertGetId($data);
            if ($s) {
                $this->success('添加成功！');
            } else {
                $this->error("添加失败，请重试！");
            }
        } else {
            return view();
        }
    }

    public function edit_template()
    {
        if ($this->request->isPost()) {
            $data = input('post.');

            if(!$data['code'] || !$data['title']){
                $this->error("必填项未填！");
            }

            $data['update_time'] = date("Y-m-d H:i:s");
            $s = Db::name("Info_template")->strict(false)->save($data);
            if ($s) {
                $this->success('修改成功！');
            } else {
                $this->error("修改失败，请重试！");
            }
        } else {
            $id = input('id');
            $getone = Db::name("Info_template")->where("id", $id)->find();

            return view("", [
                "getone" => $getone,
            ]);
        }
    }

    public function del_template()
    {
        $id = input('id');
        $s = Db::name("Info_template")->where("id", $id)->delete();
        if ($s) {
            $this->success("删除成功！");
        } else {
            $this->error("删除失败！");
        }
    }


    public function feedback()
    {
        $List = Db::name('Feedback')
            ->order("id desc")
            ->paginate([
                'query'     =>  request()->param(), //url额外参数
                'list_rows' => 20, //每页数量
            ]);

        return view("", [
            "List" => $List
        ]);
    }
}
