/*
SQLyog Ultimate v12.14 (64 bit)
MySQL - 5.6.51-log : Database - opendelclub
*********************************************************************
*/


/*!40101 SET NAMES utf8 */;

/*!40101 SET SQL_MODE=''*/;

/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;
CREATE DATABASE /*!32312 IF NOT EXISTS*/`opendelclub` /*!40100 DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci */;

USE `opendelclub`;

/*Table structure for table `opd_backend_menus` */

DROP TABLE IF EXISTS `opd_backend_menus`;

CREATE TABLE `opd_backend_menus` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `parent_id` int(11) DEFAULT '0' COMMENT '父菜单ID',
  `title` varchar(50) NOT NULL COMMENT '菜单名称',
  `icon` varchar(50) DEFAULT NULL COMMENT '菜单图标',
  `url` varchar(255) DEFAULT NULL COMMENT '菜单URL',
  `sort` int(11) DEFAULT '0' COMMENT '排序',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态(0:禁用,1:正常)',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_parent_id` (`parent_id`)
) ENGINE=InnoDB AUTO_INCREMENT=52 DEFAULT CHARSET=utf8mb4 COMMENT='后台菜单表';

/*Data for the table `opd_backend_menus` */

insert  into `opd_backend_menus`(`id`,`parent_id`,`title`,`icon`,`url`,`sort`,`status`,`create_time`,`update_time`) values 

(1,37,'首页管理','0',NULL,2,1,'2025-06-09 13:48:03','2025-07-09 14:40:55'),

(2,1,'轮播图列表',NULL,'Banner/index',1,1,'2025-06-09 13:48:17','2025-07-09 14:40:55'),

(3,37,'新闻管理','3',NULL,3,1,'2025-06-09 13:49:28','2025-07-09 14:40:55'),

(4,3,'新闻列表',NULL,'News/index',1,1,'2025-06-09 13:49:40','2025-07-09 14:40:55'),

(5,3,'新闻分类',NULL,'News/category',2,1,'2025-06-09 13:50:11','2025-07-09 14:40:55'),

(6,37,'用户管理','10','User/index',0,0,'2025-06-09 13:50:30','2025-06-09 14:54:04'),

(7,37,'产品管理','7',NULL,4,1,'2025-06-09 13:50:58','2025-07-09 14:40:55'),

(8,7,'产品列表',NULL,'Product/index',1,1,'2025-06-09 13:51:07','2025-07-09 14:40:55'),

(9,7,'服务列表',NULL,'Product/service',2,1,'2025-06-09 13:51:24','2025-07-09 14:40:55'),

(10,37,'评价管理','3',NULL,5,1,'2025-06-09 13:51:55','2025-07-09 14:40:55'),

(11,10,'评价列表',NULL,'Community/index',1,1,'2025-06-09 13:52:05','2025-07-09 14:40:55'),

(12,37,'资源管理','7',NULL,6,1,'2025-06-09 13:52:30','2025-07-09 14:40:55'),

(13,12,'资源列表',NULL,'Resource/index',1,1,'2025-06-09 13:52:43','2025-07-09 14:40:55'),

(14,12,'资源分类',NULL,'Resource/category',2,1,'2025-06-09 13:52:44','2025-07-09 14:40:55'),

(15,37,'页面管理','1','Pages/index',7,1,'2025-06-09 13:53:22','2025-07-09 14:40:55'),

(16,37,'试用券管理','3',NULL,8,1,'2025-06-09 13:53:44','2025-07-16 11:30:47'),

(17,16,'普通试用券',NULL,'Coupon/index',1,1,'2025-06-09 13:53:49','2025-07-09 14:40:55'),

(18,16,'赠送列表',NULL,'Coupon/gift',2,1,'2025-06-09 13:53:54','2025-07-09 14:40:55'),

(19,37,'订单管理','7',NULL,9,1,'2025-06-09 13:54:25','2025-07-09 14:40:55'),

(20,19,'订单列表',NULL,'Order/index?order_status=0',1,1,'2025-06-09 13:54:28','2025-07-22 14:33:26'),

(21,19,'咨询列表',NULL,'Order/quote',2,1,'2025-06-09 13:54:29','2025-07-09 14:40:55'),

(22,37,'邮件管理','5',NULL,11,1,'2025-06-09 13:54:58','2025-07-09 14:40:55'),

(23,22,'邮件列表',NULL,'Email/index',0,0,'2025-06-09 13:55:08','2025-06-13 17:24:16'),

(24,22,'发件账号',NULL,'Email/account',1,1,'2025-06-09 13:55:12','2025-07-09 14:40:55'),

(25,22,'模板管理',NULL,'Email/template',2,1,'2025-06-09 13:55:13','2025-07-09 14:40:55'),

(26,37,'留言咨询','11',NULL,12,1,'2025-06-09 13:56:00','2025-07-16 16:34:57'),

(27,26,'用户信息',NULL,'Info/index',0,0,'2025-06-09 13:56:13','2025-06-16 15:50:44'),

(28,26,'系统信息',NULL,'Info/system',0,0,'2025-06-09 13:56:19','2025-06-16 16:13:00'),

(29,26,'模板管理',NULL,'Info/template',1,0,'2025-06-09 13:56:23','2025-07-16 17:23:23'),

(30,26,'咨询列表',NULL,'Info/feedback',2,1,'2025-06-09 13:56:24','2025-07-16 16:35:51'),

(31,37,'FAQ管理',NULL,NULL,13,1,'2025-06-09 13:57:17','2025-07-09 14:40:55'),

(32,31,'FAQ列表',NULL,'Faq/index',1,1,'2025-06-09 13:57:21','2025-07-09 14:40:55'),

(33,31,'FAQ分类',NULL,'Faq/category',2,1,'2025-06-09 13:57:25','2025-07-09 14:40:55'),

(34,31,'用户问题列表',NULL,'Faq/ask',3,1,'2025-06-09 13:57:26','2025-07-09 14:40:55'),

(35,37,'下载记录','7','Download/index',14,1,'2025-06-09 13:57:58','2025-07-09 14:40:55'),

(36,37,'全局配置','1','Basic/index',15,1,'2025-06-09 13:59:07','2025-07-09 14:40:55'),

(37,0,'内容管理',NULL,NULL,2,1,'2025-06-09 14:00:42','2025-07-09 14:40:55'),

(38,0,'系统管理','xitong',NULL,1,1,'2025-06-09 14:01:28','2025-07-09 14:40:55'),

(39,38,'用户管理','12',NULL,1,1,'2025-06-09 14:01:49','2025-07-09 14:40:55'),

(40,39,'用户列表',NULL,'User/index',1,1,'2025-06-09 14:02:57','2025-07-09 14:40:55'),

(41,39,'角色管理',NULL,'User/roles',2,1,'2025-06-09 15:04:20','2025-07-09 14:40:55'),

(42,16,'灰色试用券',NULL,'Coupon/index?coupon_type=1',3,1,'2025-06-12 17:07:52','2025-07-09 14:40:55'),

(43,37,'联系我们',NULL,'Contact/index',16,1,'2025-06-20 10:41:26','2025-07-09 14:40:55'),

(44,37,'论坛系统','11',NULL,1,1,'2025-07-03 11:08:16','2025-07-09 14:40:55'),

(45,44,'话题分类',NULL,'Forum/topic',3,1,'2025-07-03 11:10:38','2025-07-09 14:40:55'),

(46,44,'标签分类',NULL,'Forum/tags',4,1,'2025-07-03 11:10:58','2025-07-09 14:40:55'),

(47,44,'帖子列表',NULL,'Forum/index',1,1,'2025-07-03 11:11:24','2025-07-09 14:40:55'),

(48,44,'评论列表',NULL,'Forum/reply',2,1,'2025-07-03 14:33:16','2025-07-17 09:38:11'),

(49,16,'兑换记录',NULL,'Coupon/exchange',4,1,'2025-07-09 10:57:54','2025-07-09 14:40:55'),

(50,37,'积分记录','7','Points/index',10,1,'2025-07-09 11:13:05','2025-07-09 14:40:55'),

(51,44,'@列表',NULL,'Forum/mention',5,1,'2025-07-16 15:34:57','2025-07-16 15:35:43');

/*Table structure for table `opd_backend_permissions` */

DROP TABLE IF EXISTS `opd_backend_permissions`;

CREATE TABLE `opd_backend_permissions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL COMMENT '权限名称',
  `slug` varchar(50) NOT NULL COMMENT '权限标识',
  `description` varchar(255) DEFAULT NULL COMMENT '权限描述',
  `controller` varchar(100) DEFAULT NULL COMMENT '控制器',
  `action` varchar(100) DEFAULT NULL COMMENT '方法',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_slug` (`slug`),
  KEY `idx_controller_action` (`controller`,`action`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='权限表';

/*Data for the table `opd_backend_permissions` */

/*Table structure for table `opd_banner` */

DROP TABLE IF EXISTS `opd_banner`;

CREATE TABLE `opd_banner` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `image` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `smt_image` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `sort` int(2) DEFAULT '50',
  `create_time` datetime DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

/*Data for the table `opd_banner` */

insert  into `opd_banner`(`id`,`image`,`smt_image`,`sort`,`create_time`,`update_time`) values 

(1,'/storage/20250515/6161497d5fc4c69cf9e40b0e540cde57.jpg','/storage/20250515/eb7ce6a15df8d738682691cac36beb87.jpg',1,'2025-05-15 14:02:24',NULL),

(2,'/storage/20250515/6161497d5fc4c69cf9e40b0e540cde57.jpg','/storage/20250515/eb7ce6a15df8d738682691cac36beb87.jpg',2,'2025-05-15 14:02:24',NULL),

(3,'/storage/20250515/6161497d5fc4c69cf9e40b0e540cde57.jpg','/storage/20250515/eb7ce6a15df8d738682691cac36beb87.jpg',3,'2025-05-15 14:02:24',NULL);

/*Table structure for table `opd_basic` */

DROP TABLE IF EXISTS `opd_basic`;

CREATE TABLE `opd_basic` (
  `id` int(1) NOT NULL,
  `coupon_status` tinyint(1) DEFAULT '1' COMMENT '试用券 1已开启 0已关闭',
  `forum_status` tinyint(1) DEFAULT '1' COMMENT '论坛 1已开启 0已关闭',
  `letter_status` tinyint(1) DEFAULT '1' COMMENT '私信 1已开启 0已关闭',
  `content_test1` text COLLATE utf8mb4_unicode_ci,
  `content_test2` text COLLATE utf8mb4_unicode_ci,
  PRIMARY KEY (`id`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='基础配置';

/*Data for the table `opd_basic` */

insert  into `opd_basic`(`id`,`coupon_status`,`forum_status`,`letter_status`,`content_test1`,`content_test2`) values 

(1,1,1,1,'<p>{{user_name}}</p><p>&nbsp;</p><p>cdsacd <a class=\"mention\" href=\"/user/profile/1\" data-mention=\"@test\" data-user-id=\"1\">@test</a> <a class=\"mention\" href=\"/user/profile/1\" data-mention=\"@test\" data-user-id=\"1\">@test</a>&nbsp;</p><p><a class=\"mention\" href=\"/user/profile/3\" data-mention=\"@wang\" data-user-id=\"3\">@wang</a>&nbsp;</p><figure class=\"image image_resized\" style=\"width:40.13%;\"><img style=\"aspect-ratio:680/680;\" src=\"/editor/images/20250605/abba7aa9f27913f6128b45ce2d593a27.jpg\" width=\"680\" height=\"680\"></figure>','<h3>v发大V发</h3><p>eafgvfd</p><p>&nbsp;</p>');

/*Table structure for table `opd_contact` */

DROP TABLE IF EXISTS `opd_contact`;

CREATE TABLE `opd_contact` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `icon` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `title` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `content` text COLLATE utf8mb4_unicode_ci,
  `sort` int(2) DEFAULT '50',
  `create_time` datetime DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

/*Data for the table `opd_contact` */

insert  into `opd_contact`(`id`,`icon`,`title`,`content`,`sort`,`create_time`,`update_time`) values 

(1,'/storage/20250620/30b9bb67c207fd7b6c3d3ef050c39614.png','HitGen Official Webisite','<p><a class=\"underline text-lg text-[#155797] md:text-2xl\" href=\"\">DNA encoded library | HitGen</a></p>',1,'2025-06-20 10:49:55',NULL),

(2,'/storage/20250620/a2aa1f02980fa935763d1d00b1912fb3.png','Business Inquiry','<p><a class=\"underline text-lg text-[#155797] md:text-2xl\" href=\"mailto:<EMAIL>\"><EMAIL></a></p>',2,'2025-06-20 10:50:32','2025-06-20 10:50:41'),

(3,'/storage/20250620/a57bb16dbf0dafaa67df2042343612f2.png','OpenDEL-related Support','<p><a class=\"underline text-lg text-[#155797] md:text-2xl\" href=\"mailto:<EMAIL>\"><EMAIL></a></p>',3,'2025-06-20 10:51:45','2025-06-20 10:51:53');

/*Table structure for table `opd_country` */

DROP TABLE IF EXISTS `opd_country`;

CREATE TABLE `opd_country` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `country_code` char(2) NOT NULL COMMENT 'ISO 2字母国家代码',
  `country_code3` char(3) NOT NULL COMMENT 'ISO 3字母国家代码',
  `cn_name` varchar(50) NOT NULL COMMENT '中文国家名称',
  `en_name` varchar(50) NOT NULL COMMENT '英文国家名称',
  `phone_code` varchar(10) DEFAULT NULL COMMENT '国际电话区号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `country_code` (`country_code`),
  UNIQUE KEY `country_code3` (`country_code3`)
) ENGINE=InnoDB AUTO_INCREMENT=59 DEFAULT CHARSET=utf8mb4 COMMENT='全球主要国家数据表';

/*Data for the table `opd_country` */

insert  into `opd_country`(`id`,`country_code`,`country_code3`,`cn_name`,`en_name`,`phone_code`) values 

(1,'CN','CHN','中国','China','+86'),

(2,'US','USA','美国','United States','+1'),

(3,'JP','JPN','日本','Japan','+81'),

(4,'DE','DEU','德国','Germany','+49'),

(5,'FR','FRA','法国','France','+33'),

(6,'GB','GBR','英国','United Kingdom','+44'),

(7,'IT','ITA','意大利','Italy','+39'),

(8,'CA','CAN','加拿大','Canada','+1'),

(9,'AU','AUS','澳大利亚','Australia','+61'),

(10,'RU','RUS','俄罗斯','Russia','+7'),

(11,'KR','KOR','韩国','South Korea','+82'),

(12,'IN','IND','印度','India','+91'),

(13,'BR','BRA','巴西','Brazil','+55'),

(14,'MX','MEX','墨西哥','Mexico','+52'),

(15,'ID','IDN','印度尼西亚','Indonesia','+62'),

(16,'NL','NLD','荷兰','Netherlands','+31'),

(17,'SA','SAU','沙特阿拉伯','Saudi Arabia','+966'),

(18,'TR','TUR','土耳其','Turkey','+90'),

(19,'CH','CHE','瑞士','Switzerland','+41'),

(20,'SE','SWE','瑞典','Sweden','+46'),

(21,'ES','ESP','西班牙','Spain','+34'),

(22,'SG','SGP','新加坡','Singapore','+65'),

(23,'MY','MYS','马来西亚','Malaysia','+60'),

(24,'TH','THA','泰国','Thailand','+66'),

(25,'VN','VNM','越南','Vietnam','+84'),

(26,'PH','PHL','菲律宾','Philippines','+63'),

(27,'ZA','ZAF','南非','South Africa','+27'),

(28,'EG','EGY','埃及','Egypt','+20'),

(29,'NG','NGA','尼日利亚','Nigeria','+234'),

(30,'AR','ARG','阿根廷','Argentina','+54'),

(31,'CL','CHL','智利','Chile','+56'),

(32,'CO','COL','哥伦比亚','Colombia','+57'),

(33,'PE','PER','秘鲁','Peru','+51'),

(34,'VE','VEN','委内瑞拉','Venezuela','+58'),

(35,'PK','PAK','巴基斯坦','Pakistan','+92'),

(36,'BD','BGD','孟加拉国','Bangladesh','+880'),

(37,'IR','IRN','伊朗','Iran','+98'),

(38,'IQ','IRQ','伊拉克','Iraq','+964'),

(39,'IL','ISR','以色列','Israel','+972'),

(40,'AE','ARE','阿联酋','United Arab Emirates','+971'),

(41,'KW','KWT','科威特','Kuwait','+965'),

(42,'QA','QAT','卡塔尔','Qatar','+974'),

(43,'OM','OMN','阿曼','Oman','+968'),

(44,'NZ','NZL','新西兰','New Zealand','+64'),

(45,'FI','FIN','芬兰','Finland','+358'),

(46,'NO','NOR','挪威','Norway','+47'),

(47,'DK','DNK','丹麦','Denmark','+45'),

(48,'BE','BEL','比利时','Belgium','+32'),

(49,'AT','AUT','奥地利','Austria','+43'),

(50,'PL','POL','波兰','Poland','+48'),

(51,'PT','PRT','葡萄牙','Portugal','+351'),

(52,'IE','IRL','爱尔兰','Ireland','+353'),

(53,'GR','GRC','希腊','Greece','+30'),

(54,'CZ','CZE','捷克','Czech Republic','+420'),

(55,'HU','HUN','匈牙利','Hungary','+36'),

(56,'RO','ROU','罗马尼亚','Romania','+40'),

(57,'UA','UKR','乌克兰','Ukraine','+380'),

(58,'KZ','KAZ','哈萨克斯坦','Kazakhstan','+7');

/*Table structure for table `opd_coupon` */

DROP TABLE IF EXISTS `opd_coupon`;

CREATE TABLE `opd_coupon` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `description` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '说明',
  `product_ids` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '产品ids',
  `service_ids` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '服务ids',
  `scope_type` tinyint(1) DEFAULT '0' COMMENT '使用范围 0单独 1整体',
  `start_time` date DEFAULT NULL COMMENT '开始时间',
  `end_time` date DEFAULT NULL COMMENT '结束时间',
  `points` int(5) DEFAULT '0' COMMENT '兑换积分数',
  `coupon_type` tinyint(1) DEFAULT '0' COMMENT '0(普通优惠券)1(灰色优惠券)',
  `del_status` tinyint(1) DEFAULT '0' COMMENT '0未删除 1已删除',
  `create_time` datetime DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='试用券';

/*Data for the table `opd_coupon` */

insert  into `opd_coupon`(`id`,`name`,`description`,`product_ids`,`service_ids`,`scope_type`,`start_time`,`end_time`,`points`,`coupon_type`,`del_status`,`create_time`,`update_time`) values 

(1,'10','下游服务','[\"1\"]','[\"1\",\"2\"]',0,'2025-05-26','2025-08-30',NULL,0,0,'2025-05-26 11:45:59','2025-06-16 13:55:13'),

(2,'20','For Christmas use onlyFor','[\"1\",\"3\"]','[\"2\",\"4\"]',1,'2025-05-26','2025-07-31',NULL,0,0,'2025-05-26 11:46:34','2025-06-16 13:54:57'),

(3,'10','9折兑换券',NULL,NULL,0,NULL,NULL,300,1,0,'2025-06-13 09:38:01','2025-06-16 14:14:34'),

(4,'15','85折兑换券',NULL,NULL,0,NULL,NULL,500,1,0,'2025-06-13 09:38:31','2025-06-16 14:14:26'),

(5,'20','8折兑换券',NULL,NULL,0,NULL,NULL,800,1,0,'2025-06-13 09:38:45','2025-06-16 14:14:00');

/*Table structure for table `opd_download` */

DROP TABLE IF EXISTS `opd_download`;

CREATE TABLE `opd_download` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL COMMENT '下载用户 opd_user',
  `order_file_id` int(11) DEFAULT NULL COMMENT 'opd_order_file id',
  `type` tinyint(1) DEFAULT NULL COMMENT '0产品 1服务',
  `file` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `file_name` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `url` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `ip` varchar(30) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '下载ip',
  `download_time` datetime DEFAULT NULL COMMENT '下载时间',
  `product_name` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '产品/服务名称',
  `product_managers` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '产品经理',
  PRIMARY KEY (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=16 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='保密文件下载记录日志';

/*Data for the table `opd_download` */

insert  into `opd_download`(`id`,`user_id`,`order_file_id`,`type`,`file`,`file_name`,`url`,`ip`,`download_time`,`product_name`,`product_managers`) values 

(14,3,14,0,'','','http://www.01000.cn','127.0.0.1','2025-07-16 14:14:06','OpenDEL™ Kit',NULL),

(13,3,13,1,'','','www.01000889.cn','127.0.0.1','2025-07-16 14:28:57','OpenDEL™ Screening',NULL),

(11,3,11,0,'/storage/security/20250530/f67a84b3072dbb1e87b6df817d8e7410.docx','5.28个人中心修改意见.docx','','127.0.0.1','2025-07-16 14:44:45','OpenDEL™ Kit','Jevons Wang'),

(15,3,11,0,'/storage/security/20250530/f67a84b3072dbb1e87b6df817d8e7410.docx','5.28个人中心修改意见.docx','','127.0.0.1','2025-07-16 14:45:59','OpenDEL™ Kit','Jevons Wang');

/*Table structure for table `opd_email` */

DROP TABLE IF EXISTS `opd_email`;

CREATE TABLE `opd_email` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `template_id` int(11) NOT NULL COMMENT '邮件模版id opd_email_template',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `source_id` int(11) DEFAULT NULL COMMENT '来源ID',
  `content` text COLLATE utf8mb4_unicode_ci COMMENT '邮件内容',
  `create_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='邮件记录表';

/*Data for the table `opd_email` */

/*Table structure for table `opd_email_account` */

DROP TABLE IF EXISTS `opd_email_account`;

CREATE TABLE `opd_email_account` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `host` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '主机地址',
  `port` varchar(5) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '端口',
  `protocol` varchar(5) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '安全协议',
  `email` varchar(30) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '发件邮箱',
  `password` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '密码',
  `from_name` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '发件用户',
  `create_time` datetime DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='发件账号表';

/*Data for the table `opd_email_account` */

insert  into `opd_email_account`(`id`,`host`,`port`,`protocol`,`email`,`password`,`from_name`,`create_time`,`update_time`) values 

(1,'mail.hitgen.com','465','ssl','<EMAIL>','S7#M8Xw%fUx','<EMAIL>','2025-04-28 09:49:47',NULL);

/*Table structure for table `opd_email_template` */

DROP TABLE IF EXISTS `opd_email_template`;

CREATE TABLE `opd_email_template` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `account_id` int(11) NOT NULL COMMENT '发件账号id opd_email_account',
  `identifier` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '模板标识',
  `name` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '模板名称',
  `description` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '模板说明',
  `subject` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '邮件主题',
  `content` text COLLATE utf8mb4_unicode_ci COMMENT 'HTML内容',
  `variables` text COLLATE utf8mb4_unicode_ci COMMENT '可用变量说明',
  `status` tinyint(1) DEFAULT '1' COMMENT '1启用 0禁用',
  `create_time` datetime DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='邮件模板表';

/*Data for the table `opd_email_template` */

insert  into `opd_email_template`(`id`,`account_id`,`identifier`,`name`,`description`,`subject`,`content`,`variables`,`status`,`create_time`,`update_time`) values 

(1,1,'grant-coupon',NULL,'后台给用户发放优惠券的通知邮件','You have new coupons','<div style=\"border-bottom:1px solid #eee;padding:20px 0;text-align:center;\"><h1>Dear {{username}}, you have a new coupon waiting to be used</h1></div><div style=\"background-color:#fff9e6;border-radius:8px;border:1px dashed #ffcc00;margin:25px 0;padding:20px;text-align:center;\"><div style=\"color:#e67e22;font-size:24px;margin-bottom:10px;\"><strong>{{coupon_name}}</strong></div><div style=\"color:#888;font-size:14px;margin:10px 0;\">Validity period: {{start_time}} to {{end_time}}</div><p><a style=\"background-color:#e67e22;border-radius:4px;color:white !important;display:inline-block;margin:15px 0 0;padding:12px 30px;text-decoration:none;\" href=\"{{site_url}}\"><strong>immediate use</strong></a></p></div><p>instructions:</p><ul><li>This coupon can be automatically deducted at checkout</li><li>Each order is limited to one coupon</li><li>Coupons cannot be used in conjunction with other activities</li><li>Please use within the validity period, expired and invalid</li></ul>','',1,'2025-06-13 15:38:31','2025-06-17 10:41:26'),

(2,1,'send-captcha',NULL,'注册、登录、Quote时，给填写的邮箱发送验证码邮件','Opendelclub Email Verification Code','<div style=\"border:1px solid #ddd;margin:0 auto;max-width:600px;padding:20px;\"><h2 style=\"text-align:center;\">Authentication Code</h2><p>Hello {{username}}，</p><p>The operation you are performing requires email verification, and the verification code is:</p><div style=\"margin:20px 0;text-align:center;\"><span style=\"background-color:#cee4f4;font-size:24px;\"><span style=\"display:inline-block;letter-spacing:5px;padding:10px 20px;\">{{captcha}}</span></span></div><p>The verification code is valid for {{expire}} minutes, please use it promptly.</p><p>If not operated by myself, please ignore this email.</p></div>','{{captcha}} - 验证码',1,'2025-06-13 16:17:31','2025-06-17 16:25:09'),

(3,1,'order-notification',NULL,'后台创建订单后，给用户邮箱发送通知邮件','A new order has been created for you','<div style=\"font-family:\'Helvetica Neue\', Helvetica, Arial, sans-serif;margin:0;padding:0;\"><table class=\"table layout-table\" style=\"background-color:white;border-radius:8px;box-shadow:0 4px 12px rgba(0,0,0,0.05);margin:20px auto;max-width:600px;overflow:hidden;\" role=\"presentation\" width=\"100%\" cellspacing=\"0\" cellpadding=\"0\"><tbody><tr><td style=\"background-color:#FFCC33;padding:30px 30px 20px;text-align:center;\"><h1 style=\"color:white;font-size:24px;margin:0;\"><strong>{{system_name}} Order Notification</strong></h1></td></tr><tr><td style=\"padding:30px;\"><p style=\"color:#555;line-height:1.6;margin:0 0 20px;\">Dear {{username}},</p><p style=\"color:#555;line-height:1.6;margin:0 0 20px;\">A new order has been created for you in {{system_name}}. Here are the details:</p><table class=\"table layout-table\" style=\"border-collapse:collapse;margin-bottom:25px;width:100%;\" role=\"presentation\"><tbody><tr><td style=\"border-bottom:1px solid #eee;color:#777;padding:10px 0;\"><strong>Order No:</strong></td><td style=\"border-bottom:1px solid #eee;color:#333;padding:10px 0;text-align:right;\">{{order_no}}</td></tr><tr><td style=\"border-bottom:1px solid #eee;color:#777;padding:10px 0;\"><strong>Date:</strong></td><td style=\"border-bottom:1px solid #eee;color:#333;padding:10px 0;text-align:right;\">{{order_date}}</td></tr><tr><td style=\"border-bottom:1px solid #eee;color:#777;padding:10px 0;\"><strong>Product:</strong></td><td style=\"border-bottom:1px solid #eee;color:#333;padding:10px 0;text-align:right;\">{{product_name}}</td></tr><tr><td style=\"border-bottom:1px solid #eee;color:#777;padding:10px 0;\"><strong>Service:</strong></td><td style=\"border-bottom:1px solid #eee;color:#333;padding:10px 0;text-align:right;\">{{service_name}}</td></tr></tbody></table><p style=\"color:#555;line-height:1.6;margin:0 0 20px;\">You can now log in to your account to view and manage this order:</p><div style=\"margin:25px 0;text-align:center;\"><a style=\"background-color:#FFCC33;border-radius:30px;color:white;display:inline-block;font-weight:500;letter-spacing:0.5px;padding:12px 30px;text-decoration:none;\" href=\"{{login_link}}\">View Order in Dashboard</a></div><p style=\"color:#555;line-height:1.6;margin:0;\">If you have any questions, please contact our support team.</p></td></tr><tr><td style=\"background-color:#fafafa;border-top:1px solid #eee;color:#999;font-size:12px;padding:20px 30px;text-align:center;\"><p style=\"margin:0 0 5px;\">This is an automated email. Please do not reply directly.</p></td></tr></tbody></table></div>','{{system_name}} - 网站名称\r\n{{username}} - 用户名\r\n{{order_no}} - 订单号\r\n{{order_date}} - 订单日期\r\n{{product_name}} - 产品名称\r\n{{service_name}} - 服务名称\r\n{{login_link}} - 登录链接\r\n',1,'2025-06-17 11:16:50','2025-07-17 11:29:26'),

(4,1,'feedback-reply',NULL,'后台回复用户提交的留言后，给用户发送回复邮件','Your message has been replied to','<div style=\"background-color:#f8f9fa;border-radius:5px;padding:30px;\"><div style=\"margin-bottom:25px;text-align:center;\"><h1 style=\"color:#2c3e50;margin:0 0 10px;\">{{system_name}}</h1><p style=\"color:#7f8c8d;margin:0;\">Message reply notification</p></div><div style=\"background-color:#fff;border-left:4px solid #3498db;border-radius:5px;margin-bottom:20px;padding:20px;\"><h2 style=\"color:#2c3e50;margin-top:0;\">Dear {{username}}, hello!</h2><p>We have received a reply to the message you submitted on {{creat_time}}:</p><div style=\"background-color:#f8f9fa;border-radius:3px;margin:15px 0;padding:15px;\"><h3 style=\"color:#3498db;margin-top:0;\">Your message content:</h3><p style=\"margin-bottom:0;\">{{message_content}}</p></div><div style=\"background-color:#e8f4fd;border-radius:3px;margin:15px 0;padding:15px;\"><h3 style=\"color:#3498db;margin-top:0;\">Administrator\'s reply:</h3><p style=\"margin-bottom:0;\">{{reply_content}}</p></div><p>If you need further consultation, please log in to the website to view or reply directly to this email.</p></div><div style=\"border-top:1px solid #eee;color:#7f8c8d;font-size:12px;padding-top:20px;text-align:center;\"><p style=\"margin:5px 0;\">This email is automatically sent by the system, please do not reply directly</p><p style=\"margin:5px 0;\"><a style=\"color:#3498db;text-decoration:none;\" href=\"{{site_url}}\">Visit a website</a> | <a style=\"color:#3498db;text-decoration:none;\" href=\"{{login_link}}\">User Center</a></p></div></div>','',1,'2025-06-23 12:02:36','2025-07-17 11:29:10'),

(5,1,'send-feedback',NULL,'首页用户留言后，给产品经理发送通知邮件','User message notification','<div style=\"background-color:#f8f9fa;border-radius:5px;padding:30px;\"><div style=\"margin-bottom:25px;text-align:center;\"><h1 style=\"color:#2c3e50;margin:0 0 10px;\">{{system_name}}</h1><p style=\"color:#7f8c8d;margin:0;\">User message notification</p></div><div style=\"background-color:#fff;border-left:4px solid #e74c3c;border-radius:5px;margin-bottom:20px;padding:20px;\"><h2 style=\"color:#2c3e50;margin-top:0;\">Hello Product Manager!</h2><p>The system has received a new user message, please handle it promptly:</p><table class=\"table layout-table\" style=\"border-collapse:collapse;margin:15px 0;width:100%;\" role=\"presentation\"><tbody><tr><td style=\"color:#7f8c8d;padding:8px 0;width:80px;\">User Name:</td><td style=\"padding:8px 0;\"><strong>{{name}}</strong></td></tr><tr><td style=\"color:#7f8c8d;padding:8px 0;\">Phone：</td><td style=\"padding:8px 0;\">{{phone}}</td></tr><tr><td style=\"color:#7f8c8d;padding:8px 0;\">Email：</td><td style=\"padding:8px 0;\">{{email}}</td></tr><tr><td style=\"color:#7f8c8d;padding:8px 0;\">Company：</td><td style=\"padding:8px 0;\">{{company}}</td></tr><tr><td style=\"color:#7f8c8d;padding:8px 0;\">Message time：</td><td style=\"padding:8px 0;\">{{create_time}}</td></tr></tbody></table><div style=\"background-color:#f8f9fa;border-radius:3px;margin:15px 0;padding:15px;\"><h3 style=\"color:#e74c3c;margin-top:0;\">Feedback message：</h3><p style=\"margin-bottom:0;\">{{message_content}}</p></div><div style=\"margin:25px 0 15px;text-align:center;\"><a style=\"background-color:#e74c3c;border-radius:4px;color:white;display:inline-block;padding:10px 20px;text-decoration:none;\" href=\"{{login_link}}\"><strong>Process the message immediately</strong></a></div></div><div style=\"border-top:1px solid #eee;color:#7f8c8d;font-size:12px;padding-top:20px;text-align:center;\"><p style=\"margin:5px 0;\">This email is automatically sent by the system, please do not reply directly</p><p style=\"margin:5px 0;\">If you have any questions, please contact technical support</p></div></div>','',1,'2025-06-23 13:55:53','2025-07-17 11:28:03'),

(6,1,'shipment-notification',NULL,'后台填写邮寄信息后，给邮寄email发送通知邮件','You\'re getting a shipment {{tracking_no}}','<div style=\"font-family:\'Helvetica Neue\', Helvetica, Arial, sans-serif;margin:0;padding:0;\"><table class=\"table layout-table\" style=\"background-color:white;border-radius:8px;box-shadow:0 4px 12px rgba(0,0,0,0.05);margin:20px auto;max-width:600px;overflow:hidden;\" role=\"presentation\" width=\"100%\" cellspacing=\"0\" cellpadding=\"0\"><tbody><tr><td style=\"background-color:#6600CC;padding:30px 30px 20px;text-align:center;\"><h1 style=\"color:white;font-size:24px;margin:0;\"><strong>{{system_name}} Shipment Notification</strong></h1></td></tr><tr><td style=\"padding:30px;\"><p style=\"color:#555;line-height:1.6;margin:0 0 20px;\">Dear {{username}},</p><p style=\"color:#555;line-height:1.6;margin:0 0 20px;\">You\'re getting a shipment in {{system_name}}. Here are the Tracking details:</p><table class=\"table layout-table\" style=\"border-collapse:collapse;margin-bottom:25px;width:100%;\" role=\"presentation\"><tbody><tr><td style=\"border-bottom:1px solid #eee;color:#777;padding:10px 0;\"><strong>Tracking No:</strong></td><td style=\"border-bottom:1px solid #eee;color:#333;padding:10px 0;text-align:right;\">{{tracking_no}}</td></tr><tr><td style=\"border-bottom:1px solid #eee;color:#777;padding:10px 0;\"><strong>Tracking Company:</strong></td><td style=\"border-bottom:1px solid #eee;color:#333;padding:10px 0;text-align:right;\">{{tracking_company}}</td></tr></tbody></table><p style=\"color:#555;line-height:1.6;margin:0;\">If you have any questions, please contact our support team.</p></td></tr><tr><td style=\"background-color:#fafafa;border-top:1px solid #eee;color:#999;font-size:12px;padding:20px 30px;text-align:center;\"><p style=\"margin:0 0 5px;\">This is an automated email. Please do not reply directly.</p></td></tr></tbody></table></div>','{{system_name}} - 网站名称\r\n{{username}} - 用户名\r\n{{tracking_no}} - 物流单号\r\n{{tracking_company}} - 物流公司\r\n',1,'2025-06-27 15:02:08','2025-07-17 11:26:56'),

(7,1,'send-examine',NULL,'用户发布帖子后，给产品经理发送审核通知邮件','Post pending review','<div style=\"background-color:#f8f9fa;border-radius:5px;padding:30px;\"><div style=\"margin-bottom:25px;text-align:center;\"><h1 style=\"color:#2c3e50;margin:0 0 10px;\">{{system_name}}</h1><p style=\"color:#7f8c8d;margin:0;\">Post pending review</p></div><div style=\"background-color:#fff;border-left:4px solid #e74c3c;border-radius:5px;margin-bottom:20px;padding:20px;\"><h2 style=\"color:#2c3e50;margin-top:0;\">Hello Product Manager!</h2><p>The system has received a new user message, please handle it promptly:</p><table class=\"table layout-table\" style=\"border-collapse:collapse;margin:15px 0;width:100%;\" role=\"presentation\"><tbody><tr><td style=\"color:#7f8c8d;padding:8px 0;width:80px;\">User Name:</td><td style=\"padding:8px 0;\"><strong>{{name}}</strong></td></tr><tr><td style=\"color:#7f8c8d;padding:8px 0;\">Email：</td><td style=\"padding:8px 0;\">{{email}}</td></tr><tr><td style=\"color:#7f8c8d;padding:8px 0;\">Create time：</td><td style=\"padding:8px 0;\">{{create_time}}</td></tr><tr><td style=\"color:#7f8c8d;padding:8px 0;\">Title：</td><td style=\"padding:8px 0;\">{{post_title}}</td></tr></tbody></table><div style=\"background-color:#f8f9fa;border-radius:3px;margin:15px 0;padding:15px;\"><h3 style=\"color:#e74c3c;margin-top:0;\">Content：</h3><p style=\"margin-bottom:0;\">{{post_content}}</p></div><div style=\"margin:25px 0 15px;text-align:center;\"><a style=\"background-color:#e74c3c;border-radius:4px;color:white;display:inline-block;padding:10px 20px;text-decoration:none;\" href=\"{{login_link}}\"><strong>Login to the backend for review</strong></a></div></div><div style=\"border-top:1px solid #eee;color:#7f8c8d;font-size:12px;padding-top:20px;text-align:center;\"><p style=\"margin:5px 0;\">This email is automatically sent by the system, please do not reply directly</p><p style=\"margin:5px 0;\">If you have any questions, please contact technical support</p></div></div>','',1,'2025-07-15 13:35:35','2025-07-15 13:46:01'),

(8,1,'send-news-message',NULL,'管理员发布公共新闻后，选择用户发布通知邮件','New Announcement Notice','<div style=\"background-color:#f5f5f5;color:#333;font-family:\'Helvetica Neue\', Helvetica, Arial, sans-serif;margin:0;padding:0;\"><div style=\"background-color:#ffffff;margin:0 auto;max-width:600px;\"><!-- 邮件头部 --><div style=\"background-color:#1890ff;padding:20px;text-align:center;\"><h1 style=\"color:#ffffff;font-size:24px;margin:0;\">{{system_name}}</h1></div><!-- 邮件内容 --><div style=\"padding:30px;\"><h2 style=\"color:#1890ff;margin-top:0;\">Dear {{username}}, hello!</h2><p style=\"line-height:1.6;margin-bottom:20px;\">The administrator has posted a new public news article to share with you:</p><div style=\"background-color:#f9f9f9;border-left:4px solid #1890ff;margin-bottom:25px;padding:15px;\"><h3 style=\"color:#1890ff;margin-top:0;\">{{news_title}}</h3><p style=\"color:#666;font-size:14px;margin-bottom:10px;\">Release time: {{publish_date}}</p><p style=\"line-height:1.6;\">{{news_content}}</p></div><p><a style=\"background-color:#1890ff;border-radius:4px;color:#ffffff;display:inline-block;margin-bottom:20px;padding:12px 25px;text-decoration:none;\" href=\"{{link}}\"><strong>View complete content</strong></a></p></div><!-- 邮件底部 --><div style=\"background-color:#f5f5f5;color:#999;font-size:12px;padding:20px;text-align:center;\"><p style=\"margin:5px 0 0;\">This email is automatically sent by the system, please do not reply directly</p></div></div></div>','',1,'2025-07-16 17:08:31',NULL);

/*Table structure for table `opd_faq` */

DROP TABLE IF EXISTS `opd_faq`;

CREATE TABLE `opd_faq` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `category_id` int(11) DEFAULT NULL,
  `seo_url` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `question` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '问题',
  `answer` text COLLATE utf8mb4_unicode_ci COMMENT '回答',
  `create_time` datetime DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='FAQ表';

/*Data for the table `opd_faq` */

insert  into `opd_faq`(`id`,`category_id`,`seo_url`,`question`,`answer`,`create_time`,`update_time`) values 

(1,4,'2025-pre-sale-nucleic','2025 Pre-Sale|Nucleic','<p>DNA-encoded chemical libraries (DEL) is a technology that involves the synthesis and conjugation of chemical compounds to DNA barcodes (Figure 1). This enables affinity screening against targets with billions of molecules, followed by deconvolution through high-throughput sequencing. Boosted by recent development of the next-generation sequencing technology, DEL aims to accelerate the drug discovery process.</p>\r\n<p><img src=\"../../tinymce/20250523/59dfca51c2fb40c1eb871d8abb392ac0.png\" alt=\"\" width=\"1577\" height=\"513\"></p>\r\n<p>DNA-encoded chemical libraries (DEL) is a technology that involves the synthesis and conjugation of chemical compounds to DNA barcodes (Figure 1). This enables affinity screening against targets with billions of molecules, followed by deconvolution through high-throughput sequencing. Boosted by recent development of the next-generation sequencing technology, DEL aims to accelerate the drug discovery process.</p>\r\n<p><img src=\"../../tinymce/20250523/59dfca51c2fb40c1eb871d8abb392ac0.png\" alt=\"\" width=\"1577\" height=\"513\"></p>\r\n<p>DNA-encoded chemical libraries (DEL) is a technology that involves the synthesis and conjugation of chemical compounds to DNA barcodes (Figure 1). This enables affinity screening against targets with billions of molecules, followed by deconvolution through high-throughput sequencing. Boosted by recent development of the next-generation sequencing technology, DEL aims to accelerate the drug discovery process.</p>\r\n<p><img src=\"../../tinymce/20250523/59dfca51c2fb40c1eb871d8abb392ac0.png\" alt=\"\" width=\"1577\" height=\"513\"></p>','2025-04-28 13:45:55','2025-05-23 11:40:00'),

(2,3,'2025-pre-sale-nucleic-acid-monomer','2025 Pre-Sale|Nucleic Acid Monomer','<p>DNA-encoded chemical libraries (DEL) is a technology that involves the synthesis and conjugation of chemical compounds to DNA barcodes (Figure 1). This enables affinity screening against targets with billions of molecules, followed by deconvolution through high-throughput sequencing. Boosted by recent development of the next-generation sequencing technology, DEL aims to accelerate the drug discovery process.</p>\r\n<p><img src=\"../../tinymce/20250523/59dfca51c2fb40c1eb871d8abb392ac0.png\" alt=\"\" width=\"1577\" height=\"513\"></p>\r\n<p>DNA-encoded chemical libraries (DEL) is a technology that involves the synthesis and conjugation of chemical compounds to DNA barcodes (Figure 1). This enables affinity screening against targets with billions of molecules, followed by deconvolution through high-throughput sequencing. Boosted by recent development of the next-generation sequencing technology, DEL aims to accelerate the drug discovery process.</p>\r\n<p><img src=\"../../tinymce/20250523/59dfca51c2fb40c1eb871d8abb392ac0.png\" alt=\"\" width=\"1577\" height=\"513\"></p>\r\n<p>DNA-encoded chemical libraries (DEL) is a technology that involves the synthesis and conjugation of chemical compounds to DNA barcodes (Figure 1). This enables affinity screening against targets with billions of molecules, followed by deconvolution through high-throughput sequencing. Boosted by recent development of the next-generation sequencing technology, DEL aims to accelerate the drug discovery process.</p>\r\n<p><img src=\"../../tinymce/20250523/59dfca51c2fb40c1eb871d8abb392ac0.png\" alt=\"\" width=\"1577\" height=\"513\"></p>','2025-04-28 13:45:55','2025-05-23 10:56:26'),

(3,3,'2025-pre-sale-nucleic-acid','2025 Pre-Sale|Nucleic Acid','','2025-04-28 13:45:55','2025-05-23 10:56:22'),

(4,3,'2025-pre-sale-nucleic-acid-monomer-off','2025 Pre-Sale|Nucleic Acid Monomer Off','<p>DNA-encoded chemical libraries (DEL) is a technology that involves the synthesis and conjugation of chemical compounds to DNA barcodes (Figure 1). This enables affinity screening against targets with billions of molecules, followed by deconvolution through high-throughput sequencing. Boosted by recent development of the next-generation sequencing technology, DEL aims to accelerate the drug discovery process.</p>\r\n<p><img src=\"../../tinymce/20250523/59dfca51c2fb40c1eb871d8abb392ac0.png\" alt=\"\" width=\"1577\" height=\"513\"></p>\r\n<p>DNA-encoded chemical libraries (DEL) is a technology that involves the synthesis and conjugation of chemical compounds to DNA barcodes (Figure 1). This enables affinity screening against targets with billions of molecules, followed by deconvolution through high-throughput sequencing. Boosted by recent development of the next-generation sequencing technology, DEL aims to accelerate the drug discovery process.</p>\r\n<p><img src=\"../../tinymce/20250523/59dfca51c2fb40c1eb871d8abb392ac0.png\" alt=\"\" width=\"1577\" height=\"513\"></p>\r\n<p>DNA-encoded chemical libraries (DEL) is a technology that involves the synthesis and conjugation of chemical compounds to DNA barcodes (Figure 1). This enables affinity screening against targets with billions of molecules, followed by deconvolution through high-throughput sequencing. Boosted by recent development of the next-generation sequencing technology, DEL aims to accelerate the drug discovery process.</p>\r\n<p><img src=\"../../tinymce/20250523/59dfca51c2fb40c1eb871d8abb392ac0.png\" alt=\"\" width=\"1577\" height=\"513\"></p>','2025-04-28 13:45:55','2025-05-23 10:56:17'),

(5,4,'2025-pre-sale-nucleic-acid-monomer-off-20','2025 Pre-Sale|Nucleic Acid Monomer Off 20','ccc','2025-04-28 13:45:55','2025-05-23 09:48:17'),

(6,3,'2025-pre-sale-nucleic-acid-monomer-off-20-100','2025 Pre-Sale|Nucleic Acid Monomer Off 20%|100+','sss','2025-04-28 13:45:55','2025-05-23 09:48:03'),

(7,3,'test','test','dddd','2025-05-23 10:56:54',NULL);

/*Table structure for table `opd_faq_ask` */

DROP TABLE IF EXISTS `opd_faq_ask`;

CREATE TABLE `opd_faq_ask` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `question` text COLLATE utf8mb4_unicode_ci,
  `ip` varchar(30) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` datetime DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户提交问题表';

/*Data for the table `opd_faq_ask` */

insert  into `opd_faq_ask`(`id`,`question`,`ip`,`create_time`,`update_time`) values 

(1,'test111','127.0.0.1','2025-05-23 11:14:22',NULL),

(2,'ccccccc','127.0.0.1','2025-05-23 11:14:41',NULL),

(3,'这里是一个测试的问题','*************','2025-07-18 13:24:41',NULL);

/*Table structure for table `opd_faq_category` */

DROP TABLE IF EXISTS `opd_faq_category`;

CREATE TABLE `opd_faq_category` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `pid` int(11) DEFAULT '0' COMMENT '父分类',
  `name` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` datetime DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='FAQ分类表';

/*Data for the table `opd_faq_category` */

insert  into `opd_faq_category`(`id`,`pid`,`name`,`create_time`,`update_time`) values 

(1,0,'About DEL Technology','2025-05-22 17:24:09','2025-05-23 09:36:43'),

(2,0,'Customer Support','2025-05-22 17:24:24','2025-05-23 09:36:51'),

(3,1,'DNA-Encoded Libraries','2025-05-23 09:23:01',NULL),

(4,1,'OpenDEL','2025-05-23 09:23:16',NULL);

/*Table structure for table `opd_feedback` */

DROP TABLE IF EXISTS `opd_feedback`;

CREATE TABLE `opd_feedback` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(30) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `phone` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `email` varchar(30) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `company` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `feedback` text COLLATE utf8mb4_unicode_ci COMMENT '留言内容',
  `ip` varchar(30) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `reply_content` text COLLATE utf8mb4_unicode_ci COMMENT '回复内容',
  `reply_time` datetime DEFAULT NULL COMMENT '回复时间',
  `reply_status` tinyint(1) DEFAULT '0' COMMENT '0未回复 1已回复',
  `create_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='在线留言表';

/*Data for the table `opd_feedback` */

insert  into `opd_feedback`(`id`,`name`,`phone`,`email`,`company`,`feedback`,`ip`,`reply_content`,`reply_time`,`reply_status`,`create_time`) values 

(1,'test','13666669999','<EMAIL>','yidian','','127.0.0.1','你好，这是一封回复邮件~~~~','2025-06-23 13:12:52',1,'2025-05-16 11:24:55'),

(2,'Text','12321434345','<EMAIL>','********* ','ewrwerq','**************',NULL,NULL,0,'2025-07-17 16:07:02');

/*Table structure for table `opd_forum_posts` */

DROP TABLE IF EXISTS `opd_forum_posts`;

CREATE TABLE `opd_forum_posts` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `post_type` tinyint(1) DEFAULT NULL COMMENT '0Qustion 1Posting',
  `user_id` int(11) DEFAULT NULL COMMENT '用户id',
  `title` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '标题',
  `content` text COLLATE utf8mb4_unicode_ci COMMENT '内容',
  `topic` varchar(30) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `status` tinyint(1) DEFAULT '0' COMMENT '0审核中 1审核通过 2审核不通过',
  `fail_reason` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '审核不通过原因',
  `create_time` datetime DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=13 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='帖子表';

/*Data for the table `opd_forum_posts` */

insert  into `opd_forum_posts`(`id`,`post_type`,`user_id`,`title`,`content`,`topic`,`status`,`fail_reason`,`create_time`,`update_time`) values 

(1,0,3,'测试222','<p>你好，这是一个测试帖子。。。</p>','topic4',0,NULL,'2025-07-03 14:34:53','2025-07-17 09:57:36'),

(2,1,3,'test','this is a test','topic2',1,NULL,'2025-07-04 13:44:18',NULL),

(3,0,5,'你好啊','<p>hello!!!</p>','topic1',0,NULL,'2025-07-04 15:41:01',NULL),

(4,1,5,'hiii','<p>hello!!!</p>','topic4',2,'违禁','2025-07-04 15:42:39',NULL),

(5,0,5,'hello...','<p>hi,this is just a test!!!</p>','topic3',1,NULL,'2025-07-07 11:01:05',NULL),

(6,1,5,'hello hello...','<p>this is a test posting…</p>','topic3',1,'','2025-07-15 13:50:13',NULL),

(7,0,3,'Just a test','<p>ooo <a class=\"mention\" data-mention=\"@wu xy\" href=\"/iCommunity/user/3\" data-user-id=\"3\">@wu xy</a> hello <a class=\"mention\" data-mention=\"@peijin Li\" href=\"/iCommunity/user/5\" data-user-id=\"5\">@peijin Li</a> &nbsp;,this is a mention test</p>','topic5',2,'','2025-07-16 15:25:25',NULL),

(8,0,6,'Comprehensive Review of the Apple iPhone 16: Innovation Meets Refinement','<p><strong style=\"-webkit-text-stroke-width:0px;background-color:rgba(255, 255, 255, 0.9);color:rgba(0, 0, 0, 0.86);font-family:&quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, &quot;Arial Regular&quot;;font-size:14px;font-style:normal;font-variant-caps:normal;font-variant-ligatures:normal;letter-spacing:normal;orphans:2;text-align:justify;text-decoration-color:initial;text-decoration-style:initial;text-decoration-thickness:initial;text-indent:0px;text-transform:none;white-space:normal;widows:2;word-spacing:0px;\">Comprehensive Review of the Apple iPhone 16: Innovation Meets Refinement</strong></p><p>The iPhone 16 marks Apple’s latest iteration in its flagship smartphone lineup, blending incremental upgrades with subtle design refinements. As a device that sits at the intersection of cutting-edge technology and user-centric design, the iPhone 16 aims to solidify Apple’s dominance in the premium smartphone market. This review delves into its key features, performance, camera capabilities, battery life, and overall value proposition.</p><h3 style=\"-webkit-text-stroke-width:0px;background-color:rgba(255, 255, 255, 0.9);color:rgba(0, 0, 0, 0.86);font-family:&quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, &quot;Arial Regular&quot;;font-size:14px;font-style:normal;font-variant-caps:normal;font-variant-ligatures:normal;font-weight:500;letter-spacing:normal;margin:0px !important 0px 0px;orphans:2;padding:0px;text-align:start;text-decoration-color:initial;text-decoration-style:initial;text-decoration-thickness:initial;text-indent:0px;text-transform:none;white-space:normal;widows:2;word-spacing:0px;\"><strong>Design and Build Quality</strong></h3><p>The iPhone 16 retains Apple’s signature minimalist aesthetic, with a ceramic shield front cover and an aerospace-grade aluminum frame. The device is available in a range of vibrant colors, including a new \"Ultra Violet\" shade that adds a fresh twist to the lineup. The flat-edge design remains unchanged, but Apple has slightly tweaked the button placement for ergonomic improvements. The phone is IP68 water- and dust-resistant, ensuring durability in everyday use.</p><p>At 147.6 x 71.6 x 7.8 mm and weighing 170g, the iPhone 16 is comfortable to hold, though some users may find it marginally heavier than its predecessor due to internal component upgrades. The display is a 6.1-inch Super Retina XDR OLED panel with ProMotion technology (120Hz refresh rate), delivering crisp visuals and smooth scrolling.</p><h3 style=\"-webkit-text-stroke-width:0px;background-color:rgba(255, 255, 255, 0.9);color:rgba(0, 0, 0, 0.86);font-family:&quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, &quot;Arial Regular&quot;;font-size:14px;font-style:normal;font-variant-caps:normal;font-variant-ligatures:normal;font-weight:500;letter-spacing:normal;margin:0px !important 0px 0px;orphans:2;padding:0px;text-align:start;text-decoration-color:initial;text-decoration-style:initial;text-decoration-thickness:initial;text-indent:0px;text-transform:none;white-space:normal;widows:2;word-spacing:0px;\"><strong>Performance and Chipset</strong></h3><p>Under the hood, the iPhone 16 is powered by Apple’s A18 Bionic chip, built on a 3nm process for enhanced efficiency and performance. This chipset promises a 30% faster CPU and 40% more efficient GPU compared to the A17 Pro, making it a powerhouse for gaming, multitasking, and AI-driven tasks.</p><p><span style=\"background-color:rgba(255,255,255,0.9);color:rgba(0,0,0,0.86);font-family:&quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, &quot;Arial Regular&quot;;font-size:14px;\"><span style=\"-webkit-text-stroke-width:0px;display:inline !important;float:none;font-style:normal;font-variant-caps:normal;font-variant-ligatures:normal;font-weight:400;letter-spacing:normal;orphans:2;text-align:justify;text-decoration-color:initial;text-decoration-style:initial;text-decoration-thickness:initial;text-indent:0px;text-transform:none;white-space:normal;widows:2;word-spacing:0px;\">In real-world usage, the iPhone 16 handles everything from demanding games like </span></span><em style=\"-webkit-text-stroke-width:0px;background-color:rgba(255, 255, 255, 0.9);color:rgba(0, 0, 0, 0.86);font-family:&quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, &quot;Arial Regular&quot;;font-size:14px;font-variant-caps:normal;font-variant-ligatures:normal;font-weight:400;letter-spacing:normal;orphans:2;text-align:justify;text-decoration-color:initial;text-decoration-style:initial;text-decoration-thickness:initial;text-indent:0px;text-transform:none;white-space:normal;widows:2;word-spacing:0px;\"><i>Genshin Impact</i></em><span style=\"background-color:rgba(255,255,255,0.9);color:rgba(0,0,0,0.86);font-family:&quot;PingFang SC&quot;, &quot;Microsoft YaHei&quot;, &quot;Arial Regular&quot;;font-size:14px;\"><span style=\"-webkit-text-stroke-width:0px;display:inline !important;float:none;font-style:normal;font-variant-caps:normal;font-variant-ligatures:normal;font-weight:400;letter-spacing:normal;orphans:2;text-align:justify;text-decoration-color:initial;text-decoration-style:initial;text-decoration-thickness:initial;text-indent:0px;text-transform:none;white-space:normal;widows:2;word-spacing:0px;\"> to video editing in Final Cut Pro with ease. The Neural Engine has also been upgraded, enabling faster machine learning tasks, such as real-time photo enhancements and improved Siri responsiveness.</span></span></p>','topic3',1,'','2025-07-17 13:31:03','2025-07-17 13:41:17'),

(9,1,7,'这里是一个检测通过的帖子','<p>这里是内容</p>','topic2',1,'','2025-07-18 13:46:01','2025-07-18 13:47:20'),

(10,0,7,'这里是一个检测不通过的帖子','<p>这里是内容</p>','topic2',2,'内容有煽动舆论的嫌疑','2025-07-18 13:46:31','2025-07-18 13:47:09'),

(11,0,6,'121212','<p>32323</p>','topic2',1,'','2025-07-18 16:30:00','2025-07-18 16:30:45'),

(12,0,7,'这里是一个问答的帖子','<p>这里是问答的测试帖子内容</p>','topic2',1,'','2025-07-22 09:27:11','2025-07-22 09:27:50');

/*Table structure for table `opd_forum_posts_tags` */

DROP TABLE IF EXISTS `opd_forum_posts_tags`;

CREATE TABLE `opd_forum_posts_tags` (
  `post_id` int(11) DEFAULT NULL COMMENT '新闻id/帖子id',
  `tag_name` varchar(30) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `type` tinyint(1) DEFAULT '0' COMMENT '0帖子 1新闻/活动',
  `sort` int(2) DEFAULT NULL
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='帖子标签表';

/*Data for the table `opd_forum_posts_tags` */

insert  into `opd_forum_posts_tags`(`post_id`,`tag_name`,`type`,`sort`) values 

(4,'tag2',1,2),

(4,'tag4',1,1),

(4,'tag1',1,0),

(4,'tag2',0,0),

(4,'tag5',0,1),

(3,'tag1',0,0),

(3,'tag2',0,1),

(3,'tag5',0,2),

(2,'tag3',0,0),

(5,'tag2',0,0),

(5,'tag5',0,1),

(5,'tag6',0,2),

(6,'tag3',0,0),

(6,'tag4',0,1),

(7,'tag1',0,0),

(7,'tag4',0,1),

(8,'tag3',0,0),

(8,'tag5',0,1),

(9,'tag3',0,0),

(9,'tag2',0,1),

(10,'tag4',0,0),

(10,'tag5',0,1),

(11,'tag2',0,0),

(12,'tag4',0,0);

/*Table structure for table `opd_forum_reply` */

DROP TABLE IF EXISTS `opd_forum_reply`;

CREATE TABLE `opd_forum_reply` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `post_id` int(11) NOT NULL COMMENT '新闻id/帖子id',
  `parent_id` int(11) DEFAULT '0' COMMENT '父reply id',
  `user_id` int(11) NOT NULL COMMENT '用户id',
  `content` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `reply_to_comment` int(11) DEFAULT NULL,
  `reply_to_user` int(11) NOT NULL COMMENT '被评论/回复 用户id',
  `type` tinyint(1) DEFAULT '0' COMMENT '0帖子 1新闻/活动',
  `status` tinyint(1) DEFAULT '0' COMMENT '0审核中 1审核通过 2审核不通过',
  `fail_reason` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '审核不通过原因',
  `is_read` tinyint(1) DEFAULT '0' COMMENT '0未读 1已读',
  `create_time` datetime DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=32 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='论坛回复表';

/*Data for the table `opd_forum_reply` */

insert  into `opd_forum_reply`(`id`,`post_id`,`parent_id`,`user_id`,`content`,`reply_to_comment`,`reply_to_user`,`type`,`status`,`fail_reason`,`is_read`,`create_time`,`update_time`) values 

(1,5,0,5,'<p>你好，这是第一个评论。。。</p>',NULL,5,0,1,NULL,1,'2025-07-11 10:38:46',NULL),

(2,5,0,5,'<p>哈哈哈?</p>',NULL,5,0,1,NULL,1,'2025-07-11 11:03:22',NULL),

(3,5,0,5,'<figure class=\"image image_resized\" style=\"width:30.55%;\"><img style=\"aspect-ratio:1650/1100;\" src=\"/editor/images/20250711/31c415ce40eb92e2e1bdc0a6c42a1df3.jpg\" width=\"1650\" height=\"1100\"></figure>',NULL,5,0,1,NULL,0,'2025-07-11 11:28:37',NULL),

(4,5,3,5,'1111',NULL,5,0,1,NULL,0,'2025-07-11 15:01:37',NULL),

(5,5,3,5,'2222',NULL,5,0,1,NULL,0,'2025-07-11 15:07:06',NULL),

(6,5,3,5,'@peijin Li 3333333',NULL,5,0,1,NULL,0,'2025-07-11 15:07:38',NULL),

(7,5,2,5,'vvvvv',NULL,5,0,1,NULL,0,'2025-07-11 15:08:47',NULL),

(8,2,0,5,'<p>hi!!!</p>',NULL,3,1,1,'',0,'2025-07-15 14:25:59',NULL),

(9,7,0,3,'<p>hello… <a class=\"mention\" href=\"/iCommunity/user/3\" data-mention=\"@wu xy\" data-user-id=\"3\">@wu xy</a>&nbsp;</p>',NULL,3,0,2,'',0,'2025-07-16 15:33:33','2025-07-17 10:29:12'),

(10,7,9,3,'yyyy',NULL,3,0,1,NULL,0,'2025-07-17 10:20:18',NULL),

(11,8,0,6,'<p>hi</p>',NULL,6,0,1,'',0,'2025-07-17 13:54:06','2025-07-17 13:57:36'),

(12,8,11,6,'The iPhone 16 is a refined evolution of Apple’s flagship smartphone, offering top-tier performance, an exceptional camera system, and reliable battery life. While it may not revolutionize the market, it solidifies Apple’s position as a leader in premium smartphones. If you’re due for an upgrade or deeply embedded in the Apple ecosystem, the iPhone 16 is a worthy investment.',NULL,6,0,1,'',0,'2025-07-17 13:58:55','2025-07-17 13:59:03'),

(13,2,0,7,'<p>哈哈</p>',NULL,3,1,1,'',0,'2025-07-18 13:29:57','2025-07-18 13:30:32'),

(14,2,13,7,'1111',NULL,7,1,1,'',0,'2025-07-18 13:31:02','2025-07-18 13:31:28'),

(15,2,8,7,'来了！！！',NULL,5,1,1,'',0,'2025-07-18 13:32:02','2025-07-18 13:32:23'),

(16,2,8,7,'这是什么意思？？？',NULL,5,1,2,'',0,'2025-07-18 13:33:15','2025-07-18 13:35:39'),

(17,2,8,7,'@fu ke 啦啦咯',NULL,5,1,1,'',0,'2025-07-18 13:35:01','2025-07-18 13:35:32'),

(18,2,0,7,'<p><a class=\"mention\" data-mention=\"@wu xy\" href=\"/iCommunity/user/3\" data-user-id=\"3\">@wu xy</a> 年底阿富汗送的护发素</p>',NULL,3,1,1,'',0,'2025-07-18 16:22:16','2025-07-18 16:22:40'),

(19,11,0,7,'<p>是大法师的饭撒</p>',NULL,6,0,1,'',0,'2025-07-18 16:33:54','2025-07-18 16:35:05'),

(20,11,0,7,'<p>这里是一个测试不通过</p>',NULL,6,0,2,'你的评论太难听',0,'2025-07-18 17:06:32','2025-07-18 17:07:11'),

(21,2,0,6,'<p>12345678</p>',NULL,3,1,0,NULL,0,'2025-07-21 09:52:28',NULL),

(22,2,0,6,'<p>11</p>',NULL,3,1,0,NULL,0,'2025-07-21 09:53:45',NULL),

(23,2,0,6,'<p>444222</p>',NULL,3,1,0,NULL,0,'2025-07-21 09:53:56',NULL),

(24,2,0,6,'<figure class=\"image image_resized\" style=\"width:20.56%;\"><img style=\"aspect-ratio:1080/1080;\" src=\"/editor/images/20250721/63b67e52090960a41743608ea560b0ef.jpg\" width=\"1080\" height=\"1080\"></figure><p>123</p>',NULL,3,1,1,'',0,'2025-07-21 09:54:39','2025-07-21 09:55:14'),

(25,2,0,6,'<p><a class=\"mention\" data-mention=\"@mo zi\" href=\"/iCommunity/user/6\" data-user-id=\"6\">@mo zi</a> 122</p>',NULL,3,1,1,'',0,'2025-07-21 10:31:31','2025-07-21 10:33:44'),

(26,2,13,3,'hello',14,7,1,1,'',0,'2025-07-21 16:46:53','2025-07-21 16:47:05'),

(27,2,0,3,'<p>lllll</p>',NULL,3,1,0,NULL,0,'2025-07-21 16:53:29',NULL),

(28,11,19,6,'fgfg',0,7,0,0,NULL,0,'2025-07-21 17:50:39',NULL),

(29,2,0,3,'<p>?</p>',NULL,3,1,1,'',0,'2025-07-22 09:19:36','2025-07-22 09:19:52'),

(30,2,8,3,'llllllllll',17,7,1,1,'',0,'2025-07-22 09:35:42','2025-07-22 09:35:48'),

(31,8,11,6,'for an upgrade or deeply embedded in the Apple ecosystem, the iPhone 16 is a worthy investment.',0,6,0,1,'',0,'2025-07-22 14:48:01','2025-07-22 14:48:34');

/*Table structure for table `opd_forum_tags` */

DROP TABLE IF EXISTS `opd_forum_tags`;

CREATE TABLE `opd_forum_tags` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '标签名称',
  `sort` int(2) DEFAULT '50',
  `create_time` datetime DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='论坛标签分类表';

/*Data for the table `opd_forum_tags` */

insert  into `opd_forum_tags`(`id`,`name`,`sort`,`create_time`,`update_time`) values 

(1,'tag1',0,'2025-07-04 10:29:46',NULL),

(2,'tag2',0,'2025-07-04 10:29:52',NULL),

(3,'tag3',0,'2025-07-04 10:29:58',NULL),

(4,'tag4',0,'2025-07-04 10:30:04',NULL),

(5,'tag5',0,'2025-07-04 10:30:09',NULL),

(6,'tag6',0,'2025-07-04 10:30:15',NULL);

/*Table structure for table `opd_forum_topic` */

DROP TABLE IF EXISTS `opd_forum_topic`;

CREATE TABLE `opd_forum_topic` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '话题名称',
  `sort` int(2) DEFAULT '50',
  `create_time` datetime DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='论坛话题分类表';

/*Data for the table `opd_forum_topic` */

insert  into `opd_forum_topic`(`id`,`name`,`sort`,`create_time`,`update_time`) values 

(1,'topic1',0,'2025-07-04 10:29:11',NULL),

(2,'topic2',0,'2025-07-04 10:29:22',NULL),

(3,'topic3',0,'2025-07-04 10:29:27',NULL),

(4,'topic4',0,'2025-07-04 10:29:33',NULL),

(5,'topic5',0,'2025-07-04 10:29:38',NULL);

/*Table structure for table `opd_info` */

DROP TABLE IF EXISTS `opd_info`;

CREATE TABLE `opd_info` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `content` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '消息内容',
  `reply_content` text COLLATE utf8mb4_unicode_ci COMMENT '系统回复',
  `is_read` tinyint(1) DEFAULT '0' COMMENT '0未读 1已读',
  `create_time` datetime DEFAULT NULL,
  `reply_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='站内信息记录表';

/*Data for the table `opd_info` */

/*Table structure for table `opd_info_template` */

DROP TABLE IF EXISTS `opd_info_template`;

CREATE TABLE `opd_info_template` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `code` varchar(10) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '信息模板标识',
  `description` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '模板说明',
  `title` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '标题',
  `content` text COLLATE utf8mb4_unicode_ci COMMENT '内容',
  `create_time` datetime DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='站内信息模板';

/*Data for the table `opd_info_template` */

insert  into `opd_info_template`(`id`,`code`,`description`,`title`,`content`,`create_time`,`update_time`) values 

(1,'test','测试模板','this is a test','hello,this is just a test infomation!','2025-06-16 16:11:14',NULL);

/*Table structure for table `opd_news` */

DROP TABLE IF EXISTS `opd_news`;

CREATE TABLE `opd_news` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `category_id` int(11) DEFAULT NULL COMMENT '新闻分类id opd_news_category',
  `seo_url` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `user_id` int(11) DEFAULT NULL COMMENT '用户id',
  `title` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '标题',
  `content` text COLLATE utf8mb4_unicode_ci COMMENT '内容',
  `image` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '图片',
  `publish_date` date DEFAULT NULL COMMENT '发布日期',
  `topic` varchar(30) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` datetime DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='新闻表';

/*Data for the table `opd_news` */

insert  into `opd_news`(`id`,`category_id`,`seo_url`,`user_id`,`title`,`content`,`image`,`publish_date`,`topic`,`create_time`,`update_time`) values 

(1,1,'latest-progress-of-dellatest',2,'Latest Progress of DELLatest','<p style=\"-webkit-text-stroke-width:0px;background-color:rgb(248, 253, 255);border-style:solid;border-width:0px;box-sizing:border-box;color:rgb(17, 17, 17);font-family:Roboto_Regular, sans-serif;font-size:16px;font-style:normal;font-variant-caps:normal;font-variant-ligatures:normal;font-weight:400;letter-spacing:normal;line-height:1.4;margin-block:1em;margin:0px;orphans:2;padding:0px;text-align:start;text-decoration-color:initial;text-decoration-style:initial;text-decoration-thickness:initial;text-indent:0px;text-transform:none;white-space:normal;widows:2;word-spacing:0px;\">DNA-encoded chemical libraries (DEL) is a technology that involves the synthesis and conjugation of chemical compounds to DNA barcodes (Figure 1). This enables affinity screening against targets with billions of molecules, followed by deconvolution through high-throughput sequencing. Boosted by recent development of the next-generation sequencing technology, DEL aims to accelerate the drug discovery process.</p><figure class=\"image\"><img style=\"aspect-ratio:1577/513;\" src=\"/editor/images/20250717/6a3b4852c2222fd2b1c133929003b1f8.png\" width=\"1577\" height=\"513\"></figure><p style=\"-webkit-text-stroke-width:0px;background-color:rgb(248, 253, 255);border-style:solid;border-width:0px;box-sizing:border-box;color:rgb(17, 17, 17);font-family:Roboto_Regular, sans-serif;font-size:16px;font-style:normal;font-variant-caps:normal;font-variant-ligatures:normal;font-weight:400;letter-spacing:normal;line-height:1.4;margin-block:1em;margin:0px;orphans:2;padding:0px;text-align:start;text-decoration-color:initial;text-decoration-style:initial;text-decoration-thickness:initial;text-indent:0px;text-transform:none;white-space:normal;widows:2;word-spacing:0px;\">DNA-encoded chemical libraries (DEL) is a technology that involves the synthesis and conjugation of chemical compounds to DNA barcodes (Figure 1). This enables affinity screening against targets with billions of molecules, followed by deconvolution through high-throughput sequencing. Boosted by recent development of the next-generation sequencing technology, DEL aims to accelerate the drug discovery process.</p><figure class=\"image\"><img style=\"aspect-ratio:1577/513;\" src=\"/editor/images/20250717/6a3b4852c2222fd2b1c133929003b1f8.png\" width=\"1577\" height=\"513\"></figure><p style=\"-webkit-text-stroke-width:0px;background-color:rgb(248, 253, 255);border-style:solid;border-width:0px;box-sizing:border-box;color:rgb(17, 17, 17);font-family:Roboto_Regular, sans-serif;font-size:16px;font-style:normal;font-variant-caps:normal;font-variant-ligatures:normal;font-weight:400;letter-spacing:normal;line-height:1.4;margin-block:1em;margin:0px;orphans:2;padding:0px;text-align:start;text-decoration-color:initial;text-decoration-style:initial;text-decoration-thickness:initial;text-indent:0px;text-transform:none;white-space:normal;widows:2;word-spacing:0px;\">DNA-encoded chemical libraries (DEL) is a technology that involves the synthesis and conjugation of chemical compounds to DNA barcodes (Figure 1). This enables affinity screening against targets with billions of molecules, followed by deconvolution through high-throughput sequencing. Boosted by recent development of the next-generation sequencing technology, DEL aims to accelerate the drug discovery process.</p><figure class=\"image\"><img style=\"aspect-ratio:1577/513;\" src=\"/editor/images/20250717/6a3b4852c2222fd2b1c133929003b1f8.png\" width=\"1577\" height=\"513\"></figure>',NULL,'2024-12-01','topic5','2025-05-13 10:43:26','2025-07-17 17:05:48'),

(2,1,'latest-progress-of-dellatest-progress-of',2,'Latest Progress of DELLatest Progress of','<p style=\"-webkit-text-stroke-width:0px;background-color:rgb(248, 253, 255);border-style:solid;border-width:0px;box-sizing:border-box;color:rgb(17, 17, 17);font-family:Roboto_Regular, sans-serif;font-size:16px;font-style:normal;font-variant-caps:normal;font-variant-ligatures:normal;font-weight:400;letter-spacing:normal;line-height:1.4;margin-block:1em;margin:0px;orphans:2;padding:0px;text-align:start;text-decoration-color:initial;text-decoration-style:initial;text-decoration-thickness:initial;text-indent:0px;text-transform:none;white-space:normal;widows:2;word-spacing:0px;\">DNA-encoded chemical libraries (DEL) is a technology that involves the synthesis and conjugation of chemical compounds to DNA barcodes (Figure 1). This enables affinity screening against targets with billions of molecules, followed by deconvolution through high-throughput sequencing. Boosted by recent development of the next-generation sequencing technology, DEL aims to accelerate the drug discovery process.</p><figure class=\"image\"><img style=\"aspect-ratio:1577/513;\" src=\"/editor/images/20250717/6a3b4852c2222fd2b1c133929003b1f8.png\" width=\"1577\" height=\"513\"></figure><p style=\"-webkit-text-stroke-width:0px;background-color:rgb(248, 253, 255);border-style:solid;border-width:0px;box-sizing:border-box;color:rgb(17, 17, 17);font-family:Roboto_Regular, sans-serif;font-size:16px;font-style:normal;font-variant-caps:normal;font-variant-ligatures:normal;font-weight:400;letter-spacing:normal;line-height:1.4;margin-block:1em;margin:0px;orphans:2;padding:0px;text-align:start;text-decoration-color:initial;text-decoration-style:initial;text-decoration-thickness:initial;text-indent:0px;text-transform:none;white-space:normal;widows:2;word-spacing:0px;\">DNA-encoded chemical libraries (DEL) is a technology that involves the synthesis and conjugation of chemical compounds to DNA barcodes (Figure 1). This enables affinity screening against targets with billions of molecules, followed by deconvolution through high-throughput sequencing. Boosted by recent development of the next-generation sequencing technology, DEL aims to accelerate the drug discovery process.</p><figure class=\"image\"><img style=\"aspect-ratio:1577/513;\" src=\"/editor/images/20250717/6a3b4852c2222fd2b1c133929003b1f8.png\" width=\"1577\" height=\"513\"></figure><p style=\"-webkit-text-stroke-width:0px;background-color:rgb(248, 253, 255);border-style:solid;border-width:0px;box-sizing:border-box;color:rgb(17, 17, 17);font-family:Roboto_Regular, sans-serif;font-size:16px;font-style:normal;font-variant-caps:normal;font-variant-ligatures:normal;font-weight:400;letter-spacing:normal;line-height:1.4;margin-block:1em;margin:0px;orphans:2;padding:0px;text-align:start;text-decoration-color:initial;text-decoration-style:initial;text-decoration-thickness:initial;text-indent:0px;text-transform:none;white-space:normal;widows:2;word-spacing:0px;\">DNA-encoded chemical libraries (DEL) is a technology that involves the synthesis and conjugation of chemical compounds to DNA barcodes (Figure 1). This enables affinity screening against targets with billions of molecules, followed by deconvolution through high-throughput sequencing. Boosted by recent development of the next-generation sequencing technology, DEL aims to accelerate the drug discovery process.</p><figure class=\"image\"><img style=\"aspect-ratio:1577/513;\" src=\"/editor/images/20250717/6a3b4852c2222fd2b1c133929003b1f8.png\" width=\"1577\" height=\"513\"></figure>',NULL,'2025-05-15','topic1','2025-05-13 10:43:26','2025-07-17 17:05:06'),

(3,2,'latest-progress-of-dellatest-progress-of-dellatest',2,'Latest Progress of DELLatest Progress of DELLatest ','<p style=\"-webkit-text-stroke-width:0px;background-color:rgb(248, 253, 255);border-style:solid;border-width:0px;box-sizing:border-box;color:rgb(17, 17, 17);font-family:Roboto_Regular, sans-serif;font-size:16px;font-style:normal;font-variant-caps:normal;font-variant-ligatures:normal;font-weight:400;letter-spacing:normal;line-height:1.4;margin-block:1em;margin:0px;orphans:2;padding:0px;text-align:start;text-decoration-color:initial;text-decoration-style:initial;text-decoration-thickness:initial;text-indent:0px;text-transform:none;white-space:normal;widows:2;word-spacing:0px;\">DNA-encoded chemical libraries (DEL) is a technology that involves the synthesis and conjugation of chemical compounds to DNA barcodes (Figure 1). This enables affinity screening against targets with billions of molecules, followed by deconvolution through high-throughput sequencing. Boosted by recent development of the next-generation sequencing technology, DEL aims to accelerate the drug discovery process.</p><figure class=\"image\"><img style=\"aspect-ratio:1577/513;\" src=\"/editor/images/20250717/6a3b4852c2222fd2b1c133929003b1f8.png\" width=\"1577\" height=\"513\"></figure><p style=\"-webkit-text-stroke-width:0px;background-color:rgb(248, 253, 255);border-style:solid;border-width:0px;box-sizing:border-box;color:rgb(17, 17, 17);font-family:Roboto_Regular, sans-serif;font-size:16px;font-style:normal;font-variant-caps:normal;font-variant-ligatures:normal;font-weight:400;letter-spacing:normal;line-height:1.4;margin-block:1em;margin:0px;orphans:2;padding:0px;text-align:start;text-decoration-color:initial;text-decoration-style:initial;text-decoration-thickness:initial;text-indent:0px;text-transform:none;white-space:normal;widows:2;word-spacing:0px;\">DNA-encoded chemical libraries (DEL) is a technology that involves the synthesis and conjugation of chemical compounds to DNA barcodes (Figure 1). This enables affinity screening against targets with billions of molecules, followed by deconvolution through high-throughput sequencing. Boosted by recent development of the next-generation sequencing technology, DEL aims to accelerate the drug discovery process.</p><figure class=\"image\"><img style=\"aspect-ratio:1577/513;\" src=\"/editor/images/20250717/6a3b4852c2222fd2b1c133929003b1f8.png\" width=\"1577\" height=\"513\"></figure><p style=\"-webkit-text-stroke-width:0px;background-color:rgb(248, 253, 255);border-style:solid;border-width:0px;box-sizing:border-box;color:rgb(17, 17, 17);font-family:Roboto_Regular, sans-serif;font-size:16px;font-style:normal;font-variant-caps:normal;font-variant-ligatures:normal;font-weight:400;letter-spacing:normal;line-height:1.4;margin-block:1em;margin:0px;orphans:2;padding:0px;text-align:start;text-decoration-color:initial;text-decoration-style:initial;text-decoration-thickness:initial;text-indent:0px;text-transform:none;white-space:normal;widows:2;word-spacing:0px;\">DNA-encoded chemical libraries (DEL) is a technology that involves the synthesis and conjugation of chemical compounds to DNA barcodes (Figure 1). This enables affinity screening against targets with billions of molecules, followed by deconvolution through high-throughput sequencing. Boosted by recent development of the next-generation sequencing technology, DEL aims to accelerate the drug discovery process.</p><figure class=\"image\"><img style=\"aspect-ratio:1577/513;\" src=\"/editor/images/20250717/6a3b4852c2222fd2b1c133929003b1f8.png\" width=\"1577\" height=\"513\"></figure>',NULL,'2025-05-15','topic3','2025-05-13 10:43:26','2025-07-17 17:04:46'),

(4,3,'latest-progress-of-dellatest-progress-of-dellatest-progress-of-del',2,'Latest Progress of DELLatest Progress of DELLatest  Progress of DEL','<p style=\"-webkit-text-stroke-width:0px;background-color:rgb(248, 253, 255);border-style:solid;border-width:0px;box-sizing:border-box;color:rgb(17, 17, 17);font-family:Roboto_Regular, sans-serif;font-size:16px;font-style:normal;font-variant-caps:normal;font-variant-ligatures:normal;font-weight:400;letter-spacing:normal;line-height:1.4;margin-block:1em;margin:0px;orphans:2;padding:0px;text-align:start;text-decoration-color:initial;text-decoration-style:initial;text-decoration-thickness:initial;text-indent:0px;text-transform:none;white-space:normal;widows:2;word-spacing:0px;\">DNA-encoded chemical libraries (DEL) is a technology that involves the synthesis and conjugation of chemical compounds to DNA barcodes (Figure 1). This enables affinity screening against targets with billions of molecules, followed by deconvolution through high-throughput sequencing. Boosted by recent development of the next-generation sequencing technology, DEL aims to accelerate the drug discovery process.</p><figure class=\"image\"><img style=\"aspect-ratio:1577/513;\" src=\"/editor/images/20250717/6a3b4852c2222fd2b1c133929003b1f8.png\" width=\"1577\" height=\"513\"></figure><p style=\"-webkit-text-stroke-width:0px;background-color:rgb(248, 253, 255);border-style:solid;border-width:0px;box-sizing:border-box;color:rgb(17, 17, 17);font-family:Roboto_Regular, sans-serif;font-size:16px;font-style:normal;font-variant-caps:normal;font-variant-ligatures:normal;font-weight:400;letter-spacing:normal;line-height:1.4;margin-block:1em;margin:0px;orphans:2;padding:0px;text-align:start;text-decoration-color:initial;text-decoration-style:initial;text-decoration-thickness:initial;text-indent:0px;text-transform:none;white-space:normal;widows:2;word-spacing:0px;\">DNA-encoded chemical libraries (DEL) is a technology that involves the synthesis and conjugation of chemical compounds to DNA barcodes (Figure 1). This enables affinity screening against targets with billions of molecules, followed by deconvolution through high-throughput sequencing. Boosted by recent development of the next-generation sequencing technology, DEL aims to accelerate the drug discovery process.</p><figure class=\"image\"><img style=\"aspect-ratio:1577/513;\" src=\"/editor/images/20250717/6a3b4852c2222fd2b1c133929003b1f8.png\" width=\"1577\" height=\"513\"></figure><p style=\"-webkit-text-stroke-width:0px;background-color:rgb(248, 253, 255);border-style:solid;border-width:0px;box-sizing:border-box;color:rgb(17, 17, 17);font-family:Roboto_Regular, sans-serif;font-size:16px;font-style:normal;font-variant-caps:normal;font-variant-ligatures:normal;font-weight:400;letter-spacing:normal;line-height:1.4;margin-block:1em;margin:0px;orphans:2;padding:0px;text-align:start;text-decoration-color:initial;text-decoration-style:initial;text-decoration-thickness:initial;text-indent:0px;text-transform:none;white-space:normal;widows:2;word-spacing:0px;\">DNA-encoded chemical libraries (DEL) is a technology that involves the synthesis and conjugation of chemical compounds to DNA barcodes (Figure 1). This enables affinity screening against targets with billions of molecules, followed by deconvolution through high-throughput sequencing. Boosted by recent development of the next-generation sequencing technology, DEL aims to accelerate the drug discovery process.</p><figure class=\"image\"><img style=\"aspect-ratio:1577/513;\" src=\"/editor/images/20250717/6a3b4852c2222fd2b1c133929003b1f8.png\" width=\"1577\" height=\"513\"></figure>',NULL,'2025-05-15','topic2','2025-05-13 10:43:26','2025-07-17 17:04:30');

/*Table structure for table `opd_news_category` */

DROP TABLE IF EXISTS `opd_news_category`;

CREATE TABLE `opd_news_category` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `color` varchar(10) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '字体颜色',
  `backgroud_color` varchar(30) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '背景颜色',
  `create_time` datetime DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='新闻分类表';

/*Data for the table `opd_news_category` */

insert  into `opd_news_category`(`id`,`name`,`color`,`backgroud_color`,`create_time`,`update_time`) values 

(1,'News','#155797','rgb(231, 238, 244)','2025-05-13 09:52:44','2025-07-17 14:48:42'),

(2,'Papers','#f08411','rgb(247, 241, 231)','2025-05-13 09:53:33','2025-07-17 14:47:23'),

(3,'Activity','#11ca5f','rgb(231, 244, 232)','2025-06-10 15:18:45','2025-07-21 09:38:10');

/*Table structure for table `opd_order` */

DROP TABLE IF EXISTS `opd_order`;

CREATE TABLE `opd_order` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `parent_id` int(11) DEFAULT '0' COMMENT '父订单id opd_order',
  `info_id` int(11) NOT NULL COMMENT 'opd_order_info id',
  `user_id` int(11) NOT NULL COMMENT '用户id',
  `order_no` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '订单编号',
  `project_no` varchar(30) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '项目号',
  `money` varchar(10) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '订单金额',
  `money_unit` varchar(5) COLLATE utf8mb4_unicode_ci DEFAULT 'USD' COMMENT '金额单位',
  `tracking_no` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '物流单号',
  `tracking_company` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '物流公司',
  `product_id` int(11) DEFAULT '0' COMMENT '产品id opd_product',
  `product_progress_id` int(11) DEFAULT '0' COMMENT '产品进度id opd_progress',
  `service_id` int(11) DEFAULT '0' COMMENT '服务id opd_service',
  `service_progress_id` int(11) DEFAULT '0' COMMENT '服务进度id opd_progress',
  `remark` text COLLATE utf8mb4_unicode_ci COMMENT '备注',
  `order_status` tinyint(1) DEFAULT '0' COMMENT '订单状态0进行中 1已完成 2已取消',
  `create_time` datetime DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=26 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

/*Data for the table `opd_order` */

insert  into `opd_order`(`id`,`parent_id`,`info_id`,`user_id`,`order_no`,`project_no`,`money`,`money_unit`,`tracking_no`,`tracking_company`,`product_id`,`product_progress_id`,`service_id`,`service_progress_id`,`remark`,`order_status`,`create_time`,`update_time`) values 

(1,0,1,3,'202506161507101560',NULL,'10000','USD','487569678796567',NULL,1,2,0,0,NULL,0,'2025-06-16 15:07:10','2025-06-18 16:09:31'),

(2,0,2,3,'202506161509596774',NULL,'50000','USD','487569678796543',NULL,2,15,3,11,NULL,0,'2025-06-16 15:09:59','2025-06-18 15:58:41'),

(3,1,1,3,'202506181007384034',NULL,'5000','USD',NULL,NULL,0,0,1,17,NULL,0,'2025-06-18 10:07:38','2025-06-18 15:59:17'),

(4,1,1,3,'202506181012316031',NULL,'8000','USD',NULL,NULL,0,0,2,7,NULL,0,'2025-06-18 10:12:31','2025-06-18 10:12:44'),

(5,2,2,3,'202506181019532669',NULL,'6000','USD',NULL,NULL,0,0,3,10,NULL,0,'2025-06-18 10:19:53','2025-06-18 10:20:02'),

(6,0,3,3,'202506181043105956',NULL,'50000','USD','487569379796567',NULL,2,15,0,0,NULL,1,'2025-06-18 10:43:10','2025-06-18 11:40:54'),

(7,0,4,3,'202506181043494666','','600','USD','80986576465789','vvvvv',1,0,2,0,'',2,'2025-06-18 10:43:49','2025-06-26 17:20:49'),

(8,0,5,5,'202506251620178349','111','40','USD','23647568754353','fff',1,0,0,0,'这是用户A的订单',0,'2025-06-25 16:20:17','2025-07-02 09:54:08'),

(9,8,5,5,'202506251628268549',NULL,'4000','USD',NULL,NULL,0,0,2,8,NULL,0,'2025-06-25 16:28:26','2025-06-25 16:32:30'),

(10,8,5,5,'202506251648093565',NULL,'50','USD',NULL,NULL,0,0,3,0,NULL,0,'2025-06-25 16:48:09',NULL),

(11,8,5,5,'202506251648117903','222','50','USD','4567487576834','中通快递',0,0,4,20,NULL,0,'2025-06-25 16:48:11','2025-06-26 15:30:12'),

(12,0,6,7,'202507181106198754','0002','10000','USD','2312334345556','sf',1,4,1,16,'这里是备注',0,'2025-07-18 11:06:19','2025-07-21 17:26:29'),

(13,12,6,7,'202507181110029136','0002','3333','USD','2312334345556','sf',0,0,2,8,NULL,0,'2025-07-18 11:10:02','2025-07-21 17:26:53'),

(14,0,7,7,'202507181111486975','00005','55555','USD','9999','jd',2,0,4,0,'yyyyy',0,'2025-07-18 11:11:48',NULL),

(15,0,8,7,'202507181113478473','007','6666','USD','33333','cn',3,0,2,0,'有订单吗',0,'2025-07-18 11:13:47',NULL),

(16,0,9,7,'202507181132055257','008','','USD','33434656','vc',1,0,3,0,'这还有服务',0,'2025-07-18 11:32:05',NULL),

(17,0,10,6,'202507211656192786','','','USD','','',1,0,0,0,'',0,'2025-07-21 16:56:19',NULL),

(18,0,11,6,'202507211656313674','','','USD','','',1,0,0,0,'',0,'2025-07-21 16:56:31',NULL),

(19,0,12,6,'202507211656394451','','','USD','','',1,0,1,0,'',0,'2025-07-21 16:56:39',NULL),

(20,0,13,6,'202507211657412457','','','USD','','',2,0,0,0,'',0,'2025-07-21 16:57:41',NULL),

(21,0,14,6,'202507211658382460','','','USD','','',3,0,0,0,'',0,'2025-07-21 16:58:38',NULL),

(22,0,15,7,'202507211659029362','','','USD','','',3,0,0,0,'',2,'2025-07-21 16:59:02','2025-07-22 13:50:19'),

(23,0,16,7,'202507211659522707','','','USD','','',3,24,0,0,'',1,'2025-07-21 16:59:52','2025-07-22 13:41:04'),

(24,21,14,6,'202507220927594788','','122','USD','','',0,0,1,16,NULL,0,'2025-07-22 09:27:59',NULL),

(25,18,11,6,'202507220928548150','','1122','USD','','',0,0,1,16,NULL,0,'2025-07-22 09:28:54',NULL);

/*Table structure for table `opd_order_file` */

DROP TABLE IF EXISTS `opd_order_file`;

CREATE TABLE `opd_order_file` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `order_id` int(11) DEFAULT '0' COMMENT '订单id opd_order',
  `type` tinyint(1) DEFAULT NULL COMMENT '0产品 1服务',
  `file_type` tinyint(1) DEFAULT NULL COMMENT '0保密文件 1用户上传资料',
  `file` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `file_name` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `url` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `url_describe` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '链接验证说明',
  `create_time` datetime DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=27 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单文件表';

/*Data for the table `opd_order_file` */

insert  into `opd_order_file`(`id`,`order_id`,`type`,`file_type`,`file`,`file_name`,`url`,`url_describe`,`create_time`,`update_time`) values 

(1,1,0,0,'','','http://www.baidu.com','密码：666','2025-05-27 14:57:11','2025-06-18 16:26:17'),

(7,2,1,0,'/storage/security/20250527/ba0645aa0c0221d1139550dc7830018a.docx','OPD社区网站内容V1.docx','',NULL,'2025-05-27 16:03:31',NULL),

(8,2,1,0,'','','www.01000.cn','88888','2025-05-30 09:46:33','2025-06-03 14:52:59'),

(9,2,0,0,'/storage/security/20250530/05b224ceefb5aa0d8668461a08b8c505.docx','5.28个人中心修改意见.docx','','','2025-05-30 09:47:03','2025-05-30 10:03:53'),

(10,2,1,0,NULL,NULL,'www.01000.cn','222','2025-05-30 11:35:20',NULL),

(11,1,0,0,'/storage/security/20250530/f67a84b3072dbb1e87b6df817d8e7410.docx','5.28个人中心修改意见.docx','','','2025-05-30 14:20:26','2025-06-18 16:23:55'),

(12,3,0,0,NULL,NULL,'www.01000.cn','','2025-05-30 14:50:01',NULL),

(13,3,1,0,'','','www.01000889.cn','','2025-05-30 14:50:27','2025-05-30 14:50:38'),

(14,1,0,0,'','','http://www.01000.cn','校验码：111111','2025-06-03 14:17:06','2025-06-18 16:26:09'),

(19,3,1,1,'/storage/userfile/20250619/ca44eee142e1c93c7701af7bd0bd1314.jpg','banner_01_pc.jpg',NULL,NULL,'2025-06-19 11:38:25',NULL),

(20,3,1,1,'/storage/userfile/20250619/1e7ceadf06d85efe7d236c8f595e6d46.jpg','banner_01_pc.jpg',NULL,NULL,'2025-06-19 11:38:25',NULL),

(21,3,1,1,'/storage/userfile/20250619/02d5e238e7a2566f6d59bb7ebaa4fabf.png','BHEEHGGIAFIJB-a0bqJwnT0B.png',NULL,NULL,'2025-06-19 11:41:42',NULL),

(22,4,1,1,'/storage/userfile/20250619/7e71fffc5307a2932c1a0c0a474e7eb3.jpg','user-1.jpg',NULL,NULL,'2025-06-19 11:42:41',NULL),

(23,13,1,1,'/storage/userfile/20250722/c4d51caa87076ec3c48015784c2dbf32.docx','手绘资料.docx',NULL,NULL,'2025-07-22 09:13:14',NULL),

(24,24,1,1,'/storage/userfile/20250722/7925d08ca80a4fd9fffd30fdcec6cf4e.pdf','Pink_Head_Crochet_Pattern.pdf',NULL,NULL,'2025-07-22 09:29:24',NULL),

(25,5,1,1,'/storage/userfile/20250722/eab0165f0a049aecb3643fc5eda65e3a.jpg','17.jpg',NULL,NULL,'2025-07-22 10:24:25',NULL),

(26,13,1,1,'/storage/userfile/20250722/2cb094469c1c3ddd2a8cd0346e3cc0df.jpg','未标题-1.jpg',NULL,NULL,'2025-07-22 10:31:08',NULL);

/*Table structure for table `opd_order_info` */

DROP TABLE IF EXISTS `opd_order_info`;

CREATE TABLE `opd_order_info` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT 'opd_user',
  `email` varchar(30) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `first_name` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `last_name` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `country` varchar(30) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `organization` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `title` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `phone` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `city` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `express_first_name` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `express_last_name` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `express_email` varchar(30) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `express_phone` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `express_address` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `express_postcode` varchar(30) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` datetime DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=17 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单用户信息表';

/*Data for the table `opd_order_info` */

insert  into `opd_order_info`(`id`,`user_id`,`email`,`first_name`,`last_name`,`country`,`organization`,`title`,`phone`,`city`,`express_first_name`,`express_last_name`,`express_email`,`express_phone`,`express_address`,`express_postcode`,`create_time`,`update_time`) values 

(1,3,'<EMAIL>','wu','xy','China','yidian','ttt','13677778888','222',NULL,NULL,NULL,NULL,NULL,NULL,'2025-06-16 15:07:10','2025-06-18 16:09:31'),

(2,3,'<EMAIL>','wu','xy','China','yidian','ttt','13677778888','555555',NULL,NULL,NULL,NULL,NULL,NULL,'2025-06-16 15:09:59','2025-06-18 15:58:41'),

(3,3,'<EMAIL>','wu','xy','China','yidian','ttt','13677778888','555555',NULL,NULL,NULL,NULL,NULL,NULL,'2025-06-18 10:43:10','2025-06-18 11:40:54'),

(4,3,'<EMAIL>','wu','xy','China','yidian','ttt','13677778888','555555','wu','xymm','<EMAIL>','13600009999','2222','555','2025-06-18 10:43:49','2025-06-26 17:20:49'),

(5,5,'<EMAIL>','peijin','Li','China','HitGen','','13408096659','chengdu','','','','','','','2025-06-25 16:20:17','2025-07-02 09:54:08'),

(6,7,'<EMAIL>','fu','ke','China','del','小卡拉米','18521366550','上海','fu','ke','<EMAIL>','18521366550','sh','0001','2025-07-18 11:06:19','2025-07-21 17:26:29'),

(7,7,'<EMAIL>','fu','ke','China','del','uuuu','18521366550','bj','fu','ke','<EMAIL>','18521366550','bj','0004','2025-07-18 11:11:48',NULL),

(8,7,'<EMAIL>','fu','ke','China','del','测试111','18521366550','nj','fu','ke','<EMAIL>','18521366550','nj','0077','2025-07-18 11:13:47',NULL),

(9,7,'<EMAIL>','fu','ke','China','del','哦哦哦','18521366550','','fu','ke','<EMAIL>','18521366550','hn','008','2025-07-18 11:32:05',NULL),

(10,6,'<EMAIL>','mo','zi','China','12346','','17891912921','','mo','zi','<EMAIL>','17891912921','','','2025-07-21 16:56:19',NULL),

(11,6,'<EMAIL>','mo','zi','China','12346','','17891912921','','mo','zi','<EMAIL>','17891912921','','','2025-07-21 16:56:31',NULL),

(12,6,'<EMAIL>','mo','zi','China','12346','','17891912921','','mo','zi','<EMAIL>','17891912921','','','2025-07-21 16:56:39',NULL),

(13,6,'<EMAIL>','mo','zi','China','12346','','17891912921','','mo','zi','<EMAIL>','17891912921','','','2025-07-21 16:57:41',NULL),

(14,6,'<EMAIL>','mo','zi','China','12346','','17891912921','','mo','zi','<EMAIL>','17891912921','','','2025-07-21 16:58:38',NULL),

(15,7,'<EMAIL>','fu','ke','China','del','','18521366550','','fu','ke','<EMAIL>','18521366550','','','2025-07-21 16:59:02','2025-07-22 13:50:19'),

(16,7,'<EMAIL>','fu','ke','China','del','','18521366550','','fu','ke','<EMAIL>','18521366550','','','2025-07-21 16:59:52','2025-07-22 13:41:04');

/*Table structure for table `opd_pages` */

DROP TABLE IF EXISTS `opd_pages`;

CREATE TABLE `opd_pages` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `page_id` int(11) DEFAULT NULL COMMENT '父页面',
  `url` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '页面链接',
  `name` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '页面名称',
  `image` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `image_smt` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `content` text COLLATE utf8mb4_unicode_ci,
  `sort` int(2) DEFAULT '50',
  `seo_title` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `seo_description` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `seo_keywords` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` datetime DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='自定义页面表';

/*Data for the table `opd_pages` */

insert  into `opd_pages`(`id`,`page_id`,`url`,`name`,`image`,`image_smt`,`content`,`sort`,`seo_title`,`seo_description`,`seo_keywords`,`create_time`,`update_time`) values 

(1,0,NULL,'首页',NULL,NULL,NULL,50,'HitGen OpenDEL™','','','2025-05-15 11:22:51','2025-06-10 15:43:26'),

(2,0,NULL,'产品',NULL,NULL,NULL,50,'OpenDEL™Kit - HitGen OpenDEL™','','','2025-06-10 15:51:40',NULL),

(3,0,NULL,'Terms',NULL,NULL,'<p>This site (the \"Site\") is owned, managed and maintained by HitGen Inc. (hereinafter referred to as \"the company\" or \"us\"). Please read the following terms carefully before using this website. Using this website indicates that you have known and accepted these terms. If you do not accept these terms, please do not use this website. We maintain this website for information dissemination and communication purposes. This page contains terms of use regarding access and usage rights of this website. If you do not accept these terms of use or comply with their provisions, you must not use this website.</p><h2>Changes To Terms</h2><p>We may, at any time, for any reason and without notice, make changes to the Site, including its look, feel, format, and content, as well as the products and services as described in the Site. Any modifications will take effect when posted to the Site. Therefore, each time you access the Site, you need to review the Terms of Use upon which access and use of the Site is conditioned. By your continuing use of the Site after changes are posted, you will be deemed to have accepted such changes.</p><h2>Jurisdiction</h2><p>This website is not open to any individual or entity in a specific jurisdiction (due to nationality, residence, citizenship or other reasons). If within this jurisdiction, the publication or existence of this website and its content (including product and services) is unachievable or violates local laws or regulations. If you have such a situation, you have no right to access or use any information on this website. We do not make any statement as to whether the information, opinions, suggestions or other content of this website (collectively referred to as the \"content\") is applicable outside of China. Users who choose to access this website from other regions are at their own risk and are responsible for complying with local laws.</p><h2>Scope of Use</h2><p>You are only authorized to view, use, copy for your records and download small portions of the Content (including without limitation text, graphics, software, audio and video files and photos) of the Site for your informational, non-commercial use, provided that you leave all the copyright notices, including copyright management information, or other proprietary notices intact.</p><p>You may not store, modify, reproduce, transmit, reverse engineer or distribute a significant portion of the Content on the Site, or the design or layout of the Site or individual sections of it, in any form or media. The systematic retrieval of data from the Site is also prohibited.</p><p>E-mail submission via the Internet may be insecure and risk being intercepted by a third party. Please consider this before sending any information via email. Please refer to our\" privacy policy\".</p><p>You agree not to submit or transmit any e-mails or materials through the Site that: (i) are defamatory, threatening, obscene or harassing, (ii) contain a virus, worm, Trojan horse or any other harmful component, (iii) incorporate copyrighted or other proprietary material of any third party without such party\'s permission or (iv) otherwise violate any applicable laws. We shall not be subject to any obligations of confidentiality regarding any information or materials that you submit online except as specified in the Terms of Use, or as set forth in any additional terms and conditions relating to specific products or services, or as otherwise specifically agreed or required by law. The commercial use, reproduction, transmission or distribution of any information, software or other material available through the Site without our prior written consent is strictly prohibited.</p>',52,'Terms - HitGen OpenDEL™','','','2025-06-10 16:23:50','2025-06-20 14:08:48'),

(4,0,NULL,'About','/storage/20250620/e96c1e7b3ca01bd25c7ad3112d5f4040.jpg','/storage/20250620/12cb778125a98d0c79f8c4508a523315.jpg','<h2>About OpenDELclub</h2><p>OpenDELclub is a platform initiative by&nbsp;HitGen, dedicated to fostering collaboration among industry, academia, and research institutions. Our platform serves as a hub for researchers and professionals interested in&nbsp;<strong> DNA-encoded library (DEL) </strong>technology&nbsp;and drug discovery, providing a space for knowledge exchange, innovation, and partnership.</p><h2>Empowering Drug Discovery with DEL Technology</h2><p>In drug development, finding the right molecule to target \"undruggable\" proteins is like searching for a needle in a haystack. Traditional methods often resemble groping in the dark, while&nbsp;DNA-encoded library (DEL) technology&nbsp;acts as a guiding flashlight, revolutionizing hit identification and lead optimization.</p><p>DEL technology combines&nbsp;combinatorial chemistry with DNA barcoding, enabling the rapid synthesis and screening of billions to trillions of compounds. By tagging each molecule with a unique DNA sequence, researchers can efficiently identify high-affinity binders to biological targets, accelerating the discovery of novel therapeutics.</p><h2>Starting a Journey to Access the Vast DEL Space with OpenDEL Kit</h2><p>OpenDEL™ is an open-access small molecule screening platform that enables fully autonomous DNA-Encoded library (DEL) exploration. With complete data transparency and open architecture, we empower researchers to validate every innovative idea, starting your journey to access the vast DEL space.&nbsp;<br>3 Billion Drug-like Compounds<br>Multi-target Screening<br>Comprehensive Data Access<br>Flexible Collaboration Model</p><h2>Join Our Community</h2><p>OpenDELclub connects global innovators in drug discovery. Through seminars, collaborative projects, and resource sharing, we aim to advance DEL applications and bring groundbreaking therapies to life.</p><h2>Join Our Community</h2><p>Become part of the OpenDELclub today!</p>',50,'About - HitGen OpenDEL™','','','2025-06-10 16:24:04','2025-06-20 09:57:24'),

(5,0,NULL,'Contact Us',NULL,NULL,'<p>&nbsp;</p>',50,'Contact - HitGen OpenDEL™','','','2025-06-10 16:24:52','2025-06-20 10:15:42'),

(6,0,NULL,'Privacy',NULL,NULL,'<h2>Terms of Service</h2><p>This site (the \"Site\") is owned, managed and maintained by HitGen Inc. (hereinafter referred to as \"the company\" or \"us\"). Please read the following terms carefully before using this website. Using this website indicates that you have known and accepted these terms. If you do not accept these terms, please do not use this website. We maintain this website for information dissemination and communication purposes. This page contains terms of use regarding access and usage rights of this website. If you do not accept these terms of use or comply with their provisions, you must not use this website.</p><h2>Changes To Terms</h2><p>We may, at any time, for any reason and without notice, make changes to the Site, including its look, feel, format, and content, as well as the products and services as described in the Site. Any modifications will take effect when posted to the Site. Therefore, each time you access the Site, you need to review the Terms of Use upon which access and use of the Site is conditioned. By your continuing use of the Site after changes are posted, you will be deemed to have accepted such changes.</p><h2>Jurisdiction</h2><p>This website is not open to any individual or entity in a specific jurisdiction (due to nationality, residence, citizenship or other reasons). If within this jurisdiction, the publication or existence of this website and its content (including product and services) is unachievable or violates local laws or regulations. If you have such a situation, you have no right to access or use any information on this website. We do not make any statement as to whether the information, opinions, suggestions or other content of this website (collectively referred to as the \"content\") is applicable outside of China. Users who choose to access this website from other regions are at their own risk and are responsible for complying with local laws.</p><h2>Scope of Use</h2><p>You are only authorized to view, use, copy for your records and download small portions of the Content (including without limitation text, graphics, software, audio and video files and photos) of the Site for your informational, non-commercial use, provided that you leave all the copyright notices, including copyright management information, or other proprietary notices intact.</p><p>You may not store, modify, reproduce, transmit, reverse engineer or distribute a significant portion of the Content on the Site, or the design or layout of the Site or individual sections of it, in any form or media. The systematic retrieval of data from the Site is also prohibited.</p><p>E-mail submission via the Internet may be insecure and risk being intercepted by a third party. Please consider this before sending any information via email. Please refer to our\" privacy policy\".</p><p>You agree not to submit or transmit any e-mails or materials through the Site that: (i) are defamatory, threatening, obscene or harassing, (ii) contain a virus, worm, Trojan horse or any other harmful component, (iii) incorporate copyrighted or other proprietary material of any third party without such party\'s permission or (iv) otherwise violate any applicable laws. We shall not be subject to any obligations of confidentiality regarding any information or materials that you submit online except as specified in the Terms of Use, or as set forth in any additional terms and conditions relating to specific products or services, or as otherwise specifically agreed or required by law. The commercial use, reproduction, transmission or distribution of any information, software or other material available through the Site without our prior written consent is strictly prohibited.</p>',51,'Privacy - HitGen OpenDEL™','','','2025-06-10 16:25:49','2025-06-20 14:07:08'),

(7,0,NULL,'Resources','/storage/20250620/f497ea32fce84ad0261dfd58b6a5b427.jpg','/storage/20250620/bcf68e29600ff90cbbac8ecb192f5b81.jpg','<p>&nbsp;</p>',50,'Resources - HitGen OpenDEL™','','','2025-06-20 14:01:44',NULL);

/*Table structure for table `opd_pages_content` */

DROP TABLE IF EXISTS `opd_pages_content`;

CREATE TABLE `opd_pages_content` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `page_id` int(11) NOT NULL,
  `width` varchar(2) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '宽度',
  `back_image` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '背景图片',
  `back_color` varchar(10) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '背景颜色',
  `content` text COLLATE utf8mb4_unicode_ci COMMENT '文字',
  `image` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '图片',
  `link` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '链接',
  `sort` int(2) DEFAULT '50',
  PRIMARY KEY (`id`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='页面内容表';

/*Data for the table `opd_pages_content` */

/*Table structure for table `opd_private_conversation` */

DROP TABLE IF EXISTS `opd_private_conversation`;

CREATE TABLE `opd_private_conversation` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user1_id` int(11) NOT NULL COMMENT 'opd_user',
  `user2_id` int(11) NOT NULL COMMENT 'opd_user',
  `create_time` datetime DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='私信会话表';

/*Data for the table `opd_private_conversation` */

insert  into `opd_private_conversation`(`id`,`user1_id`,`user2_id`,`create_time`,`update_time`) values 

(1,5,3,'2025-07-14 11:03:57','2025-07-15 14:24:52'),

(2,5,2,'2025-07-15 10:54:48','2025-07-15 11:01:38'),

(3,6,5,'2025-07-17 13:57:08','2025-07-17 13:57:14'),

(4,7,6,'2025-07-18 13:55:46','2025-07-21 10:21:11'),

(5,6,2,'2025-07-18 15:03:30','2025-07-18 15:03:30');

/*Table structure for table `opd_private_conversation_delete` */

DROP TABLE IF EXISTS `opd_private_conversation_delete`;

CREATE TABLE `opd_private_conversation_delete` (
  `conversation_id` int(11) NOT NULL COMMENT '会话id opd_private_conversation',
  `user_id` int(11) NOT NULL COMMENT '用户id',
  `delete_time` datetime DEFAULT NULL COMMENT '删除时间',
  KEY `idx_conversation_user` (`conversation_id`,`user_id`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

/*Data for the table `opd_private_conversation_delete` */

/*Table structure for table `opd_private_message` */

DROP TABLE IF EXISTS `opd_private_message`;

CREATE TABLE `opd_private_message` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `conversation_id` int(11) NOT NULL COMMENT '会话id opd_private_conversation',
  `sender_id` int(11) NOT NULL COMMENT '发送者id',
  `receiver_id` int(11) NOT NULL COMMENT '接收者id',
  `content` text COLLATE utf8mb4_unicode_ci COMMENT '私信内容',
  `file_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `file_size` int(5) DEFAULT NULL,
  `type` varchar(5) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '消息类型：text/image/file',
  `is_read` tinyint(1) DEFAULT '0' COMMENT '0未读 1已读',
  `create_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=29 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='私信消息表';

/*Data for the table `opd_private_message` */

insert  into `opd_private_message`(`id`,`conversation_id`,`sender_id`,`receiver_id`,`content`,`file_name`,`file_size`,`type`,`is_read`,`create_time`) values 

(1,1,5,3,'111',NULL,NULL,'text',1,'2025-07-14 17:57:45'),

(2,1,5,3,'2222',NULL,NULL,'text',1,'2025-07-14 17:58:53'),

(3,1,5,3,'人人人人',NULL,NULL,'text',1,'2025-07-14 18:09:09'),

(4,1,5,3,'test test tttt',NULL,NULL,'text',1,'2025-07-15 09:31:38'),

(10,1,5,3,'/storage/messages/20250715/5fc0fb0dd6ed614f9e11c971654088a8.jpg',NULL,NULL,'image',1,'2025-07-15 10:20:07'),

(9,1,5,3,'/storage/messages/20250715/f64b181c981ddfb3f77b9633008dea9a.jpg',NULL,NULL,'image',1,'2025-07-15 10:18:36'),

(11,1,5,3,'/storage/messages/20250715/75d5498adec159b3ca739db9b94d046c.jpg',NULL,NULL,'image',1,'2025-07-15 10:20:07'),

(12,1,5,3,'/storage/messages/20250715/a2e9cd96e5f2c31dcb6a15a062379eeb.pdf','网站需求share V3.pdf',836833,'file',1,'2025-07-15 10:40:49'),

(13,2,5,2,'你好。。。',NULL,NULL,'text',0,'2025-07-15 10:55:06'),

(14,2,5,2,'/storage/messages/20250715/fd36ffc08b5dcea8262dd8e32f460b5c.jpg',NULL,NULL,'image',0,'2025-07-15 10:56:02'),

(15,2,5,2,'是是是',NULL,NULL,'text',0,'2025-07-15 10:56:12'),

(16,2,5,2,'通天塔',NULL,NULL,'text',0,'2025-07-15 11:01:38'),

(17,1,3,5,'嘻嘻嘻',NULL,NULL,'text',1,'2025-07-15 11:11:10'),

(18,1,3,5,'吞吞吐吐',NULL,NULL,'text',1,'2025-07-15 14:24:40'),

(19,1,3,5,'/storage/messages/20250715/8b2c54efa206fc21d09693bd7f1442a5.jpg',NULL,NULL,'image',1,'2025-07-15 14:24:52'),

(20,3,6,5,'hi',NULL,NULL,'text',0,'2025-07-17 13:57:14'),

(21,4,7,6,'你好呀',NULL,NULL,'text',1,'2025-07-18 13:56:07'),

(22,4,6,7,'....',NULL,NULL,'text',1,'2025-07-18 14:23:52'),

(23,4,6,7,'000',NULL,NULL,'text',1,'2025-07-18 16:33:54'),

(24,4,7,6,'666',NULL,NULL,'text',1,'2025-07-18 16:44:40'),

(25,4,6,7,'1111',NULL,NULL,'text',1,'2025-07-18 16:47:58'),

(26,4,7,6,'666',NULL,NULL,'text',1,'2025-07-18 17:09:05'),

(27,4,6,7,'6666',NULL,NULL,'text',1,'2025-07-18 17:19:18'),

(28,4,7,6,'嗨',NULL,NULL,'text',1,'2025-07-21 10:21:11');

/*Table structure for table `opd_private_message_delete` */

DROP TABLE IF EXISTS `opd_private_message_delete`;

CREATE TABLE `opd_private_message_delete` (
  `conversation_id` int(11) NOT NULL COMMENT '会话id',
  `message_id` int(11) NOT NULL COMMENT '消息id opd_private_message',
  `user_id` int(11) NOT NULL COMMENT '用户id',
  `delete_time` datetime DEFAULT NULL COMMENT '删除时间',
  KEY `idx_message_user` (`message_id`,`user_id`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='私信消息删除表';

/*Data for the table `opd_private_message_delete` */

/*Table structure for table `opd_product` */

DROP TABLE IF EXISTS `opd_product`;

CREATE TABLE `opd_product` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '产品名称',
  `title` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '标题',
  `seo_url` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `description` text COLLATE utf8mb4_unicode_ci COMMENT '描述',
  `content` text COLLATE utf8mb4_unicode_ci COMMENT '产品详情',
  `image` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '产品图片',
  `image_detail` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '详情图',
  `image_background` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `image_background_smt` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `price` varchar(10) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '产品价格',
  `testimonial` text COLLATE utf8mb4_unicode_ci COMMENT '客户感言',
  `case` text COLLATE utf8mb4_unicode_ci,
  `is_recommend` tinyint(1) DEFAULT '0' COMMENT '是否推荐 0否 1是',
  `manager_ids` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '产品经理ids',
  `seo_title` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `seo_description` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `seo_keywords` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` datetime DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='产品表';

/*Data for the table `opd_product` */

insert  into `opd_product`(`id`,`name`,`title`,`seo_url`,`description`,`content`,`image`,`image_detail`,`image_background`,`image_background_smt`,`price`,`testimonial`,`case`,`is_recommend`,`manager_ids`,`seo_title`,`seo_description`,`seo_keywords`,`create_time`,`update_time`) values 

(1,'OpenDEL™ Kit','Starting a journey to access the vast DEL space','opendel-kit','<div><div>HitGen provides an OpenDEL kit containing 50 libraries and 3 billion compounds, primers&nbsp;and standard selection protocol. Drug developers could perform affinity selection and&nbsp;NGS according to the manual</div><div><div><div>Clients have the option to let HitGen to do the downstream experiments from selection to&nbsp;off-DNA synthesis.Clients have the option to let HitGen to do the downstream experiments&nbsp;from selection to&nbsp;off-DNA synthesis.</div></div></div></div>','<dl class=\"text-sm leading-[1.5] mb-1.5 md:text-xl md:mb-5\"><dt class=\"font-bold text-md mb-1.5 md:text-xl\"><p>The Kit</p></dt><dd><p>50 Libraries</p></dd><dd><p>3Bn compounds</p></dd><dd><p>10 DEL samples</p></dd></dl><dl class=\"text-sm leading-[1.5] mb-1.5 md:text-xl md:mb-5\"><dt class=\"font-bold text-md mb-1.5 md:text-xl\"><p>To Access</p></dt><dd><p>Fully enumerated molecules</p></dd><dd><p>Building Block Structures</p></dd><dd><p>DNA Codon Sequences</p></dd><dd><p>Scaffolds Information</p></dd></dl><dl class=\"text-sm leading-[1.5] mb-1.5 md:text-xl md:mb-5\"><dt class=\"font-bold text-md mb-1.5 md:text-xl\"><p>No Structure Disclosure Fee</p></dt></dl><dl class=\"text-sm leading-[1.5] mb-1.5 md:text-xl md:mb-5\"><dt class=\"font-bold text-md mb-1.5 md:text-xl\"><p>No Structure Disclosure Fee</p></dt></dl>','/storage/20250516/93f702e0d04e0bc6723a3a8014f5eb70.jpg','/storage/20250521/f5b8358a102cac807172f5005b2936f5.png','/storage/20250625/44a48b85688b2c14e9b96c902c8af02c.jpg','/storage/20250625/0c995f1d9b515a64ecaa34f1bb1e1b99.jpg','111','[[\"\\/storage\\/20250521\\/7e6c37d204c955ca67d6ea4f08f436be.jpg\"],[\"\\/storage\\/20250521\\/b7a527900fba875aca3d7b958a5614a2.jpg\"],[\"\\/storage\\/20250521\\/67d12bc0f85304dad6b5bbb703f33a13.jpg\"]]','[[\"\\/storage\\/20250521\\/dc5105181b82d560b194b7d1fc9348d8.jpg\"],[\"\\/storage\\/20250521\\/27b7d66529c9aef55dd6be15f5b14d4b.jpg\"],[\"\\/storage\\/20250521\\/206d0e7645e1170db837b34287afbe4f.jpg\"]]',1,'4',NULL,NULL,NULL,'2025-05-20 17:28:07','2025-06-09 08:56:59'),

(2,'OpenDEL™ Kit II','Starting a journey to access the vast DEL space','opendel-kit-ii','<div><div>HitGen provides an OpenDEL kit containing 50 libraries and 3 billion compounds, primers&nbsp;and standard selection protocol. Drug developers could perform affinity selection and&nbsp;NGS according to the manual</div><div><div><div>Clients have the option to let HitGen to do the downstream experiments from selection to&nbsp;off-DNA synthesis.Clients have the option to let HitGen to do the downstream experiments&nbsp;from selection to&nbsp;off-DNA synthesis.</div></div></div></div>','<dl class=\"text-sm leading-[1.5] mb-1.5 md:text-xl md:mb-5\"><dt class=\"font-bold text-md mb-1.5 md:text-xl\"><p>The Kit</p></dt><dd><p>50 Libraries</p></dd><dd><p>3Bn compounds</p></dd><dd><p>10 DEL samples</p></dd></dl><dl class=\"text-sm leading-[1.5] mb-1.5 md:text-xl md:mb-5\"><dt class=\"font-bold text-md mb-1.5 md:text-xl\"><p>To Access</p></dt><dd><p>Fully enumerated molecules</p></dd><dd><p>Building Block Structures</p></dd><dd><p>DNA Codon Sequences</p></dd><dd><p>Scaffolds Information</p></dd></dl><dl class=\"text-sm leading-[1.5] mb-1.5 md:text-xl md:mb-5\"><dt class=\"font-bold text-md mb-1.5 md:text-xl\"><p>No Structure Disclosure Fee</p></dt></dl><dl class=\"text-sm leading-[1.5] mb-1.5 md:text-xl md:mb-5\"><dt class=\"font-bold text-md mb-1.5 md:text-xl\"><p>No Structure Disclosure Fee</p></dt></dl>','/storage/20250516/93f702e0d04e0bc6723a3a8014f5eb70.jpg','/storage/20250521/f5b8358a102cac807172f5005b2936f5.png','/storage/20250625/44a48b85688b2c14e9b96c902c8af02c.jpg','/storage/20250625/0c995f1d9b515a64ecaa34f1bb1e1b99.jpg','','[[\"\\/storage\\/20250521\\/7e6c37d204c955ca67d6ea4f08f436be.jpg\"],[\"\\/storage\\/20250521\\/b7a527900fba875aca3d7b958a5614a2.jpg\"],[\"\\/storage\\/20250521\\/67d12bc0f85304dad6b5bbb703f33a13.jpg\"]]','[[\"\\/storage\\/20250521\\/dc5105181b82d560b194b7d1fc9348d8.jpg\"],[\"\\/storage\\/20250521\\/27b7d66529c9aef55dd6be15f5b14d4b.jpg\"],[\"\\/storage\\/20250521\\/206d0e7645e1170db837b34287afbe4f.jpg\"]]',0,'4','','','','2025-04-29 14:51:17','2025-06-18 17:22:11'),

(3,'OpenDEL™ Kit III','Starting a journey to access the vast DEL space','opendel-kit-iii','<div><div>HitGen provides an OpenDEL kit containing 50 libraries and 3 billion compounds, primers&nbsp;and standard selection protocol. Drug developers could perform affinity selection and&nbsp;NGS according to the manual</div><div><div><div>Clients have the option to let HitGen to do the downstream experiments from selection to&nbsp;off-DNA synthesis.Clients have the option to let HitGen to do the downstream experiments&nbsp;from selection to&nbsp;off-DNA synthesis.</div></div></div></div>','<dl class=\"text-sm leading-[1.5] mb-1.5 md:text-xl md:mb-5\"><dt class=\"font-bold text-md mb-1.5 md:text-xl\"><p>The Kit</p></dt><dd><p>50 Libraries</p></dd><dd><p>3Bn compounds</p></dd><dd><p>10 DEL samples</p></dd></dl><dl class=\"text-sm leading-[1.5] mb-1.5 md:text-xl md:mb-5\"><dt class=\"font-bold text-md mb-1.5 md:text-xl\"><p>To Access</p></dt><dd><p>Fully enumerated molecules</p></dd><dd><p>Building Block Structures</p></dd><dd><p>DNA Codon Sequences</p></dd><dd><p>Scaffolds Information</p></dd></dl><dl class=\"text-sm leading-[1.5] mb-1.5 md:text-xl md:mb-5\"><dt class=\"font-bold text-md mb-1.5 md:text-xl\"><p>No Structure Disclosure Fee</p></dt></dl><dl class=\"text-sm leading-[1.5] mb-1.5 md:text-xl md:mb-5\"><dt class=\"font-bold text-md mb-1.5 md:text-xl\"><p>No Structure Disclosure Fee</p></dt></dl>','/storage/20250516/93f702e0d04e0bc6723a3a8014f5eb70.jpg','/storage/20250521/f5b8358a102cac807172f5005b2936f5.png','/storage/20250625/948e9acc19da6f7bfd4b5d0a2e43e1e4.jpg','/storage/20250625/d3d3f0c138290a035c9553da4d49de77.jpg','','[[\"\\/storage\\/20250521\\/7e6c37d204c955ca67d6ea4f08f436be.jpg\"],[\"\\/storage\\/20250521\\/b7a527900fba875aca3d7b958a5614a2.jpg\"],[\"\\/storage\\/20250521\\/67d12bc0f85304dad6b5bbb703f33a13.jpg\"]]','[[\"\\/storage\\/20250521\\/dc5105181b82d560b194b7d1fc9348d8.jpg\"],[\"\\/storage\\/20250521\\/27b7d66529c9aef55dd6be15f5b14d4b.jpg\"],[\"\\/storage\\/20250521\\/206d0e7645e1170db837b34287afbe4f.jpg\"]]',0,'4','','','','2025-04-29 14:51:23','2025-07-16 11:52:38');

/*Table structure for table `opd_product_relation` */

DROP TABLE IF EXISTS `opd_product_relation`;

CREATE TABLE `opd_product_relation` (
  `product_id` int(11) NOT NULL COMMENT '产品ID',
  `related_id` int(11) NOT NULL COMMENT '关联ID',
  `sort` tinyint(1) DEFAULT NULL,
  `type` tinyint(1) DEFAULT NULL COMMENT '1产品-服务 2产品-产品 3产品-资源 4服务-资源'
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='相关产品表';

/*Data for the table `opd_product_relation` */

insert  into `opd_product_relation`(`product_id`,`related_id`,`sort`,`type`) values 

(4,4,2,4),

(4,2,1,4),

(1,2,2,3),

(1,4,1,3),

(1,1,1,1),

(1,2,2,1),

(1,3,3,1),

(1,4,4,1),

(1,3,1,2),

(3,1,1,2),

(1,2,2,2),

(2,1,2,2),

(1,3,1,4),

(1,4,2,4),

(1,3,3,3),

(2,1,1,3),

(2,3,2,3),

(3,3,1,3),

(3,10,2,3),

(3,11,3,3),

(3,9,4,3),

(3,2,1,4),

(3,4,2,4),

(2,3,1,4),

(2,2,2,4),

(2,3,1,1),

(3,1,1,1),

(3,2,2,1),

(2,1,2,1),

(2,2,3,1),

(2,4,4,1);

/*Table structure for table `opd_progress` */

DROP TABLE IF EXISTS `opd_progress`;

CREATE TABLE `opd_progress` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `main_id` int(11) DEFAULT NULL COMMENT '产品id，服务id',
  `type` tinyint(1) DEFAULT NULL COMMENT '0产品 1服务',
  `name` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '状态',
  `content` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '状态说明',
  `sort` int(2) DEFAULT '50' COMMENT '排序',
  PRIMARY KEY (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=25 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

/*Data for the table `opd_progress` */

insert  into `opd_progress`(`id`,`main_id`,`type`,`name`,`content`,`sort`) values 

(1,1,0,'Awaiting shipment','The order awaiting shipment',1),

(2,1,0,'Has been sent','The order has been shipped. Please check the logistics information.',3),

(4,1,0,'Delivered','The order has delivered',4),

(7,2,1,'Waiting for samples','Service Waiting for samples',1),

(8,2,1,'Starting Sequencing','Service Starting Sequencing',2),

(9,2,1,'Completed','Service Completed',3),

(10,3,1,'Scenario confirmation','Service access Scenario confirmation',1),

(11,3,1,'In Progress','Service access has been enabled. You can download materials and upload reports now.',2),

(12,3,1,'Completed','Service access has been Completed',3),

(13,2,0,'Awaiting shipment','The order awaiting shipment',50),

(14,2,0,'Has been sent','The order has been shipped. Please check the logistics information.',50),

(15,2,0,'Delivered','The order has delivered',50),

(16,1,1,'Waiting for Samples','Service Waiting for Samples',1),

(17,1,1,'Starting','Service Starting',2),

(18,1,1,'Finish','Service access has been enabled. You can download materials and upload reports now.',3),

(19,4,1,'Waiting for Samples','Service access has been enabled. You can download materials and upload reports now.',1),

(20,4,1,'Starting','Service access has been enabled. You can download materials and upload reports now.',2),

(21,4,1,'Finish','Service access has been enabled. You can download materials and upload reports now.',3),

(22,3,0,'Awaiting shipment','',1),

(23,3,0,'Has been sent','',2),

(24,3,0,'Delivered','',3);

/*Table structure for table `opd_quote` */

DROP TABLE IF EXISTS `opd_quote`;

CREATE TABLE `opd_quote` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL COMMENT '用户id opd_users',
  `email` varchar(30) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `first_name` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `last_name` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `country` varchar(30) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `organization` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `title` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `phone` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `express_address` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `city` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `express_postcode` varchar(30) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `product_id` int(11) DEFAULT '0' COMMENT '产品id opd_product',
  `service_id` int(11) DEFAULT '0' COMMENT '服务id opd_service',
  `message` text COLLATE utf8mb4_unicode_ci,
  `ip` varchar(30) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `is_transform` tinyint(1) DEFAULT '0' COMMENT '0未转化 1已转化',
  `create_time` datetime DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=13 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单表';

/*Data for the table `opd_quote` */

insert  into `opd_quote`(`id`,`user_id`,`email`,`first_name`,`last_name`,`country`,`organization`,`title`,`phone`,`express_address`,`city`,`express_postcode`,`product_id`,`service_id`,`message`,`ip`,`is_transform`,`create_time`,`update_time`) values 

(1,NULL,'<EMAIL>','wu','xxx','China','yidian','www','','','','',1,3,'ccccc','127.0.0.1',0,'2025-05-22 09:15:03',NULL),

(2,3,'<EMAIL>','Jevons','Wang','China','HT','',NULL,NULL,NULL,NULL,2,0,NULL,'**************',0,'2025-05-28 16:46:33',NULL),

(3,6,'<EMAIL>','mo','zi','China','12346','','17891912921','','','',2,0,NULL,'**************',0,'2025-07-17 14:21:06',NULL),

(4,7,'<EMAIL>','fu','ke','China','del','',NULL,NULL,NULL,NULL,1,0,NULL,'**************',0,'2025-07-18 10:45:08',NULL),

(5,7,'<EMAIL>','fu','ke','China','del','',NULL,NULL,NULL,NULL,2,0,NULL,'**************',0,'2025-07-18 10:46:00',NULL),

(6,7,'<EMAIL>','fu','ke','China','del','',NULL,NULL,NULL,NULL,2,0,NULL,'**************',0,'2025-07-18 10:46:51',NULL),

(7,7,'<EMAIL>','fu','ke','China','del','',NULL,NULL,NULL,NULL,2,0,NULL,'**************',0,'2025-07-18 10:48:13',NULL),

(8,7,'<EMAIL>','fu','ke','China','del','',NULL,NULL,NULL,NULL,2,1,NULL,'**************',0,'2025-07-18 10:55:05',NULL),

(9,7,'<EMAIL>','fu','ke','China','del','',NULL,NULL,NULL,NULL,3,2,NULL,'**************',0,'2025-07-18 10:55:16',NULL),

(10,7,'<EMAIL>','fu','ke','China','del','',NULL,NULL,NULL,NULL,2,1,NULL,'**************',0,'2025-07-18 10:56:52',NULL),

(11,7,'<EMAIL>','fu','ke','China','del','','18521366550','','','',1,2,NULL,'180.158.246.77',0,'2025-07-18 17:13:05',NULL),

(12,6,'<EMAIL>','mo','zi','China','12346','','17891912921','','','',2,0,NULL,'180.158.246.77',0,'2025-07-21 16:51:38',NULL);

/*Table structure for table `opd_resource` */

DROP TABLE IF EXISTS `opd_resource`;

CREATE TABLE `opd_resource` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `category_id` int(11) DEFAULT NULL COMMENT '分类id opd_resource_category',
  `name` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '资源名称',
  `file` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '文件',
  `file_name` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '文件名称',
  `create_time` datetime DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=12 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文件资源表';

/*Data for the table `opd_resource` */

insert  into `opd_resource`(`id`,`category_id`,`name`,`file`,`file_name`,`create_time`,`update_time`) values 

(1,1,'Latest Progress of DELLatest Progress of DELLof  222','/storage/resource/20250521/f5dc5b608d99ba003741e5bedc662eaf.docx','OPD社区网站内容V1.docx','2025-05-15 16:48:29','2025-06-20 13:30:00'),

(2,1,'Latest Progress of DELLatest Progress of DELLof 11','/storage/resource/20250521/f5dc5b608d99ba003741e5bedc662eaf.docx','OPD社区网站内容V1.docx','2025-05-15 16:48:29','2025-06-20 13:29:52'),

(3,1,'Latest Progress of DELLatest 111','/storage/resource/20250521/f5dc5b608d99ba003741e5bedc662eaf.docx','OPD社区网站内容V1.docx','2025-05-15 16:48:29','2025-06-20 13:29:46'),

(4,1,'Latest Progress 111','/storage/resource/20250521/f5dc5b608d99ba003741e5bedc662eaf.docx','OPD社区网站内容V1.docx','2025-05-15 16:48:29','2025-06-20 13:29:14'),

(5,1,'Latest Progress of DELLatest Progress of DELLof  DELLatest ','/storage/resource/20250521/f5dc5b608d99ba003741e5bedc662eaf.docx','OPD社区网站内容V1.docx','2025-05-15 16:48:29','2025-06-20 13:28:53'),

(6,2,'Latest Progress of DELLatest Progress of DELLof  ','/storage/resource/20250521/f5dc5b608d99ba003741e5bedc662eaf.docx','OPD社区网站内容V1.docx','2025-05-15 16:48:29','2025-06-20 13:28:46'),

(7,2,'Latest Progress of DELLatest Progress of ','/storage/resource/20250521/f5dc5b608d99ba003741e5bedc662eaf.docx','OPD社区网站内容V1.docx','2025-05-15 16:48:29','2025-06-20 13:28:39'),

(8,2,'Latest Progress of DELLatest Progress ','/storage/resource/20250521/f5dc5b608d99ba003741e5bedc662eaf.docx','OPD社区网站内容V1.docx','2025-05-15 16:48:29','2025-06-20 13:28:33'),

(9,2,'Latest Progress of DELLatest ','/storage/resource/20250521/f5dc5b608d99ba003741e5bedc662eaf.docx','OPD社区网站内容V1.docx','2025-05-15 16:48:29','2025-06-20 13:28:20'),

(10,2,'Latest Progress ','/storage/resource/20250521/f5dc5b608d99ba003741e5bedc662eaf.docx','OPD社区网站内容V1.docx','2025-05-15 16:48:29','2025-06-20 13:28:12'),

(11,2,'Latest','/storage/resource/20250521/f5dc5b608d99ba003741e5bedc662eaf.docx','OPD社区网站内容V1.docx','2025-05-15 16:48:29','2025-06-20 13:28:05');

/*Table structure for table `opd_resource_category` */

DROP TABLE IF EXISTS `opd_resource_category`;

CREATE TABLE `opd_resource_category` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(30) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `icon` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` datetime DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='资源分类表';

/*Data for the table `opd_resource_category` */

insert  into `opd_resource_category`(`id`,`name`,`icon`,`create_time`,`update_time`) values 

(1,'Open color page','/storage/20250515/71ae339dd8b30ed4d949845beaf92f02.png','2025-05-13 17:08:45','2025-05-15 16:42:19'),

(2,'Open data','/storage/20250515/9646691b97f6b84363595dc694dac6ba.png','2025-05-13 17:09:01','2025-05-15 16:42:07'),

(3,'White papers',NULL,'2025-06-10 16:27:10',NULL),

(4,'Webinar',NULL,'2025-06-10 16:27:38',NULL);

/*Table structure for table `opd_role_backend_menus` */

DROP TABLE IF EXISTS `opd_role_backend_menus`;

CREATE TABLE `opd_role_backend_menus` (
  `role_id` int(11) NOT NULL COMMENT '角色ID',
  `menu_id` int(11) NOT NULL COMMENT '菜单ID',
  `state` tinyint(1) DEFAULT '1' COMMENT '状态(0:未选中,1:选中,2:半选中)',
  UNIQUE KEY `idx_role_menu` (`role_id`,`menu_id`),
  KEY `idx_menu_id` (`menu_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色菜单关联表';

/*Data for the table `opd_role_backend_menus` */

insert  into `opd_role_backend_menus`(`role_id`,`menu_id`,`state`) values 

(1,1,1),

(1,2,1),

(1,3,1),

(1,4,1),

(1,5,1),

(1,7,1),

(1,8,1),

(1,9,1),

(1,10,1),

(1,11,1),

(1,12,1),

(1,13,1),

(1,14,1),

(1,15,1),

(1,16,1),

(1,17,1),

(1,18,1),

(1,19,1),

(1,20,1),

(1,21,1),

(1,22,1),

(1,23,1),

(1,24,1),

(1,25,1),

(1,26,1),

(1,27,1),

(1,28,1),

(1,29,1),

(1,30,1),

(1,31,1),

(1,32,1),

(1,33,1),

(1,34,1),

(1,35,1),

(1,36,1),

(1,37,1),

(1,38,1),

(1,39,1),

(1,40,1),

(1,41,1),

(1,42,1),

(1,43,1),

(1,44,1),

(1,45,1),

(1,46,1),

(1,47,1),

(1,48,1),

(1,49,1),

(1,50,1),

(1,51,1),

(2,1,1),

(2,2,1),

(2,3,0),

(2,4,0),

(2,5,0),

(2,7,0),

(2,8,0),

(2,9,0),

(2,10,0),

(2,11,0),

(2,12,0),

(2,13,0),

(2,14,0),

(2,16,0),

(2,17,0),

(2,18,0),

(2,19,0),

(2,20,0),

(2,21,0),

(2,22,0),

(2,23,0),

(2,24,0),

(2,25,0),

(2,26,0),

(2,27,0),

(2,28,0),

(2,29,0),

(2,30,0),

(2,31,0),

(2,32,0),

(2,33,0),

(2,34,0),

(2,37,2),

(2,38,0),

(2,39,0),

(2,40,0),

(2,41,0),

(3,1,0),

(3,2,0),

(3,3,0),

(3,4,0),

(3,5,0),

(3,7,0),

(3,8,0),

(3,9,0),

(3,10,0),

(3,11,0),

(3,12,0),

(3,13,0),

(3,14,0),

(3,15,0),

(3,16,0),

(3,17,0),

(3,18,0),

(3,19,0),

(3,20,0),

(3,21,0),

(3,22,0),

(3,23,0),

(3,24,0),

(3,25,0),

(3,26,0),

(3,27,0),

(3,28,0),

(3,29,0),

(3,30,0),

(3,31,0),

(3,32,0),

(3,33,0),

(3,34,0),

(3,35,0),

(3,36,0),

(3,37,0),

(3,38,0),

(3,39,0),

(3,40,0),

(3,41,0);

/*Table structure for table `opd_role_backend_permissions` */

DROP TABLE IF EXISTS `opd_role_backend_permissions`;

CREATE TABLE `opd_role_backend_permissions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `role_id` int(11) NOT NULL COMMENT '角色ID',
  `permission_id` int(11) NOT NULL COMMENT '权限ID',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_role_permission` (`role_id`,`permission_id`),
  KEY `idx_permission_id` (`permission_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色权限关联表';

/*Data for the table `opd_role_backend_permissions` */

/*Table structure for table `opd_roles` */

DROP TABLE IF EXISTS `opd_roles`;

CREATE TABLE `opd_roles` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL COMMENT '角色名称',
  `description` varchar(255) DEFAULT NULL COMMENT '角色描述',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态(0:禁用,1:正常)',
  `is_admin` tinyint(1) DEFAULT '0' COMMENT '是否后台用户(0:否,1:是)',
  `money` varchar(10) DEFAULT NULL COMMENT '升级金额',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_name` (`name`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COMMENT='角色表';

/*Data for the table `opd_roles` */

insert  into `opd_roles`(`id`,`name`,`description`,`status`,`is_admin`,`money`,`create_time`,`update_time`) values 

(1,'超级管理员','拥有系统所有权限',1,1,NULL,'2025-06-09 13:44:10','2025-06-09 15:39:46'),

(2,'产品经理',NULL,1,1,NULL,'2025-06-09 16:54:59','2025-06-09 16:55:05'),

(3,'技术支持',NULL,1,0,NULL,'2025-06-09 16:55:03','2025-06-09 16:55:03'),

(4,'初级用户','',1,0,'0','2025-06-10 11:19:02','2025-06-10 11:21:52'),

(5,'中级用户','',1,0,'20000','2025-06-10 11:19:07','2025-06-10 11:22:05'),

(6,'高级用户','',1,0,'80000','2025-06-10 11:19:11','2025-06-10 11:22:14');

/*Table structure for table `opd_service` */

DROP TABLE IF EXISTS `opd_service`;

CREATE TABLE `opd_service` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(30) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '服务名称',
  `title` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '标题',
  `seo_url` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `description` text COLLATE utf8mb4_unicode_ci COMMENT '服务描述',
  `description2` text COLLATE utf8mb4_unicode_ci COMMENT '服务描述',
  `content` text COLLATE utf8mb4_unicode_ci COMMENT '服务详情',
  `image` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `image_background` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `image_background_smt` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `price` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '服务价格',
  `progress_describe` text COLLATE utf8mb4_unicode_ci COMMENT '流程说明',
  `note` text COLLATE utf8mb4_unicode_ci COMMENT '上传资料说明',
  `manager_ids` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '产品经理ids',
  `seo_title` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `seo_description` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `seo_keywords` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `create_time` datetime DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='服务表';

/*Data for the table `opd_service` */

insert  into `opd_service`(`id`,`name`,`title`,`seo_url`,`description`,`description2`,`content`,`image`,`image_background`,`image_background_smt`,`price`,`progress_describe`,`note`,`manager_ids`,`seo_title`,`seo_description`,`seo_keywords`,`create_time`,`update_time`) values 

(1,'OpenDEL™ Screening','','opendel-screening','<div>OpenDEL™ screening is carried out by our team of experienced professionals, proficient in handling over 50 different target&nbsp;types including protein-protein interactions, kinases,&nbsp;enzymes, transcription factors, and RNA targets. Our team&nbsp;typically completes the screening experiments within 1-2&nbsp;weeks. For more information and to explore detailed stories&nbsp;about our featured target types, click on the images below. We&nbsp;plan to release similar articles for other target classes in&nbsp;the future.</div>','<p class=\"MsoNormal\"><span lang=\"EN-US\" style=\"font-family: \'Helvetica\',sans-serif; color: #333333;\">OpenDEL&trade; screening is carried out by our team of experienced professionals, proficient in handling over 50 different target types including protein-protein interactions, kinases, enzymes, transcription factors, and RNA targets. Our team typically completes the screening experiments within 1-2 weeks. For more information and to explore detailed stories about our featured target types, click on the images below. We plan to release similar articles for other target classes in the future.</span></p>','<p>DNA Encoded Library Screening (also known as DEL selection) is an affinity selection process. DEL screening has many advantages over traditional approaches: 1) easily accesses trillions of physically existing compounds; 2) saving in protein/target consumption; 3) short duration in hit identification and validation; 4) more cost effective.&nbsp;</p><p>OpenDEL™ screening is carried out by our team of experienced professionals, proficient in handling over 50 different target types including protein-protein interactions, kinases, enzymes, transcription factors, and RNA targets. HitGen‘s team typically completes the screening experiments within 1-2 weeks. For more information and to explore detailed stories about our featured target types, click on the images below. We plan to release similar articles for other target classes in the future.</p><p><img src=\"../../tinymce/20250521/829408dcfe05117a941b328664a626bb.jpg\" alt=\"\" width=\"1660\" height=\"803\"></p>','/storage/20250516/02b02fe414a98ec33d7f63c01b5d2447.jpg','/storage/20250625/6fcf58dfa9b6fdbf6eae503474d1d3ef.jpg','/storage/20250625/0c995f1d9b515a64ecaa34f1bb1e1b99.jpg',NULL,'','',NULL,'','','','2025-05-13 11:54:25','2025-06-25 09:55:41'),

(2,'OpenDEL™ Sequencing','','opendel-sequencing','<div><div>HitGen offers high-quality sequencing services powered by two Illumina platforms (NextSeq2000 and NovaSeq6000) and one MGI platform (MGISEQ-2000), supported by 8 versatile flow cell types. Our flexible solutions cover a wide range of sequencing capacities, from 400M to 10,000M reads, tailored to your specific needs. With an average Q30 score exceeding 92% and a turnaround time of under two days, we deliver both speed and reliability for your research. In addition to DNA-encoded library (DEL) sequencing, we also provide multi-omics sequencing and data analysis services.</div></div>','<p class=\"MsoNormal\"><strong><span lang=\"EN-US\">HitGen&rsquo;s <u>GOLD</u> sequencing &amp; data analysis service</span></strong></p>\r\n<p class=\"MsoNormal\">&nbsp;</p>\r\n<p style=\"margin: 0cm; line-height: 25%;\"><strong><u><span lang=\"EN-US\" style=\"line-height: 25%; font-family: \'Calibri\',sans-serif; mso-ascii-theme-font: minor-latin; mso-fareast-font-family: 宋体; mso-fareast-theme-font: minor-fareast; mso-bidi-font-family: \'Times New Roman\'; mso-bidi-theme-font: minor-bidi; color: #c8a700; mso-font-kerning: 12.0pt;\">G</span></u></strong><strong><u><span lang=\"EN-US\" style=\"line-height: 25%; font-family: \'Calibri\',sans-serif; mso-ascii-theme-font: minor-latin; mso-fareast-font-family: 宋体; mso-fareast-theme-font: minor-fareast; mso-bidi-font-family: \'Times New Roman\'; mso-bidi-theme-font: minor-bidi; color: black; mso-themecolor: text1; mso-font-kerning: 12.0pt;\">lobal sample shipment</span></u></strong></p>\r\n<p style=\"margin: 0cm; line-height: 25%;\">&nbsp;</p>\r\n<p style=\"margin: 0cm; line-height: 25%;\">&nbsp;</p>\r\n<p style=\"margin: 0cm; line-height: 25%;\">&nbsp;</p>\r\n<p style=\"margin: 0cm; line-height: 25%;\"><span lang=\"EN-US\" style=\"line-height: 25%; font-family: \'Calibri\',sans-serif; mso-ascii-theme-font: minor-latin; mso-fareast-font-family: 宋体; mso-fareast-theme-font: minor-fareast; mso-bidi-font-family: \'Times New Roman\'; mso-bidi-theme-font: minor-bidi; color: black; mso-themecolor: text1; mso-font-kerning: 12.0pt;\">Shipping samples to HitGen via Hub.</span></p>\r\n<p style=\"margin: 0cm; line-height: 25%;\">&nbsp;</p>\r\n<p style=\"margin: 0cm; line-height: 25%;\">&nbsp;</p>\r\n<p style=\"margin: 0cm; line-height: 25%;\">&nbsp;</p>\r\n<p style=\"margin: 0cm; line-height: 25%;\"><span lang=\"EN-US\" style=\"line-height: 25%; font-family: \'Calibri\',sans-serif; mso-ascii-theme-font: minor-latin; mso-fareast-font-family: 宋体; mso-fareast-theme-font: minor-fareast; mso-bidi-font-family: \'Times New Roman\'; mso-bidi-theme-font: minor-bidi; color: black; mso-themecolor: text1; mso-font-kerning: 12.0pt;\">Average 4-7 days to receive DEL samples</span></p>\r\n<p style=\"margin: 0cm; line-height: 25%;\">&nbsp;</p>\r\n<p style=\"margin: 0cm; line-height: 25%;\">&nbsp;</p>\r\n<p style=\"margin: 0cm; line-height: 25%;\">&nbsp;</p>\r\n<p style=\"margin: 0cm;\"><strong><u><span lang=\"EN-US\" style=\"font-family: \'Calibri\',sans-serif; mso-ascii-theme-font: minor-latin; mso-fareast-font-family: 宋体; mso-fareast-theme-font: minor-fareast; mso-bidi-font-family: \'Times New Roman\'; mso-bidi-theme-font: minor-bidi; color: #c8a700; mso-font-kerning: 12.0pt;\">O</span></u></strong><strong><u><span lang=\"EN-US\" style=\"font-family: \'Calibri\',sans-serif; mso-ascii-theme-font: minor-latin; mso-fareast-font-family: 宋体; mso-fareast-theme-font: minor-fareast; mso-bidi-font-family: \'Times New Roman\'; mso-bidi-theme-font: minor-bidi; color: black; mso-themecolor: text1; mso-font-kerning: 12.0pt;\">utstanding sequencing quality</span></u></strong></p>\r\n<p style=\"margin: 0cm;\"><span lang=\"EN-US\" style=\"font-family: \'Calibri\',sans-serif; mso-ascii-theme-font: minor-latin; mso-fareast-font-family: 宋体; mso-fareast-theme-font: minor-fareast; mso-bidi-font-family: \'Times New Roman\'; mso-bidi-theme-font: minor-bidi; color: black; mso-themecolor: text1; mso-font-kerning: 12.0pt;\">Average Q30 &gt; 92%</span></p>\r\n<p style=\"margin: 0cm;\"><strong><u><span lang=\"EN-US\" style=\"font-family: \'Calibri\',sans-serif; mso-ascii-theme-font: minor-latin; mso-fareast-font-family: 宋体; mso-fareast-theme-font: minor-fareast; mso-bidi-font-family: \'Times New Roman\'; mso-bidi-theme-font: minor-bidi; color: #c8a700; mso-font-kerning: 12.0pt;\">L</span></u></strong><strong><u><span lang=\"EN-US\" style=\"font-family: \'Calibri\',sans-serif; mso-ascii-theme-font: minor-latin; mso-fareast-font-family: 宋体; mso-fareast-theme-font: minor-fareast; mso-bidi-font-family: \'Times New Roman\'; mso-bidi-theme-font: minor-bidi; color: black; mso-themecolor: text1; mso-font-kerning: 12.0pt;\">ightning-speed result delivery</span></u></strong></p>\r\n<p style=\"margin: 0cm;\"><span lang=\"EN-US\" style=\"font-family: \'Calibri\',sans-serif; mso-ascii-theme-font: minor-latin; mso-fareast-font-family: 宋体; mso-fareast-theme-font: minor-fareast; mso-bidi-font-family: \'Times New Roman\'; mso-bidi-theme-font: minor-bidi; color: black; mso-themecolor: text1; mso-font-kerning: 12.0pt;\">Average Sequencing + Data analysis &lt; 7 days</span></p>\r\n<p style=\"margin: 0cm;\"><strong><u><span lang=\"EN-US\" style=\"font-family: \'Calibri\',sans-serif; mso-ascii-theme-font: minor-latin; mso-fareast-font-family: 宋体; mso-fareast-theme-font: minor-fareast; mso-bidi-font-family: \'Times New Roman\'; mso-bidi-theme-font: minor-bidi; color: #c8a700; mso-font-kerning: 12.0pt;\">D</span></u></strong><strong><u><span lang=\"EN-US\" style=\"font-family: \'Calibri\',sans-serif; mso-ascii-theme-font: minor-latin; mso-fareast-font-family: 宋体; mso-fareast-theme-font: minor-fareast; mso-bidi-font-family: \'Times New Roman\'; mso-bidi-theme-font: minor-bidi; color: black; mso-themecolor: text1; mso-font-kerning: 12.0pt;\">iverse sequencing options</span></u></strong></p>\r\n<p style=\"margin: 0cm;\"><span lang=\"EN-US\" style=\"font-family: \'Calibri\',sans-serif; mso-ascii-theme-font: minor-latin; mso-fareast-font-family: 宋体; mso-fareast-theme-font: minor-fareast; mso-bidi-font-family: \'Times New Roman\'; mso-bidi-theme-font: minor-bidi; color: black; mso-themecolor: text1; mso-font-kerning: 12.0pt;\">Choose from a throughput range of 500 million to up to 10,000 million reads</span></p>','<h2>NGS flow chart</h2><p><img src=\"../../tinymce/20250521/fcedc63310230493eb14213d7becf604.jpg\" alt=\"\" width=\"1660\" height=\"560\"></p><h2>NGS capabilities</h2><p><img src=\"../../tinymce/20250521/b228890810ead136dc726170a01e4b37.jpg\" alt=\"\" width=\"1660\" height=\"320\"></p><h2>HitGen’s GOLD sequencing service</h2><p><img src=\"../../tinymce/20250521/aabd7e53549253ef2b30e82d7e7976a0.jpg\" alt=\"\" width=\"1660\" height=\"221\"></p>','/storage/20250516/39c44d427f1616f8e9e52d9d30396e95.jpg','/storage/20250625/38e1ee73509d7c89920a13f0afd8ceb2.jpg','/storage/20250625/0c995f1d9b515a64ecaa34f1bb1e1b99.jpg',NULL,'','',NULL,'','','','2025-05-13 11:55:09','2025-06-25 09:55:57'),

(3,'OpenDEL™ Hit Proposal','','opendel-hit-proposal','<div>Analyzing DEL selection data and choosing the right compounds for follow-up necessitates multidisciplinary expertise encompassing biology, computational science, and chemistry. This includes a deep understanding of the experimental design and mechanisms of action (MOAs) in biology, data processing and analysis in computational science, and aspects of both synthetic and DEL chemistry</div>','<p class=\"MsoNormal\"><span lang=\"EN-US\" style=\"font-family: \'Helvetica\',sans-serif; color: #333333;\">Analyzing DEL selection data and choosing the right compounds for follow-up necessitates multidisciplinary expertise encompassing biology, computational science, and chemistry. This includes a deep understanding of the experimental design and mechanisms of action (MOAs) in biology, data processing and analysis in computational science, and aspects of both synthetic and DEL chemistry</span></p>','<p>For hit proposal, we generally have the following considerations: 1) signal strength (sequence count, feature intensity), 2) chemotype diversity, 3) physchem properties, 4) structural relationship across different DELs and mechanism of action (signal comparison between samples). A representative hit proposal workflow is shown below.</p><p><img src=\"../../tinymce/20250521/a978ee35f567e7126059f0c5112bd6aa.jpg\" alt=\"\" width=\"1658\" height=\"818\"></p><p>HitGen is capable of industry leading synthesis with experienced chemists that have worked on over hundreds of projects and advanced equipment, instruments. Typically, 5 mg is offered for every compound and for up to 100 compounds, with 6~8 weeks lead time. Gram-scale of synthesis is also available as needed.&nbsp;</p>','/storage/20250516/de5507ce37536cb40208a70123a2e42e.jpg','/storage/20250625/c157d7dd167e69220a66dece5f5cdac6.jpg','/storage/20250625/0c995f1d9b515a64ecaa34f1bb1e1b99.jpg',NULL,'','','4','','','','2025-05-16 17:08:43','2025-07-16 13:16:19'),

(4,'OpenDEL™ Off-DNA Synthesis','','opendel-off-dna-synthesis','<p>Our traditional synthesis service excels at recreating complex chemical structures derived from various DELs. We offer diverse options, including traditional chemistry synthesis service and high-throughput synthesis service.&nbsp;<br><strong>A. Traditional Chemical Synthesis @ HitGen</strong>&nbsp;<br><strong>B. High Throughput Chemical Synthesis @ HitGen‘s</strong></p>','<p class=\"MsoNormal\"><span lang=\"EN-US\" style=\"font-family: \'Helvetica\',sans-serif; color: #333333;\">Our traditional synthesis service excels at recreating complex chemical structures derived from various DELs. We offer diverse options, including traditional chemistry synthesis service and high-throughput synthesis service.</span></p>\r\n<p class=\"MsoNormal\"><strong><span lang=\"EN-US\">A. Traditional Chemical Synthesis @ HitGen</span></strong></p>\r\n<p><strong><span lang=\"EN-US\" style=\"font-size: 10.5pt; mso-bidi-font-size: 11.0pt; font-family: \'Calibri\',sans-serif; mso-ascii-theme-font: minor-latin; mso-fareast-font-family: 宋体; mso-fareast-theme-font: minor-fareast; mso-hansi-theme-font: minor-latin; mso-bidi-font-family: \'Times New Roman\'; mso-bidi-theme-font: minor-bidi; mso-ansi-language: EN-US; mso-fareast-language: ZH-CN; mso-bidi-language: AR-SA;\">B. <u>H</u>igh <u>T</u>hroughput <u>C</u>hemical <u>S</u>ynthesis @ HitGen</span></strong></p>','<p>HitGen has more than 200 well trained chemists and over 10,000 square meters of lab space at our Chengdu site. We have heavy investment in lab instruments such as Mass Spec, NMR, UPLC, SFC, prep-HPLC, MPLC, Microwave reactors, etc. We use Scilligence ELN lab notebook and RegMol Compound Registration to guarantee data safety and customer remote access. HitGen synthetic chemistry group provide services by either Medicinal Chemistry Collaboration or a Stand-Alone compound synthesis.</p><p>Our traditional synthesis service excels at recreating complex chemical structures derived from various DELs. We offer diverse options, including traditional chemistry synthesis service and high-throughput synthesis service.</p><h3>HTCS Platform at HitGen</h3><p><img src=\"../../tinymce/20250521/c7e01329300e6c8d00e6718e01e16381.jpg\" alt=\"\" width=\"1658\" height=\"818\"></p>','/storage/20250516/4ab1cf25c7c475067798dfcf6b44233f.jpg','/storage/20250625/bc442cfc748d6fdb786a1e37f28e6c22.jpg','/storage/20250625/0c995f1d9b515a64ecaa34f1bb1e1b99.jpg',NULL,'','','4','OpenDEL™ Off-DNA Synthesis','','','2025-05-16 17:09:18','2025-07-16 13:14:50');

/*Table structure for table `opd_user` */

DROP TABLE IF EXISTS `opd_user`;

CREATE TABLE `opd_user` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `email` varchar(30) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '邮箱',
  `avatar` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '头像',
  `nickname` varchar(30) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '昵称',
  `password` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '登陆密码',
  `salt` varchar(10) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '加盐',
  `first_name` varchar(20) COLLATE utf8_unicode_ci DEFAULT NULL,
  `last_name` varchar(20) COLLATE utf8_unicode_ci DEFAULT NULL,
  `country` varchar(30) COLLATE utf8_unicode_ci DEFAULT NULL,
  `organization` varchar(100) COLLATE utf8_unicode_ci DEFAULT NULL,
  `title` varchar(100) COLLATE utf8_unicode_ci DEFAULT NULL,
  `phone` varchar(20) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '手机号码',
  `express_address` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `city` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `express_postcode` varchar(30) COLLATE utf8_unicode_ci DEFAULT NULL,
  `status` tinyint(1) DEFAULT '1' COMMENT '状态(0:禁用,1:正常)',
  `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(30) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '最后登录IP',
  `role_id` int(11) DEFAULT NULL COMMENT '角色id',
  `role_upgrade_time` datetime DEFAULT NULL COMMENT '升级时间',
  `points` varchar(10) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '用户积分',
  `create_time` datetime DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `email` (`email`),
  KEY `idx_email_status` (`email`,`status`)
) ENGINE=MyISAM AUTO_INCREMENT=8 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='用户基本信息表';

/*Data for the table `opd_user` */

insert  into `opd_user`(`id`,`email`,`avatar`,`nickname`,`password`,`salt`,`first_name`,`last_name`,`country`,`organization`,`title`,`phone`,`express_address`,`city`,`express_postcode`,`status`,`last_login_time`,`last_login_ip`,`role_id`,`role_upgrade_time`,`points`,`create_time`,`update_time`) values 

(2,'<EMAIL>',NULL,'test','25af0277c8f80853f7f95661c78a0f22','l0Mw','yi','dian','United States','test','66',NULL,NULL,NULL,NULL,1,NULL,NULL,1,NULL,NULL,'2025-04-27 14:17:34','2025-06-09 13:17:00'),

(3,'<EMAIL>','/storage/avatar/20250616/90adc32282e76d77b207a3bce1e2f6ac.jpg','wuxy','3d07e18ddbb3b19cc25aeaa94f57c519','dKrl','wu','xy','China','yidian','ttt','13677778888','111111','555555','88888',1,NULL,NULL,6,'2025-06-10 15:00:06','450','2025-05-19 17:01:44','2025-06-17 13:26:35'),

(4,'<EMAIL>',NULL,'wang','b6bb778d26086a066573c641e3471ae9','6klk','Jevons','Wang','China','HT','',NULL,NULL,NULL,NULL,1,NULL,NULL,2,NULL,NULL,'2025-05-28 15:50:52','2025-07-16 11:51:45'),

(5,'<EMAIL>','/storage/avatar/20250616/c6ec2b7fe52722b5046442bfae01bc23.jpg',NULL,'13502d284cd097cb643942340c07a116','x2RY','peijin','Li','China','HitGen','',NULL,NULL,NULL,NULL,1,NULL,NULL,4,NULL,'400','2025-06-25 16:18:02',NULL),

(6,'<EMAIL>','/storage/avatar/20250717/696df1a4849920dafd649d8d35babea5.jpg',NULL,'881b1aa0357563ca21053232300e9fa1','9XVI','mo','zi','China','12346','','17891912921','','','',1,NULL,NULL,4,NULL,'530','2025-07-17 11:47:51','2025-07-17 13:16:29'),

(7,'<EMAIL>','/storage/avatar/20250718/dcdbb38d9bdf86d765462b89b444843b.png',NULL,'dbe1c84db855cfd178a916ed60d48258','K03h','fu','ke','China','del','','18521366550','','','',1,NULL,NULL,5,'2025-07-18 11:11:48','350','2025-07-17 16:20:14','2025-07-18 10:58:51');

/*Table structure for table `opd_user_coupon` */

DROP TABLE IF EXISTS `opd_user_coupon`;

CREATE TABLE `opd_user_coupon` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `coupon_id` int(11) DEFAULT NULL,
  `user_id` int(11) DEFAULT NULL COMMENT '用户id',
  `is_used` tinyint(1) DEFAULT '0' COMMENT '0未使用 1已使用',
  `order_id` int(11) DEFAULT NULL COMMENT '订单id opd_order',
  `start_time` date DEFAULT NULL,
  `end_time` date DEFAULT NULL,
  `create_time` datetime DEFAULT NULL COMMENT '获取时间',
  `update_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户试用券';

/*Data for the table `opd_user_coupon` */

insert  into `opd_user_coupon`(`id`,`coupon_id`,`user_id`,`is_used`,`order_id`,`start_time`,`end_time`,`create_time`,`update_time`) values 

(1,1,3,0,NULL,'2025-05-26','2025-06-30','2025-06-13 17:05:11',NULL),

(2,2,2,0,NULL,'2025-05-26','2025-07-31','2025-06-16 09:39:49',NULL),

(3,2,3,0,NULL,'2025-05-26','2025-07-31','2025-06-16 09:39:49',NULL),

(4,2,4,0,NULL,'2025-05-26','2025-07-31','2025-06-16 09:39:49',NULL),

(5,3,5,1,8,'2025-07-09','2026-07-09','2025-07-09 10:49:25','2025-07-09 13:14:20'),

(6,3,7,0,NULL,'2025-07-18','2026-07-18','2025-07-18 14:52:53',NULL);

/*Table structure for table `opd_user_message` */

DROP TABLE IF EXISTS `opd_user_message`;

CREATE TABLE `opd_user_message` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL COMMENT '用户id',
  `sender_id` int(11) DEFAULT NULL,
  `content` text COLLATE utf8mb4_unicode_ci COMMENT '消息',
  `main_id` int(11) DEFAULT NULL,
  `type` tinyint(1) DEFAULT NULL COMMENT '消息类型（0创建订单 1系统模板消息 2用户发送留言咨询 3帖子回复 4帖子提及 5回复提及 6私信 7审核结果通知 8留言咨询回复 9给产品经理发审核通知 10公共新闻通知）',
  `is_read` tinyint(1) DEFAULT '0' COMMENT '0未读 1已读',
  `create_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=98 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户消息表';

/*Data for the table `opd_user_message` */

insert  into `opd_user_message`(`id`,`user_id`,`sender_id`,`content`,`main_id`,`type`,`is_read`,`create_time`) values 

(1,3,2,'The administrator has generated your order.',1,0,1,'2025-06-16 15:07:10'),

(2,3,2,'The administrator has generated your order.',2,0,1,'2025-06-16 15:09:59'),

(3,3,2,'hello,this is just a test infomation!',1,1,1,'2025-06-16 16:20:33'),

(4,3,2,'The administrator has generated your order.',3,0,1,'2025-06-18 10:07:38'),

(5,3,2,'The administrator has generated your order.',4,0,1,'2025-06-18 10:12:31'),

(6,3,2,'The administrator has generated your order.',5,0,1,'2025-06-18 10:19:53'),

(7,3,2,'The administrator has generated your order.',6,0,1,'2025-06-18 10:43:10'),

(8,3,2,'The administrator has generated your order.',7,0,1,'2025-06-18 10:43:49'),

(9,3,2,'你好，这是一封回复邮件~~~~',1,2,1,'2025-06-23 13:12:52'),

(10,5,2,'The administrator has generated your order.',8,0,1,'2025-06-25 16:20:17'),

(11,5,2,'The administrator has generated your order.',9,0,1,'2025-06-25 16:28:26'),

(12,5,2,'The administrator has generated your order.',10,0,1,'2025-06-25 16:48:10'),

(13,5,2,'The administrator has generated your order.',11,0,1,'2025-06-25 16:48:11'),

(14,5,2,'The post \'hello...\' comment review results：Review approved',5,7,1,'2025-07-11 10:50:18'),

(15,5,2,'The post \'hello...\' comment review results：Review approved',5,7,1,'2025-07-11 11:03:33'),

(16,5,2,'The post \'hello...\' comment review results：Review approved',5,7,1,'2025-07-11 11:48:46'),

(17,5,2,'The post \'hello...\' comment review results：Review approved',5,7,1,'2025-07-11 15:02:30'),

(18,5,2,'The post \'hello...\' comment review results：Review approved',5,7,1,'2025-07-11 15:07:13'),

(19,5,2,'The post \'hello...\' comment review results：Review approved',5,7,1,'2025-07-11 15:07:45'),

(20,5,2,'The post \'hello...\' comment review results：Review approved',5,7,1,'2025-07-11 15:08:52'),

(21,5,2,'The post \'hello hello...\' review results：Review failed',6,7,1,'2025-07-15 13:56:49'),

(22,5,2,'The post \'test\' comment review results：Review approved',2,7,1,'2025-07-15 14:26:10'),

(23,3,3,'The post mentioned you',7,4,1,'2025-07-16 15:25:25'),

(24,5,3,'The post mentioned you',7,4,0,'2025-07-16 15:25:25'),

(25,4,3,'You have a post waiting for review',7,9,0,'2025-07-16 15:25:25'),

(26,3,2,'The post \'Just a test\' review results：Review approved',7,7,1,'2025-07-16 15:26:13'),

(27,3,3,'The post comment mentioned you',7,5,1,'2025-07-16 15:33:33'),

(28,4,3,'You have a post comment waiting for review',7,9,0,'2025-07-16 15:33:33'),

(29,3,2,'The post \'Just a test\' comment review results：Review approved',7,7,1,'2025-07-16 15:33:43'),

(30,3,2,'You have received a public news article',4,10,1,'2025-07-16 17:19:07'),

(31,4,3,'You have a post waiting for review',1,9,0,'2025-07-17 09:54:14'),

(32,4,3,'You have a post waiting for review',1,9,0,'2025-07-17 09:57:36'),

(33,4,3,'You have a post comment waiting for review',7,9,0,'2025-07-17 10:29:12'),

(34,4,6,'You have a post waiting for review',8,9,0,'2025-07-17 13:31:03'),

(35,6,2,'The post \'Comprehensive Review of the Apple iPhone 16: Innovation Meets Refinement\' review results：Review approved',8,7,1,'2025-07-17 13:41:17'),

(36,4,6,'You have a post comment waiting for review',8,9,0,'2025-07-17 13:54:06'),

(37,6,2,'The post \'Comprehensive Review of the Apple iPhone 16: Innovation Meets Refinement\' comment review results：Review approved',8,7,1,'2025-07-17 13:57:36'),

(38,4,6,'You have a post comment waiting for review',8,9,0,'2025-07-17 13:58:55'),

(39,6,2,'The post \'Comprehensive Review of the Apple iPhone 16: Innovation Meets Refinement\' comment review results：Review approved',8,7,1,'2025-07-17 13:59:03'),

(40,4,0,'You have an online message for consultation：ewrwerq',2,2,0,'2025-07-17 16:07:02'),

(41,7,2,'The administrator has generated your order.',12,0,1,'2025-07-18 11:06:19'),

(42,7,2,'The administrator has generated your order.',13,0,1,'2025-07-18 11:10:02'),

(43,7,2,'The administrator has generated your order.',14,0,1,'2025-07-18 11:11:48'),

(44,7,2,'The administrator has generated your order.',15,0,1,'2025-07-18 11:13:47'),

(45,7,2,'The administrator has generated your order.',16,0,1,'2025-07-18 11:32:05'),

(46,4,7,'You have a post comment waiting for review',2,9,0,'2025-07-18 13:29:57'),

(47,7,2,'The post \'test\' comment review results：Review approved',2,7,1,'2025-07-18 13:30:32'),

(48,4,7,'You have a post comment waiting for review',2,9,0,'2025-07-18 13:31:02'),

(49,7,2,'The post \'test\' comment review results：Review approved',2,7,1,'2025-07-18 13:31:28'),

(50,4,7,'You have a post comment waiting for review',2,9,0,'2025-07-18 13:32:02'),

(51,7,2,'The post \'test\' comment review results：Review approved',2,7,1,'2025-07-18 13:32:23'),

(52,4,7,'You have a post comment waiting for review',2,9,0,'2025-07-18 13:33:15'),

(53,4,7,'You have a post comment waiting for review',2,9,0,'2025-07-18 13:35:01'),

(54,7,2,'The post \'test\' comment review results：Review approved',2,7,1,'2025-07-18 13:35:32'),

(55,7,2,'The post \'test\' comment review results：Review failed（）',2,7,1,'2025-07-18 13:35:39'),

(56,4,7,'You have a post waiting for review',9,9,0,'2025-07-18 13:46:01'),

(57,4,7,'You have a post waiting for review',10,9,0,'2025-07-18 13:46:31'),

(58,7,2,'The post \'这里是一个检测不通过的帖子\' review results：Review failed（内容有煽动舆论的嫌疑）',10,7,1,'2025-07-18 13:47:09'),

(59,7,2,'The post \'这里是一个检测通过的帖子\' review results：Review approved',9,7,1,'2025-07-18 13:47:20'),

(60,3,7,'The post comment mentioned you',2,5,0,'2025-07-18 16:22:16'),

(61,4,7,'You have a post comment waiting for review',2,9,0,'2025-07-18 16:22:16'),

(62,7,2,'The post \'test\' comment review results：Review approved',2,7,1,'2025-07-18 16:22:40'),

(63,4,6,'You have a post waiting for review',11,9,0,'2025-07-18 16:30:00'),

(64,6,2,'The post \'121212\' review results：Review approved',11,7,1,'2025-07-18 16:30:46'),

(65,4,7,'You have a post comment waiting for review',11,9,0,'2025-07-18 16:33:54'),

(66,7,2,'The post \'121212\' comment review results：Review approved',11,7,1,'2025-07-18 16:35:05'),

(67,4,7,'You have a post comment waiting for review',11,9,0,'2025-07-18 17:06:32'),

(68,7,2,'The post \'121212\' comment review results：Review failed（你的评论太难听）',11,7,1,'2025-07-18 17:07:11'),

(69,4,6,'You have a post comment waiting for review',2,9,0,'2025-07-21 09:52:28'),

(70,4,6,'You have a post comment waiting for review',2,9,0,'2025-07-21 09:53:45'),

(71,4,6,'You have a post comment waiting for review',2,9,0,'2025-07-21 09:53:56'),

(72,4,6,'You have a post comment waiting for review',2,9,0,'2025-07-21 09:54:39'),

(73,6,2,'The post \'test\' comment review results：Review approved',2,7,1,'2025-07-21 09:55:14'),

(74,6,6,'The post comment mentioned you',2,5,1,'2025-07-21 10:31:31'),

(75,4,6,'You have a post comment waiting for review',2,9,0,'2025-07-21 10:31:31'),

(76,6,2,'The post \'test\' comment review results：Review approved',2,7,1,'2025-07-21 10:33:44'),

(77,4,3,'You have a post comment waiting for review',2,9,0,'2025-07-21 16:46:53'),

(78,3,2,'The post \'test\' comment review results：Review approved',2,7,0,'2025-07-21 16:47:05'),

(79,4,3,'You have a post comment waiting for review',2,9,0,'2025-07-21 16:53:29'),

(80,6,2,'The administrator has generated your order.',17,0,1,'2025-07-21 16:56:19'),

(81,6,2,'The administrator has generated your order.',18,0,1,'2025-07-21 16:56:31'),

(82,6,2,'The administrator has generated your order.',19,0,1,'2025-07-21 16:56:39'),

(83,6,2,'The administrator has generated your order.',20,0,1,'2025-07-21 16:57:41'),

(84,6,2,'The administrator has generated your order.',21,0,1,'2025-07-21 16:58:38'),

(85,7,2,'The administrator has generated your order.',22,0,1,'2025-07-21 16:59:02'),

(86,7,2,'The administrator has generated your order.',23,0,1,'2025-07-21 16:59:52'),

(87,4,6,'You have a post comment waiting for review',11,9,0,'2025-07-21 17:50:39'),

(88,4,3,'You have a post comment waiting for review',2,9,0,'2025-07-22 09:19:36'),

(89,3,2,'The post \'test\' comment review results：Review approved',2,7,0,'2025-07-22 09:19:52'),

(90,4,7,'You have a post waiting for review',12,9,0,'2025-07-22 09:27:11'),

(91,7,2,'The post \'这里是一个问答的帖子\' review results：Review approved',12,7,1,'2025-07-22 09:27:50'),

(92,6,2,'The administrator has generated your order.',24,0,1,'2025-07-22 09:27:59'),

(93,6,2,'The administrator has generated your order.',25,0,1,'2025-07-22 09:28:54'),

(94,4,3,'You have a post comment waiting for review',2,9,0,'2025-07-22 09:35:42'),

(95,3,2,'The post \'test\' comment review results：Review approved',2,7,0,'2025-07-22 09:35:48'),

(96,4,6,'You have a post comment waiting for review',8,9,0,'2025-07-22 14:48:01'),

(97,6,2,'The post \'Comprehensive Review of the Apple iPhone 16: Innovation Meets Refinement\' comment review results：Review approved',8,7,0,'2025-07-22 14:48:34');

/*Table structure for table `opd_user_points` */

DROP TABLE IF EXISTS `opd_user_points`;

CREATE TABLE `opd_user_points` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `order_id` int(11) DEFAULT '0' COMMENT '订单id，获取积分',
  `coupon_id` int(11) DEFAULT '0' COMMENT '优惠券id，使用积分',
  `points` varchar(10) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '积分数（正：获取，负：使用）',
  `content` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '积分使用说明',
  `create_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=28 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户积分扣减表';

/*Data for the table `opd_user_points` */

insert  into `opd_user_points`(`id`,`user_id`,`order_id`,`coupon_id`,`points`,`content`,`create_time`) values 

(1,3,1,0,'100','Buy products','2025-06-16 15:07:10'),

(2,3,2,0,'110','Buy products','2025-06-16 15:09:59'),

(3,3,3,0,'10','Buy service','2025-06-18 10:07:38'),

(4,3,4,0,'10','Buy service','2025-06-18 10:12:31'),

(5,3,5,0,'10','Buy service','2025-06-18 10:19:53'),

(6,3,6,0,'100','Buy products','2025-06-18 10:43:10'),

(7,3,7,0,'110','Buy products','2025-06-18 10:43:49'),

(8,5,8,0,'100','Buy products','2025-06-25 16:20:17'),

(9,5,9,0,'10','Buy service','2025-06-25 16:28:26'),

(10,5,10,0,'10','Buy service','2025-06-25 16:48:10'),

(11,5,11,0,'10','Buy service','2025-06-25 16:48:11'),

(12,5,0,3,'-300','Exchange trial coupons','2025-07-09 10:49:25'),

(13,7,12,0,'110','Buy products','2025-07-18 11:06:19'),

(14,7,13,0,'10','Buy service','2025-07-18 11:10:02'),

(15,7,14,0,'110','Buy products','2025-07-18 11:11:48'),

(16,7,15,0,'110','Buy products','2025-07-18 11:13:47'),

(17,7,16,0,'110','Buy products','2025-07-18 11:32:05'),

(18,7,0,3,'-300','Exchange trial coupons','2025-07-18 14:52:53'),

(19,6,17,0,'100','Buy products','2025-07-21 16:56:19'),

(20,6,18,0,'100','Buy products','2025-07-21 16:56:31'),

(21,6,19,0,'110','Buy products','2025-07-21 16:56:39'),

(22,6,20,0,'100','Buy products','2025-07-21 16:57:41'),

(23,6,21,0,'100','Buy products','2025-07-21 16:58:38'),

(24,7,22,0,'100','Buy products','2025-07-21 16:59:02'),

(25,7,23,0,'100','Buy products','2025-07-21 16:59:52'),

(26,6,24,0,'10','Buy service','2025-07-22 09:27:59'),

(27,6,25,0,'10','Buy service','2025-07-22 09:28:54');

/*Table structure for table `opd_user_roles` */

DROP TABLE IF EXISTS `opd_user_roles`;

CREATE TABLE `opd_user_roles` (
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `role_id` int(11) NOT NULL COMMENT '角色ID',
  UNIQUE KEY `idx_user_role` (`user_id`,`role_id`),
  KEY `idx_role_id` (`role_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户角色关联表';

/*Data for the table `opd_user_roles` */

insert  into `opd_user_roles`(`user_id`,`role_id`) values 

(1,1),

(2,1),

(3,4),

(4,4);

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;
