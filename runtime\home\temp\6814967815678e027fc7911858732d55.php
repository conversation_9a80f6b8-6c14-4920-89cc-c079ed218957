<?php /*a:5:{s:61:"C:\phpstudy_pro\WWW\opendelclub\app\home\view\user\index.html";i:1753153793;s:64:"C:\phpstudy_pro\WWW\opendelclub\app\home\view\public\header.html";i:1753067175;s:60:"C:\phpstudy_pro\WWW\opendelclub\app\home\view\user\left.html";i:1753067654;s:64:"C:\phpstudy_pro\WWW\opendelclub\app\home\view\public\footer.html";i:1753075005;s:62:"C:\phpstudy_pro\WWW\opendelclub\app\home\view\public\foot.html";i:1753075005;}*/ ?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0,maximum-scale=1.0,minimum-scale=1.0,user-scalable=no">
    <title>User - HitGen OpenDEL™</title>
    <link rel="shortcut icon" href="/favicon.ico" />
    <link href="/static/home/<USER>/vendors/aos.css" rel="stylesheet">
    <link href="/static/home/<USER>/style.css" rel="stylesheet" />
</head>

<body>
    <section
        class="pb-5 md:pb-20 relative bg-[url(/static/home/<USER>/bg.jpg)] ">
        <span class="absolute top-0 left-0 w-full h-full bg-[url(/static/home/<USER>/tm-left.png)] bg-no-repeat bg-[top_left] z-0 bg-size-[10rem] md:bg-size-[26.9375rem]"></span>
        <span class="absolute top-0 right-0 w-full h-full bg-[url(/static/home/<USER>/tm-right.png)] bg-no-repeat bg-[top_right] z-0 bg-size-[10rem] md:bg-size-[25.0625rem]"></span>

        <div id="header" class="relative z-[9999]">
    <header class="w-11/12 mx-auto md:w-10/12">
        <nav class="nav-wrapper flex justify-between items-center h-[3.125rem] md:h-24 top-0 relative" role="navigation">
            <div class="flex-1 md:flex md:items-center md:gap-5 md:h-full md:relative md:z-0">
                <a href="/">
                    <img src="/static/home/<USER>/logo.png" alt="HitGen OpenDEL™" class="w-24 md:w-[8.4375rem]" />
                </a>
                <ul class="fixed hidden items-center h-[calc(100%-3.125rem)] bottom-0 overflow-auto left-0 z-[99999] w-full bg-white text-base
                md:flex md:gap-x-[3.75rem] md:relative md:bg-transparent md:overflow-visible md:h-full md:flex-1 md:text-xl animate__animated animate__delay"
                    id="nav_list">
                    <li class="md:h-full flex">
                        <a href="/" class="text-[#000]  flex-1 leading-[3.125rem] px-5 border-b border-[#e0eaff]
                        md:border-0 md:leading-none md:flex md:items-center
                        ">Home</a>
                    </li>
                    <li class="relative cursor-pointer md:h-full flex flex-col">
                        <a href="/news/" class="text-[#000] cursor-pointer flex-1 h-full leading-[3.125rem] px-5 flex justify-between items-center border-b border-[#e0eaff] md:border-0">
                            <span>DELHunter</span>
                        </a>
                    </li>
                    <li class="relative flex justify-between items-center border-b border-[#e0eaff] cursor-pointer navigation flex-wrap group
                        md:border-0 md:h-full md:flex-nowrap">
                        <a href="/product/"
                            class="text-[#000] cursor-pointer leading-[3.125rem] px-5 md:px-0 md:leading-none">
                            Services
                        </a>
                        <i class="w-[1rem] h-[.5625rem] inline-block bg-[url(/static/home/<USER>/icons/dow.png)] bg-no-repeat bg-center bg-size-[1rem] ml-2 group-hover:rotate-180 transition-all mr-5
                        md:mr-0
                        "></i>
                        <ul class="
                            secondary static z-50 hidden bg-[#f8fdff]
                            w-full md:absolute md:top-24 md:left-1/2  md:-translate-x-1/2 md:bg-white md:w-[19.5rem] ">
                            <?php if(is_array($menu_product) || $menu_product instanceof \think\Collection || $menu_product instanceof \think\Paginator): $i = 0; $__LIST__ = $menu_product;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?>
                            <li class="<?php echo !empty($vo['service']) ? 'flex items-center justify-between flex-wrap border-b border-t border-[#e0eaff] relative group navigation-item' : 'grid grid-cols-1'; ?> md:pr-[1.25rem] md:px-0">
                                <a href="/product/<?php echo htmlentities((string) $vo['seo_url']); ?>"
                                    class="text-[#000] flex items-center  md:py-5 px-8 h-[3.125rem] md:h-auto">
                                    <?php echo htmlentities((string) $vo['name']); ?>
                                </a>
                                <i class="w-[1rem] h-[.5625rem] inline-block bg-[url(/static/home/<USER>/icons/dow.png)] bg-no-repeat rotate-0 bg-size-[1rem] md:-rotate-90 bg-center ml-2 transition-all mr-5
                                md:mr-0 "></i>
                                <ul class="three-menu w-full hidden bg-[#f8fdff] md:absolute md:top-0 Roboto_Regular md:left-full md:w-full md:bg-[#fff] md:shadow-lg ">
                                    <?php if(is_array($vo['service']) || $vo['service'] instanceof \think\Collection || $vo['service'] instanceof \think\Paginator): $i = 0; $__LIST__ = $vo['service'];if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$v): $mod = ($i % 2 );++$i;?>
                                    <li class="border-b border-t border-[#e0eaff]">
                                        <a href="/service/<?php echo htmlentities((string) $v['seo_url']); ?>" class="
                                        flex items-center px-8 h-[3.125rem] md:h-auto
                                        md:px-8 md:py-5 line-clamp-1 ">
                                            <?php echo htmlentities((string) $v['name']); ?>
                                        </a>
                                    </li>
                                    <?php endforeach; endif; else: echo "" ;endif; ?>
                                </ul>
                            </li>
                            <?php endforeach; endif; else: echo "" ;endif; ?>
                        </ul>
                    </li>

                    <li class="relative flex justify-between items-center border-b border-[#e0eaff] cursor-pointer navigation flex-wrap group
                        md:border-0 md:h-full md:flex-nowrap">
                        <a href="" class="text-[#000] cursor-pointer leading-[3.125rem] px-5 md:px-0 md:leading-none">
                            Resources
                        </a>
                        <i class="w-[1rem] h-[.5625rem] inline-block bg-[url(/static/home/<USER>/icons/dow.png)] bg-no-repeat bg-center ml-2 bg-size-[1rem] group-hover:rotate-180 transition-all mr-5
                        md:mr-0
                        "></i>
                        <ul class="
                        secondary static bg-[#f8fdff] z-50
                        hidden
                        w-full
                        md:absolute
                        md:top-24
                        md:left-1/2
                        md:-translate-x-1/2
                            md:bg-white
                            md:w-[19.5rem] ">
                            <?php if(is_array($menu_resources_category) || $menu_resources_category instanceof \think\Collection || $menu_resources_category instanceof \think\Paginator): $k = 0; $__LIST__ = $menu_resources_category;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($k % 2 );++$k;?>
                            <li class="grid grid-cols-1">
                                <a href="/resources/?tab=<?php echo htmlentities((string) $k); ?>" class="text-[#000] flex items-center md:py-5 px-8 h-[3.125rem] border-b border-[#e0eaff] md:h-auto">
                                    <?php echo htmlentities((string) $vo['name']); ?>
                                </a>
                            </li>
                            <?php endforeach; endif; else: echo "" ;endif; ?>

                            <li class="grid grid-cols-1">
                                <a href="/faq/" class="text-[#000] flex items-center md:py-5 px-8 h-[3.125rem] border-b border-[#e0eaff] md:h-auto">
                                    FAQ
                                </a>
                            </li>
                        </ul>
                    </li>

                    <?php if($basic['forum_status']==1): ?>
                    <li class="md:h-full grid grid-cols-1">
                        <a href="/iCommunity/" class="text-[#000] border-b border-[#e0eaff] leading-[3.125rem] px-5 md:border-0
                        md:leading-none
                        md:flex
                        md:items-center">iCommunity</a>
                    </li>
                    <?php endif; ?>
                </ul>
            </div>

            <div class="flex items-center gap-3.5 md:gap-8 md:relative md:z-20">
                <div class="md:flex md:items-center">
                    <div class="bg-white rounded-full p-1 h-8 w-8 border border-[#dfe7ff] md:w-12 md:h-12 "
                        id="search_btn">
                        <button type="button"
                            class="search-btn w-full h-full bg-[url(/static/home/<USER>/icons/sousuo.png)] bg-size-[1rem] rounded-full bg-no-repeat bg-center md:bg-size-[1.3rem] cursor-pointer"
                            aria-label="搜索"></button>
                    </div>
                    <form action="/search" method="get"
                        class="hidden absolute w-full left-0 top-full md:relative z-10" id="search_form">
                        <div
                            class="bg-[#f8fdff] h-14 md:bg-white md:rounded-full md:ml-2 md:w-[18.75rem] md:h-12 relative flex items-center px-2.5 md:p-0">
                            <input type="text"
                                name="q"
                                value="<?php echo isset($keyword) ? htmlentities((string) $keyword) : ''; ?>"
                                class="border flex-1 rounded-md h-10 px-5 border-[#e0eaff] bg-white md:rounded-full md:ml-2 md:w-full md:h-12 md:px-5"
                                placeholder="Enter the keyword" />
                            <button type="submit" tabindex="-1"
                                class="w-8 h-8 md:absolute md:right-[5px] md:top-1/2 md:-translate-y-1/2 md:w-[2.375rem] md:h-[2.375rem] bg-[url(/static/home/<USER>/icons/sousuo.png)] rounded-full bg-no-repeat bg-center bg-size-[1rem] md:bg-size-[1.3rem] z-10 cursor-pointer"
                                id="submit_btn"></button>
                        </div>
                    </form>
                </div>

                <?php if(session('userId')): ?>
                <div class="bg-white rounded-full p-1 h-8 w-8 border border-[#dfe7ff] relative cursor-pointer md:w-12 md:h-12 flex justify-center items-center"
                 id="message_btn" onmouseenter="showMessageDrop()" onmouseleave="hideMessageDrop()"
                    onclick="toggleMessageDrop()">
                    <div class="btn-message">
                        <div class="w-full h-full flex items-center justify-center">
                            <img src="/static/home/<USER>/icons/lingdang.png" alt="" class="w-[1rem] md:w-[1.2rem]">
                        </div>
                        <?php if($user_message_count>0): ?>
                        <div class="absolute -top-1 -right-[.525rem] text-white text-center" id="message_num">
                            <div class="min-w-[1rem] min-h-[1rem] px-1 text-[.75rem] bg-[#ff0000] rounded-full md:min-w-[1.25rem] md:h-[1.25rem] md:leading-[1.25rem]">
                                <span><?php echo htmlentities((string) $user_message_count); ?></span>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>

                    <!-- Message下拉通知 -->
                    <div class="absolute top-full left-[10%] -translate-x-1/2 z-20 min-w-[15.625rem] cursor-default pt-3
                                md:min-w-[25rem] md:-left-[100%]" id="message_drop" onclick="event.stopPropagation()" style="display: none;">
                        <?php if(count($user_message)>0): ?>
                        <div class="bg-white shadow-2xl rounded-tl-xl rounded-tr-xl">
                            <div class="message-header flex items-center justify-between p-3 border-b border-[#e0eaff] text-base md:text-xl md:py-[1.25rem] md:px-[1.875rem]">
                                <h3 class="font-bold">Tooltip</h3>
                                <button type="button"
                                    class="w-[1rem] h-[1rem] bg-[url(/static/home/<USER>/icons/shanchu.png)] bg-no-repeat bg-size-[.95rem] cursor-pointer md:w-[1.25rem] md:h-[1.375rem] md:bg-size-[1rem]"
                                    id="EmptyMessage"></button>
                            </div>
                            <div class="message-tips-list" id="message_cont">
                                <ul>
                                    <?php if(is_array($user_message) || $user_message instanceof \think\Collection || $user_message instanceof \think\Paginator): $i = 0; $__LIST__ = $user_message;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?>
                                    <li>
                                        <a href="/user/message" class="flex items-center line-clamp-1 gap-x-1">
                                            <strong>[System]</strong><span><?php echo htmlentities((string) $vo['content']); ?></span>
                                        </a>
                                    </li>
                                    <?php endforeach; endif; else: echo "" ;endif; ?>
                                </ul>
                            </div>
                            <!-- <div class="flex items-center justify-center py-4 gap-2.5" id="message_more">
                                <a href="/user/message">
                                    <strong class="text-sm md:text-xl"> View All </strong>
                                </a>
                                <img src="/static/home/<USER>/icons/changjiantou-zuoshang2.png" alt="" class="w-[1rem]">
                            </div> -->
                            <!-- 清空/暂无消息时候显示 -->
                            <div class="text-center py-4 text-[#f08411] no-message hidden" id="no_message">
                                <span class="text-sm md:text-xl">No message</span>
                            </div>
                        </div>
                        <?php endif; if(count($user_icommunity)>0 && $basic['letter_status']==1): ?>
                        <div class="bg-white shadow-2xl rounded-bl-xl rounded-br-xl">
                            <div class="message-header flex items-center justify-between p-3 border-b border-[#e0eaff] text-base md:text-xl md:py-[1.25rem] md:px-[1.875rem]">
                                <h3 class="font-bold">iCommunity</h3>
                                <button type="button"
                                    class="w-[1rem] h-[1rem] bg-[url(/static/home/<USER>/icons/shanchu.png)] bg-no-repeat bg-size-[.95rem] cursor-pointer md:w-[1.25rem] md:h-[1.375rem] md:bg-size-[1rem]"
                                    id="EmptyMessage"></button>
                            </div>
                            <div class="message-tips-list" id="message_cont">
                                <ul>
                                    <?php if(is_array($user_icommunity) || $user_icommunity instanceof \think\Collection || $user_icommunity instanceof \think\Paginator): $i = 0; $__LIST__ = $user_icommunity;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?>
                                    <li>
                                        <a href="/user/private-message" class="flex items-center line-clamp-1 gap-x-1">
                                            <strong>[<?php echo htmlentities((string) $vo['first_name']); ?> <?php echo htmlentities((string) $vo['last_name']); ?>]</strong>
                                            <span><?php echo htmlentities((string) $vo['content']); ?></span>
                                        </a>
                                    </li>
                                    <?php endforeach; endif; else: echo "" ;endif; ?>
                                </ul>
                            </div>
                            <!-- <div class="flex items-center justify-center py-4 gap-2.5" id="message_more">
                                <a href="/user/private-message">
                                    <strong class="text-sm md:text-xl"> View All </strong>
                                </a>
                                <img src="/static/home/<USER>/icons/changjiantou-zuoshang2.png" alt="" class="w-[1rem]">
                            </div> -->
                            <!-- 清空/暂无消息时候显示 -->
                            <div class="text-center py-4 text-[#f08411] no-message hidden" id="no_message">
                                <span class="text-sm md:text-xl">No message</span>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="w-8 h-8 rounded-full md:w-[3.125rem] md:h-[3.125rem] relative cursor-pointer btn-name">
                    <div class="user-name">
                        <img src="<?php echo isset($user['avatar']) ? htmlentities((string) $user['avatar']) : '/static/home/<USER>/user-1.jpg'; ?>" class="w-full h-full rounded-full object-cover" alt="<?php echo session('userEmail'); ?>" />
                    </div>
                    <!-- 登录以后的下拉 -->
                    <div class="user-name-drop absolute top-full -left-[50%] -translate-x-1/2 min-w-[12.5rem] pt-3 z-20 md:right-0 md:min-w-[15.625rem] md:-left-full cursor-default" onclick="event.stopPropagation()">
                        <div class="bg-white p-[1.25rem] shadow-2xl rounded-xl  md:p-6 ">
                            <div class="text-center mb-3">
                                <img src="<?php echo isset($user['avatar']) ? htmlentities((string) $user['avatar']) : '/static/home/<USER>/user-1.jpg'; ?>" alt="" class="w-[4rem] h-[4rem] mx-auto rounded-full" />
                                <p class="mt-[.625rem] text-lg">
                                    <strong class="line-clamp-1"><?php echo htmlentities((string) $user['first_name']); ?> <?php echo htmlentities((string) $user['last_name']); ?></strong>
                                </p>
                            </div>
                            <div class="text-sm md:text-xl">
                                <div class="border-b py-2  px-2 border-[#e0eaff]">
                                    <a href="/user/">
                                        Personal Center
                                    </a>
                                </div>
                                <div class="py-2 px-2">
                                    <a href="/logout">
                                        Sign Out
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <?php else: ?>
                <div class="flex items-center h-8 min-w-20">
                    <button aria-label="登录账户"
                        class="bg-[#f08411] h-full w-full text-white cursor-pointer bg-[url(/static/home/<USER>/icons/yonghu.png)] bg-no-repeat bg-size-[0.8125rem] bg-[0.75rem_center] rounded-lg text-sm pl-6 pr-2 md:w-32 md:h-12 md:bg-size-[1.1rem] md:text-xl">
                        <a href="/login/">Login</a>
                    </button>
                </div>
                <?php endif; ?>

                <div class="menu min-w-8 h-5 ml-2 md:hidden">
                    <button aria-label="打开菜单"
                        class="h-full w-full bg-[url(/static/home/<USER>/icons/menu.png)] bg-no-repeat bg-center bg-size-[1.3rem] cursor-pointer"
                        id="menu_btn"></button>
                </div>
        </nav>
    </header>
</div>

        <div class="w-11/12 mx-auto md:w-10/12 relative z-10">
            <div class="mt-5 Navigation mb-3.5 md:mb-14">
                <ul class="text-[#999] flex items-center text-md md:text-xl">
                    <li>
                        <a href="/" class="text-[#155797]">
                            Home
                        </a>
                    </li>
                    <li class="max-w-[12.5rem] line-clamp-1 md:max-w-2xs">
                        Personal Center
                    </li>
                </ul>
            </div>

            <!-- data-aos="fade-up" -->
            <div class="md:flex md:flex-row md:gap-x-4">

                <div class="flex flex-col gap-5 mb-5 md:w-1/3">
    <div class="border border-[var(--border-color)] bg-white rounded-lg shadow-2xs flex items-center flex-col py-6 md:py-[2.75rem] md:rounded-2xl">
        <div class="w-[4.375rem] h-[4.375rem] rounded-full mb-4
        md:w-[8.75rem] md:h-[8.75rem] md:mb-[2.5rem]
        ">
            <img src="<?php echo isset($user['avatar']) ? htmlentities((string) $user['avatar']) : '/static/home/<USER>/user-1.jpg'; ?>" alt="user" class="w-full h-full rounded-full object-cover" />
        </div>
        <!-- 徽章/经验条 -->
        <div class="badge-wrapper mb-4">
            <!-- 经验条 -->
            <div class="wrapper-experience h-[.3125rem] rounded-full bg-[#dae9ff] relative mb-3 md:h-[.4375rem]">
                <div class="experience-bar absolute left-0 top-0 h-full rounded-full bg-[#f08411]" style="width: <?php echo $user['role_id']<=4 ? '0%'  :  ($user['role_id'] == 5 ? '50%' : '100%'); ?>;">
                </div>
            </div>
            <!-- 徽章 -->
            <div class="badge-container bg-[#fafbff] rounded-full px-2 py-1 border border-[#dae9ff] flex items-center gap-x-2.5 md:px-3 md:py-1.5">
                <?php if($user['role_id']==5): ?>
                <img src="/static/home/<USER>/icons/badge_1.png" class="w-[1.25rem] md:w-[2rem]" alt="02" />
                <?php elseif($user['role_id']==6): ?>
                <img src="/static/home/<USER>/icons/badge_3.png" class="w-[1.25rem] md:w-[2rem]" alt="03" />
                <?php else: ?>
                <img src="/static/home/<USER>/icons/badge_2.png" class="w-[1.25rem] md:w-[2rem]" alt="01" />
                <?php endif; ?>
                <span class="text-[#155797] text-sm md:text-lg">
                    <?php echo htmlentities((string) $user['role_name']); ?>
                </span>
            </div>
        </div>
        <div class="Roboto_Bold text-xl mb-6 md:text-[1.875rem]">
            <?php echo htmlentities((string) $user['first_name']); ?> <?php echo htmlentities((string) $user['last_name']); ?>
        </div>
        <div class="grid grid-cols-2 gap-5 text-center">
            <?php if($basic['coupon_status']==1): ?>
            <div class="border-r border-[#dfe7ff] relative">
                <span class="Roboto_Bold text-2xl md:text-4xl">
                    <?php echo htmlentities((string) $coupon_count); ?>
                </span>
                <p class="text-sm text-[#999] md:text-xl md:mt-2.5">
                    Voucher
                </p>
                <a href="/user/coupon" class="absolute top-0 left-0 w-full h-full"></a>
            </div>
            <?php endif; ?>

            <div class="relative">
                <span class="Roboto_Bold text-2xl md:text-4xl">
                    <?php echo isset($user['points']) ? htmlentities((string) $user['points']) : 0; ?>
                </span>
                <p class="text-sm text-[#999] md:text-xl md:mt-2.5">
                    HitGen Points
                </p>
                <a href="/user/points" class="absolute top-0 left-0 w-full h-full"></a>
            </div>
        </div>
    </div>
    <div
        class="w-full overflow-x-auto mb-3 bg-white
    md:border md:border-[var(--border-color)] md:rounded-2xl md:p-3.5 md:overflow-hidden md:m-0 md:px-[1.25rem]">
        <div class="flex flex-nowrap min-w-max gap-2 text-abse
        md:flex-col md:text-[1.5625rem] md:min-w-auto navigation-bar
        ">
            <div class="border border-[var(--border-color)] relative py-3 px-4 rounded-lg <?php if(request()->action()=='index'): ?>text-[#f08411]<?php endif; ?>
                md:border-0 md:border-b md:border-[#dae9ff] md:rounded-none md:py-6 navigation-item md:bg-[url(/static/home/<USER>/icons/xiala.png)] md:bg-no-repeat md:bg-[95%_center] bg-size-[1rem]
            ">
                <a href="/user">
                    <span class="line-clamp-1">User Profile Management</span>
                </a>
            </div>
            <div
                class="border border-[var(--border-color)] relative py-3 px-4 rounded-lg <?php if(request()->action()=='order'): ?>text-[#f08411]<?php endif; ?>
            md:border-0 md:border-b md:border-[#dae9ff] md:rounded-none md:py-6 navigation-item md:bg-[url(/static/home/<USER>/icons/xiala.png)] md:bg-no-repeat md:bg-[95%_center] bg-size-[1rem]">
                <a href="/user/order">
                    <span class="line-clamp-1">Order Management</span>
                </a>
            </div>
            <div class="border border-[var(--border-color)] relative py-3 px-4 rounded-lg <?php if(request()->action()=='message'): ?>text-[#f08411]<?php endif; ?>
            md:border-0 md:border-b md:border-[#dae9ff] md:rounded-none md:py-6 navigation-item md:bg-[url(/static/home/<USER>/icons/xiala.png)] md:bg-no-repeat md:bg-[95%_center] bg-size-[1rem]">
                <a href="/user/message">
                    <span>Tooltip</span>

                    <?php if(count($user_message)>0): ?>
                    <div class="absolute top-0 -right-[.525rem] text-white text-center
                    md:top-1/2 md:-translate-y-1/2 md:right-[4.375rem]
                    ">
                        <div class="min-w-[1rem] min-h-[1rem] text-[.75rem] bg-[#ff0000] rounded-full px-1 md:px-2
                            md:min-w-[1.875rem] md:min-h-[1.875rem] md:text-xl">
                            <span><?php echo htmlentities((string) count($user_message)); ?></span>
                        </div>
                    </div>
                    <?php endif; ?>
                </a>
            </div>

            <?php if($basic['forum_status']==1): ?>
            <div class="border border-[var(--border-color)] relative py-3 px-4 rounded-lg <?php if(request()->action()=='post' || request()->action()=='comment' || request()->action()=='private_message'): ?>text-[#f08411]<?php endif; ?>
            md:border-0 md:rounded-none md:py-6 navigation-item md:bg-[url(/static/home/<USER>/icons/xiala.png)] md:bg-no-repeat md:bg-[95%_center] bg-size-[1rem]">
                <a href="/user/post">
                    <span>iCommunity</span>

                    <?php if($private_count>0): ?>
                    <div class="absolute top-0 -right-[.525rem] text-white text-center
                    md:top-1/2 md:-translate-y-1/2 md:right-[4.375rem]
                    ">
                        <div class="min-w-[1rem] min-h-[1rem] text-[.75rem] bg-[#ff0000] rounded-full px-1 md:px-2
                            md:min-w-[1.875rem] md:min-h-[1.875rem] md:text-xl">
                            <span><?php echo htmlentities((string) $private_count); ?></span>
                        </div>
                    </div>
                    <?php endif; ?>
                </a>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

                <div class="bg-white border border-[var(--border-color)] rounded-lg shadow-2xs
                md:w-2/3 md:rounded-2xl
                ">
                    <div
                        class="grid grid-cols-2 text-sm md:text-[1.5625rem] md:px-[2.5rem] md:pt-4 md:flex Roboto_Bold md:text-2xl md:gap-x-[4.375rem] user-tab-link">
                        <span data-tab="information" class="user-tab-item">
                            Basic information
                        </span>
                        <span data-tab="settings" class="user-tab-item">
                            Security Settings
                        </span>
                    </div>
                    <div class="user-profile">
                        <!-- 资料修改 -->
                        <div data-tab="information" class="profile-item">
                            <form action="" method="post" id="userForm" class="p-4 pb-10 md:w-full md:p-[3.125rem]" enctype="multipart/form-data">
                                <div class="flex flex-col gap-y-4 text-sm md:text-xl md:gap-y-7">
                                    <div class="flex items-center text-[.75rem] gap-2 md:gap-x-[1.25rem] Roboto_Bold md:text-xl md:mb-[1.25rem]">
                                        <div class="w-[3.75rem] h-[3.75rem] rounded-full overflow-hidden *:
                                        md:w-[10rem] md:h-[10rem]">
                                            <!-- 默认头像 -->
                                            <img src="<?php echo isset($user['avatar']) ? htmlentities((string) $user['avatar']) : '/static/home/<USER>/user-1.jpg'; ?>" alt="" class="object-cover w-full h-full"
                                                id="user_view" />
                                        </div>
                                        <div class="w-[8.5rem] h-[2.5rem] bg-white border border-[#dae9ff] rounded-full overflow-hidden text-center flex items-center justify-center relative px-1
                                        md:w-[15rem] md:h-[3.75rem]">
                                            <span>Upload new picture</span>
                                            <input type="file" name="avatar" id="file_btn"
                                                class="absolute w-full h-full opacity-0 left-0 top-0 z-10 cursor-pointer">
                                        </div>
                                        <button type="button"
                                            class="rounded-full w-[4.375rem] h-[2.5rem] bg-[#ffefdd] text-[#111111] md:w-[7.5rem] md:h-[3.75rem] cursor-pointer"
                                            id="Delete_btn">Delete</button>
                                    </div>

                                    <div class="input-item">
                                        <label class="Roboto_Bold">
                                            <span class="text-[#ff0000] align-text-top">*</span> Email
                                        </label>
                                        <div class="relative flex flex-col mt-2">
                                            <input type="email" class="w-full h-[3.125rem] border border-[#d8e2ff] rounded-md bg-[#f8fdff] bg-[url(/static/home/<USER>/icons/youxiang.png)] bg-no-repeat bg-[.75rem_center] bg-size-[1.25rem] px-5 pl-12
                                                md:h-[5rem] md:pl-16 md:bg-size-[1.8rem] md:bg-[1.2rem_center] md:pr-32

                                                " value="<?php echo htmlentities((string) $user['email']); ?>" disabled autocomplete="off" />
                                        </div>
                                    </div>

                                    <div class="input-item">
                                        <label class="Roboto_Bold">
                                            <span class="text-[#ff0000] align-text-top">*</span> Country/Region
                                        </label>
                                        <div class="relative flex flex-col mt-2 z-10">
                                            <select name="country" id="country" class="country w-full h-[3.125rem] border border-[#d8e2ff] rounded-md bg-transparent bg-[url(/static/home/<USER>/icons/news.png)] bg-no-repeat bg-[.75rem_center] bg-size-[1.25rem] px-5 pl-12 pr-12 appearance-none text-[#ddd] cursor-pointer
                                                    md:h-[5rem] md:pl-16 md:bg-size-[1.8rem] md:bg-[1.2rem_center]
                                                    ">
                                                <option value="" disabled selected style="color:#ddd;">Please select
                                                    your country/region</option>
                                                <?php if(is_array($country) || $country instanceof \think\Collection || $country instanceof \think\Paginator): $i = 0; $__LIST__ = $country;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?>
                                                <option value="<?php echo htmlentities((string) $vo['en_name']); ?>" style="color: #333;" <?php if($user['country']==$vo['en_name']): ?>selected<?php endif; ?>><?php echo htmlentities((string) $vo['en_name']); ?> <?php echo htmlentities((string) $vo['cn_name']); ?></option>
                                                <?php endforeach; endif; else: echo "" ;endif; ?>
                                            </select>
                                            <button type="button"
                                                class="bg-[url(/static/home/<USER>/icons/mti-jiantouyou.png)] bg-no-repeat bg-[.75rem_center] bg-size-[1.125rem] rounded-md top-1/2 -translate-y-1/2 absolute right-2 w-10 h-full -z-10">
                                            </button>
                                        </div>
                                        <div class=" hidden error text-[#ff0000] mt-2
                                            md:text-base
                                            ">
                                            Please fill in the information.
                                        </div>
                                    </div>
                                    <div class="input-item grid grid-cols-1 gap-y-4
                                        md:grid-cols-2 md:gap-x-6
                                        ">
                                        <div class="">
                                            <label for="first_name" class="Roboto_Bold">
                                                <span class="text-[#ff0000] align-text-top">*</span> First Name
                                            </label>
                                            <div class="relative flex flex-col mt-2">
                                                <input type="text" name="first_name" id="first_name"
                                                    placeholder="Please enter your first name" class="w-full h-[3.125rem] border border-[#d8e2ff] rounded-md bg-white bg-[url(/static/home/<USER>/icons/yonghu-1.png)] bg-no-repeat bg-[.75rem_center] bg-size-[1.25rem] px-5 pl-12 pr-6
                                                        md:h-[5rem] md:pl-16 md:bg-size-[1.8rem] md:bg-[1.2rem_center]
                                                        " autocomplete="off" value="<?php echo htmlentities((string) $user['first_name']); ?>" />
                                            </div>
                                            <div class=" hidden error text-[#ff0000] mt-2
                                                md:text-base
                                                ">
                                                Please fill in the information.
                                            </div>
                                        </div>
                                        <div class="">
                                            <label for="last_name" class="Roboto_Bold">
                                                <span class="text-[#ff0000] align-text-top">*</span> Last Name
                                            </label>
                                            <div class="relative flex flex-col mt-2">
                                                <input type="text" name="last_name" id="last_name"
                                                    placeholder="Please enter your last name" class="w-full h-[3.125rem] border border-[#d8e2ff] rounded-md bg-white bg-[url(/static/home/<USER>/icons/yonghu-1.png)] bg-no-repeat bg-[.75rem_center] bg-size-[1.25rem] px-5 pl-12 pr-6
                                                        md:h-[5rem] md:pl-16 md:bg-size-[1.8rem] md:bg-[1.2rem_center]
                                                        " autocomplete="off" value="<?php echo htmlentities((string) $user['last_name']); ?>" />
                                            </div>
                                            <div class=" hidden error text-[#ff0000] mt-2
                                                md:text-base
                                                ">
                                                Please fill in the information.
                                            </div>
                                        </div>
                                    </div>
                                    <div class="input-item">
                                        <label for="title" class="Roboto_Bold">
                                            Title:
                                        </label>
                                        <div class="relative flex flex-col mt-2">
                                            <input type="text" name="title" id="title"
                                                placeholder="Please enter your title"
                                                class="w-full h-[3.125rem] border border-[#d8e2ff] rounded-md bg-white bg-[url(/static/home/<USER>/icons/zhiweimoban.png)] bg-no-repeat bg-[.75rem_center] bg-size-[1.25rem] px-5 pl-12 pr-6
                                                    md:h-[5rem] md:pl-16 md:bg-size-[1.8rem] md:bg-[1.2rem_center]
                                                    " autocomplete="off" value="<?php echo htmlentities((string) $user['title']); ?>" />
                                        </div>
                                    </div>

                                    <div class="input-item">
                                        <label for="phone" class="Roboto_Bold">
                                            <span class="text-[#ff0000] align-text-top">*</span>
                                            Phone:
                                        </label>
                                        <div class="relative flex flex-col mt-2">
                                            <input type="tel" name="phone" id="phone"
                                                placeholder="Enter your phone number" class="w-full h-[3.125rem] border border-[#d8e2ff] rounded-md bg-white bg-[url(/static/home/<USER>/icons/phone.png)] bg-no-repeat bg-[.75rem_center] bg-size-[1.25rem] px-5 pl-12 pr-6
                                                    md:h-[5rem] md:pl-16 md:bg-size-[1.8rem] md:bg-[1.2rem_center]
                                                    " autocomplete="off" value="<?php echo htmlentities((string) $user['phone']); ?>" />
                                        </div>
                                        <div class=" hidden error text-[#ff0000] mt-2
                                            md:text-base
                                            ">
                                            Please enter the phone number.
                                        </div>
                                    </div>

                                    <div class="input-item">
                                        <label for="organization" class="Roboto_Bold">
                                            <span class="text-[#ff0000] align-text-top">*</span>
                                            Organization/Institution/Corporation
                                        </label>
                                        <div class="relative flex flex-col mt-2">
                                            <input type="text" name="organization" id="organization"
                                                placeholder="Please enter your organization/institution/corporation"
                                                class="w-full h-[3.125rem] border border-[#d8e2ff] rounded-md bg-white bg-[url(/static/home/<USER>/icons/jigouguanli.png)] bg-no-repeat bg-[.75rem_center] bg-size-[1.25rem] px-5 pl-12 pr-6
                                                    md:h-[5rem] md:pl-16 md:bg-size-[1.8rem] md:bg-[1.2rem_center]" autocomplete="off" value="<?php echo htmlentities((string) $user['organization']); ?>" />
                                        </div>
                                        <div class=" hidden error text-[#ff0000] mt-2
                                            md:text-base
                                            ">
                                            Please fill in the information.
                                        </div>
                                    </div>

                                    <div class="input-item">
                                        <label for="express_address" class="Roboto_Bold">
                                            Shipping Address:
                                        </label>
                                        <div class="relative flex flex-col mt-2">
                                            <input type="text" name="express_address" id="express_address"
                                                placeholder="Enter your shipping addressEnter your shipping address"
                                                class="w-full h-[3.125rem] border border-[#d8e2ff] rounded-md bg-white bg-[url(/static/home/<USER>/icons/icon_address.png)] bg-no-repeat bg-[.75rem_center] bg-size-[1.25rem] px-5 pl-12 pr-6
                                                    md:h-[5rem] md:pl-16 md:bg-size-[1.8rem] md:bg-[1.2rem_center]
                                                    " autocomplete="off" value="<?php echo htmlentities((string) $user['express_address']); ?>" />
                                        </div>
                                    </div>
                                    <div class="input-item">
                                        <label for="city" class="Roboto_Bold">
                                            City:
                                        </label>
                                        <div class="relative flex flex-col mt-2">
                                            <input type="text" name="city" id="city" placeholder="Enter your city name"
                                                class="w-full h-[3.125rem] border border-[#d8e2ff] rounded-md bg-white bg-[url(/static/home/<USER>/icons/chengshi.png)] bg-no-repeat bg-[.75rem_center] bg-size-[1.25rem] px-5 pl-12 pr-6
                                                    md:h-[5rem] md:pl-16 md:bg-size-[1.8rem] md:bg-[1.2rem_center]
                                                    " autocomplete="off" value="<?php echo htmlentities((string) $user['city']); ?>" />
                                        </div>
                                    </div>
                                    <div class="input-item">
                                        <label for="express_postcode" class="Roboto_Bold">
                                            Postcode:
                                        </label>
                                        <div class="relative flex flex-col mt-2">
                                            <input type="text" name="express_postcode" id="express_postcode"
                                                placeholder="Please enter the postal code for your delivery address"
                                                class="w-full h-[3.125rem] border border-[#d8e2ff] rounded-md bg-white bg-[url(/static/home/<USER>/icons/youzhengbianma.png)] bg-no-repeat bg-[.75rem_center] bg-size-[1.25rem] px-5 pl-12 pr-6
                                                    md:h-[5rem] md:pl-16 md:bg-size-[1.8rem] md:bg-[1.2rem_center]
                                                    " autocomplete="off" value="<?php echo htmlentities((string) $user['express_postcode']); ?>" />
                                        </div>
                                    </div>

                                    <div class="input-item mt-2
                                        md:mt-5
                                        ">
                                        <button type="submit"
                                            class="bg-[#f08411] text-white rounded-xl text-base py-2 w-full cursor-pointer Roboto_Bold
                                                md:h-[5rem] md:inline-block md:w-[30.625rem] md:text-xl md:rounded-full"
                                            id="submitBtn">Save changes</button>
                                    </div>
                                </div>
                            </form>
                        </div>

                        <!-- 密码修改 -->
                        <div data-tab="settings" class="profile-item md:max-w-3xl">
                            <form action="" method="post" id="securityForm" class="p-4 md:w-full md:p-[3.125rem] pb-10">
                                <div class="flex flex-col gap-y-4 text-sm md:text-xl md:gap-y-7">
                                    <div class="input-item">
                                        <label class="Roboto_Bold">
                                            <span class="text-[#ff0000] align-text-top">*</span> Email
                                        </label>
                                        <div class="relative flex flex-col mt-2">
                                            <input type="email" name="profile_email" id="profile_email"
                                                 class="w-full h-[3.125rem] border border-[#d8e2ff] bg-[#f8fdff] rounded-md bg-[url(/static/home/<USER>/icons/youxiang.png)] bg-no-repeat bg-[12px_center] bg-size-[1.25rem] px-5 pl-12
                                            md:h-[5rem] md:pl-16 md:bg-size-[1.8rem] md:bg-[1.2rem_center] md:pr-32
                                            " value="<?php echo htmlentities((string) $user['email']); ?>" disabled autocomplete="off">
                                            <button type="button" class="text-white bg-[#f08411] rounded-md top-1/2 -translate-y-1/2 absolute right-2 px-2 py-1.5 cursor-pointer
                                                md:right-4 md:text-base md:py-3 md:px-4" id="SendOtp">Send OTP</button>
                                        </div>
                                    </div>
                                    <div class="input-item">
                                        <label for="captcha" class="Roboto_Bold">
                                            <span class="text-[#ff0000] align-text-top">*</span> Captcha
                                        </label>
                                        <div class="relative flex flex-col mt-2">
                                            <input type="text" name="captcha" id="captcha"
                                                placeholder="Please enter email verification code" class="w-full h-[3.125rem] border border-[#d8e2ff] rounded-md bg-white bg-[url(/static/home/<USER>/icons/yanzhengma.png)] bg-no-repeat bg-[12px_center] bg-size-[1.25rem] px-5 pl-12 pr-20
                                                md:h-[5rem] md:pl-16 md:bg-size-[1.8rem] md:bg-[1.2rem_center]
                                                " autocomplete="off">
                                        </div>
                                        <div class=" hidden error text-[#ff0000] mt-2
                                        md:text-base
                                        ">
                                            Please enter the verification information
                                        </div>
                                    </div>

                                    <div class="input-item">
                                        <label for="password" class="Roboto_Bold">
                                            <span class="text-[#ff0000] align-text-top">*</span> Password
                                        </label>
                                        <div class="relative flex flex-col mt-2">
                                            <input type="password" name="password" id="password"
                                                placeholder="Advise a secure password" class="w-full h-[3.125rem] border border-[#d8e2ff] rounded-md bg-white bg-[url(/static/home/<USER>/icons/mima.png)] bg-no-repeat bg-[12px_center] bg-size-[1.25rem] px-5 pl-12 pr-12
                                                md:h-[5rem] md:pl-16 md:bg-size-[1.8rem] md:bg-[1.2rem_center]
                                                " autocomplete="off">
                                            <button type="button" class="bg-[url(/static/home/<USER>/icons/yanjing_yincang_o.png)] bg-no-repeat bg-[12px_center] bg-size-[1.25rem] rounded-md top-1/2 -translate-y-1/2 absolute right-2 w-10 h-full cursor-pointer
                                                md:bg-size-auto" id="passwordEye">
                                            </button>
                                        </div>
                                        <div class="hidden flex error text-[#ff0000] mt-2 items-center gap-x-1.5
                                        md:text-base
                                        ">
                                            <img src="/static/home/<USER>/icons/tis.png" alt=""> 8-16
                                            characters must contain both digits and letters
                                        </div>
                                    </div>

                                    <div class="input-item">
                                        <label for="password_confirm" class="Roboto_Bold">
                                            <span class="text-[#ff0000] align-text-top">*</span> Confirm Password
                                        </label>
                                        <div class="relative flex flex-col mt-2">
                                            <input type="password" name="password_confirm"
                                                id="password_confirm" placeholder="Advise a secure password"
                                                class="w-full h-[3.125rem] border border-[#d8e2ff] rounded-md bg-white bg-[url(/static/home/<USER>/icons/mima.png)] bg-no-repeat bg-[12px_center] bg-size-[1.25rem] px-5 pl-12 pr-12
                                                md:h-[5rem] md:pl-16 md:bg-size-[1.8rem] md:bg-[1.2rem_center]
                                                " autocomplete="off">
                                            <button type="button" class="bg-[url(/static/home/<USER>/icons/yanjing_yincang_o.png)] bg-no-repeat bg-[12px_center] bg-size-[1.25rem] rounded-md top-1/2 -translate-y-1/2 absolute right-2 w-10 h-full cursor-pointer
                                                md:bg-size-auto" id="passwordEye2">
                                            </button>
                                        </div>
                                        <div class=" hidden flex error text-[#ff0000] mt-2 items-center gap-x-1.5
                                        md:text-base
                                        ">
                                            <img src="/static/home/<USER>/icons/tis.png" alt=""> 8-16
                                            characters must contain both digits and letters
                                        </div>
                                    </div>

                                    <div class="input-item mt-2
                                        md:mt-5 md:text-right
                                        ">
                                        <button type="submit"
                                            class="bg-[#f08411] text-white rounded-full text-base py-2 w-full cursor-pointer Roboto_Bold
                                                md:h-[4.375rem] md:inline-block md:w-[17.5rem] md:text-xl md:rounded-full"
                                            id="ModifySubmitBtn">Submit</button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>

                </div>
            </div>

        </div>
    </section>

    <section class="flex items-center justify-center py-6 md:py-10 border-t border-[#e0eaff]">
    <figure>
        <img src="/static/home/<USER>/logo.png" alt="logo" class="w-[9.375rem] md:w-[10rem]" />
        <figcaption class="sr-only">logo</figcaption>
    </figure>
</section>

<footer class="bg-[#155290]">
    <div
        class="flex flex-wrap justify-around max-w-2xs mx-auto text-white pt-8 pb-6 gap-y-3 md:max-w-max md:gap-x-10 md:text-xl">
        <a href="/">
            Open DEL
        </a>
        <a href="/about">
            About
        </a>
        <a href="/contact">
            Contact Us
        </a>
        <a href="/privacy/terms">
            Terms of Service
        </a>
        <a href="/privacy/">
            Privacy Agreement
        </a>
    </div>
    <div class="border-t border-[#2873bf] p-4">
        <p class="text-xs text-center text-white md:text-xl">
            © 2025 HitGen Inc. All Rights Reserved. <a href="https://www.miit.gov.cn/" target="_blank"
                rel="noopener noreferrer">蜀ICP备16024889-1号</a>
        </p>
    </div>

    <!-- 机器人 -->
    <div class="robot fixed right-5 top-1/2 z-50 -translate-y-1/2">
        <div class="robot-img relative w-14 h-14 border border-white rounded-full md:w-20 md:h-20">
            <span
                class="absolute inline-flex h-full w-full animate-ping rounded-full bg-[#105eb3] opacity-75 -z-[1]"></span>
            <a href="" _target="_blank">
                <img src="/static/home/<USER>/robot.png" alt="robot" class="w-full h-full object-cover" />
                <span class="absolute w-5 h-5 bg-[#f08411] text-xs rounded-full text-white flex items-center justify-center -top-2 right-0
                    md:w-6 md:h-6 md:text-base md:right-3">1</span>
            </a>
        </div>
        <div
            class="robot-text absolute bg-white right-[110%] bottom-0 min-w-[13.5rem] rounded-xl rounded-br-none p-4 text-sm md:min-w-[25.9375rem] md:p-7 md:text-xl border-[#dfe7ff] border">
            <span
                class="absolute w-5 h-5 md:w-7 md:h-7 rounded-full bg-[url(/static/home/<USER>/icons/close.png)] bg-no-repeat bg-center bg-cover -left-6 top-0 cursor-pointer md:-left-10"
                id="btn_close"></span>
            <p class="leading-6 md:leading-8">
                welcome back! still wondering if we are a good match?<img src="/static/home/<USER>/icons/515.png" alt=""
                    class="mx-1.5 w-[1.3rem] inline-block"> How can we help you today?
            </p>
        </div>
    </div>

    <!-- 置顶 -->
    <div class="fixed cursor-pointer right-5 bottom-5 w-10 h-10 bg-[#f08411] md:w-14 md:h-14 rounded-full justify-center items-center z-50 hidden"
        id="popup">
        <img src="/static/home/<USER>/icons/xiazai.png" alt="" class="w-3 md:w-[1.3rem]" />
    </div>
</footer>

<div class="modal-container fixed top-0 left-0 bottom-0 right-0 bg-[rgba(21,82,144,0.5)] hidden z-50" id="pop_container">
    <section class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white border-t-2 border-[#155290] rounded-xl overflow-hidden min-w-[90%] flex flex-col justify-center items-center py-10
    md:min-w-[43.75rem] md:min-h-[28.125rem]
    ">
        <div class="mb-6">
            <h1 class="text-2xl">
                Already have an account?
            </h1>
        </div>
        <div class="mb-7 flex flex-col gap-y-4 text-center min-w-[16.25rem]">
            <a href="/login" class="bg-[#155290] text-[#fff] text-lg py-3">
                Log In
            </a>
            <a href="/quote" onclick="window.location.href = '/quote?' + localStorage.getItem('quoteParams'); return false;" class="bg-[#e0eaff] text-[#155290] text-lg py-3">
                Not now
            </a>
        </div>
        <div class="text-base text-[#333]">
            Don t have an account? <a href="/login/register" class="underline text-[#f08411] ml-1">Sign Up</a>
        </div>
        <div class="close_btn absolute w-8 h-8 rounded-full top-2 right-3 flex items-center justify-center bg-[#155290] text-white cursor-pointer"  data-close-modal>x</div>
    </section>
</div>

    <script src="/static/home/<USER>/vendors/jquery-1.8.3.min.js"></script>
<script src="/static/home/<USER>/vendors/swiper-bundle.min.js"></script>
<script src="/static/home/<USER>/vendors/aos.js"></script>

<script src="/static/layer/layer.js"></script>
<script src="/static/home/<USER>/encapsulate.js"></script>
<script src="/static/home/<USER>/index.js"></script>

<script src="/static/home/<USER>/TabSwitch.js"></script>
<script src="/static/home/<USER>/ShowMore.js"></script>
<script src="/static/home/<USER>/MultiSelect.js"></script>

<script>
    AOS.init(); //延迟加载动画效果
</script>

<script>
    // 显示弹窗的函数
    function showQuotePopup(params) {
        // 存储参数到本地存储或全局变量
        localStorage.setItem('quoteParams', params);

        $('#pop_container').removeClass('hidden').addClass('block');
        return false; // 阻止默认行为
    }

    // 统一关闭所有弹窗
    $(document).on('click', '[data-close-modal]', function() {
        $(this).closest('.modal-container').removeClass('block').addClass('hidden');
    });
</script>

<script>
    $('#EmptyMessage').on('click', function() {
        $.ajax({
            url: '/clear-message/',
            type: 'GET',
            dataType: 'json',
            success: function(response) {
                console.log(response)
                if (response.code === 1) {
                    // 清空消息内容
                    $('#message_cont ul').empty();
                    // 隐藏查看更多按钮
                    $('#message_more').hide();
                    // 显示无消息提示（移除hidden类）
                    $('#no_message').removeClass('hidden');
                }
            },
            error: function(xhr, status, error) {
                console.error("error:", error);
            }
        });
    });
</script>


    <script src="/static/home/<USER>/TabSwitch.js"></script>


    <script>
        initTabSwitch('.user-tab-item', '.profile-item');

        document.addEventListener('DOMContentLoaded', function () {
            // 处理 select 元素的颜色变化
            const handleSelectColor = (select) => {
                const updateColor = () => {
                    select.style.color = select.value === "" ? "#ddd" : "#333";
                };
                select.addEventListener('change', updateColor);
                updateColor(); // 初始化颜色
            };

            // 初始化所有 country select
            document.querySelectorAll('select.country').forEach(handleSelectColor);

           // 表单验证配置
            const validationFields = [
                { id: 'country', name: 'Country' },
                { id: 'first_name', name: 'First Name' },
                { id: 'last_name', name: 'Last Name' },
                { id: 'organization', name: 'Organization' },
                { id: 'phone', name: 'Phone' }
            ];
            // 表单验证函数
            const validateField = (field) => {
                const input = document.getElementById(field.id);
                if (!input) return { valid: true }; // 字段不存在时跳过验证

                const value = input.value.trim();
                const errorDiv = input.parentElement.parentElement.querySelector('.error');

                if (!value) {
                    errorDiv?.classList.remove('hidden');
                    return { valid: false, element: input };
                }

                errorDiv?.classList.add('hidden');
                return { valid: true };
            };

            //用户资料修改
            $('#userForm').on('submit', function(e) {
                //阻止表单提交
                e.preventDefault();

                var valid = true;

                $('.error').addClass('hidden'); // 隐藏所有错误提示

                // 验证所有字段
                const validationResults = validationFields.map(validateField);
                const isValid = validationResults.every(result => result.valid);
                if (!isValid) {
                    // 找到第一个无效的输入框并聚焦
                    const firstInvalid = validationResults.find(result => !result.valid);
                    firstInvalid?.element?.focus();
                    return false;
                }

                // 禁用提交按钮
                var $submitBtn = $('#submitBtn');
                $submitBtn.prop('disabled', true).addClass('opacity-50 cursor-not-allowed');

                // 获取表单元素
                var form = $(this)[0]; // 获取原生 DOM 元素
                var formData = new FormData(form); // 创建 FormData 对象
                // 发送AJAX请求
                $.ajax({
                    url: '/user/edit',
                    type: 'POST',
                    data: formData,
                    dataType: 'json',
                    processData: false, // 告诉 jQuery 不要处理数据
                    contentType: false, // 告诉 jQuery 不要设置 content-Type 请求头
                    success: function(data) {
                        if (data.code === 1) {
                            //修改成功
                            layer.msg(data.msg, { icon: 1, time: 2000 }, () => {
                                location.href = data.url;
                            });
                        } else {
                            //修改失败
                            layer.msg('Error: ' + data.msg, { icon: 2 });
                        }
                    },
                    error: function(xhr, status, error) {
                        layer.msg('An error occurred: ' + error, { icon: 2 });
                    },
                    complete: function() {
                        // 无论成功失败，都重新启用按钮
                        $submitBtn.prop('disabled', false).removeClass('opacity-50 cursor-not-allowed');
                    }
                });
            });

            // 处理文件上传
            const fileBtn = document.getElementById('file_btn');
            const userView = document.getElementById('user_view');
            fileBtn.addEventListener('change', function (e) {
                const file = e.target.files[0];
                if (!file) return;

                // 检查文件类型
                const validTypes = ['image/jpeg', 'image/jpg', 'image/png'];
                if (!validTypes.includes(file.type)) {
                    alert('只能上传 JPG 或 PNG 格式的图片！');
                    fileBtn.value = ''; // 清空选择
                    return;
                }

                // 创建本地预览URL
                const imageUrl = URL.createObjectURL(file);
                userView.src = imageUrl;

                // 清理URL对象
                userView.onload = function () {
                    URL.revokeObjectURL(imageUrl);
                };
            });
            // 处理删除按钮
            const deleteBtn = document.getElementById('Delete_btn');
            deleteBtn.addEventListener('click', function () {
                userView.src = '/static/home/<USER>/user-2.jpg'; // 重置为默认图片
                fileBtn.value = ''; // 清空文件选择
            });
        });

        document.addEventListener('DOMContentLoaded', function () {
            // 导航切换
            const tabItems = document.querySelectorAll('.user-tab-item');
            const profileItems = document.querySelectorAll('.profile-item');

            tabItems.forEach((tab, index) => {
                tab.addEventListener('click', () => {
                    // 移除所有tab的active类
                    tabItems.forEach(item => item.classList.remove('active'));
                    // 为当前点击的tab添加active类
                    tab.classList.add('active');

                    // 处理对应的profile-item
                    profileItems.forEach((item, i) => {
                        if (i === index) {
                            item.classList.remove('hidden');
                        } else {
                            item.classList.add('hidden');
                        }
                    });
                });
            });

            // 修改密码
            // 通用验证函数
            function validateField(input, rules) {
                const value = input.value.trim();
                const error = input.parentElement.parentElement.querySelector('.error');
                if (error) error.classList.add('hidden');

                for (const rule of rules) {
                    if (!rule.validate(value)) {
                        if (error) {
                            error.innerHTML = rule.message;
                            error.classList.remove('hidden');
                        }
                        return false;
                    }
                }
                return true;
            }

            // 验证规则
            const validationRules = {
                email: [
                    {
                        validate: value => value.length > 0,
                        message: 'Please enter your email address.'
                    },
                    {
                        validate: value => /^[\w.-]+@[\w.-]+\.[a-zA-Z]{2,}$/.test(value),
                        message: 'Please enter the correct email format'
                    }
                ],
                password: [
                    {
                        validate: value => value.length > 0,
                        message: '<img src="/static/home/<USER>/icons/tis.png" alt=""> Please enter password'
                    },
                    {
                        validate: value => /^(?=.*[A-Za-z])(?=.*\d).{8,16}$/.test(value),
                        message: '<img src="/static/home/<USER>/icons/tis.png" alt=""> 8-16 characters must contain both digits and letters'
                    }
                ]
            };

            // 发送邮件验证码
            $('#SendOtp').click(function() {
                var $btn = $(this); // 保存按钮引用

                var $emailInput = $btn.prev('input'); // 获取邮箱输入框
                var $error = $btn.closest('.input-item').find('.error'); // 错误提示元素
                var emailReg = /^[\w.-]+@[\w.-]+\.[a-zA-Z]{2,}$/; // 邮箱正则

                $error.addClass('hidden'); // 先隐藏错误提示

                // 验证邮箱是否为空
                if (!$emailInput.val()) {
                    $error.text('Please fill in the email information').removeClass('hidden');
                    $emailInput.focus();
                    return;
                }

                // 验证邮箱格式
                if (!emailReg.test($emailInput.val())) {
                    $error.text('Please enter a valid email address').removeClass('hidden');
                    $emailInput.focus();
                    return;
                }

                // 禁用发送按钮
                $btn.prop('disabled', true).addClass('opacity-50');

                // 发送AJAX请求
                $.ajax({
                    url: '/send_captcha',
                    type: 'POST',
                    data: {email: $emailInput.val(), type: 'login'},
                    dataType: 'json',
                    success: function(data) {
                        if (data.code === 1) {
                            //发送成功
                            layer.msg(data.msg, { icon: 1, time: 2000 });
                        } else {
                            //发送失败
                            layer.msg('Error: ' + data.msg, { icon: 2 });
                        }
                    },
                    error: function(xhr, status, error) {
                        layer.msg('An error occurred: ' + error, { icon: 2 });
                    },
                    complete: function() {
                        // 无论成功失败，都重新启用按钮
                        $btn.prop('disabled', false).removeClass('opacity-50');
                    }
                });
            });

            // 修改密码
            $('#securityForm').on('submit', function(e) {
                //阻止表单提交
                e.preventDefault();

                let valid = true;
                let firstInvalidInput = null;

                // 验证邮箱
                const emailInput = document.getElementById('profile_email');
                if (!validateField(emailInput, validationRules.email)) {
                    valid = false;
                    firstInvalidInput = firstInvalidInput || emailInput;
                }

                // 验证验证码
                const captchaInput = document.getElementById('captcha');
                if (!validateField(captchaInput, [{
                    validate: value => value.length > 0,
                    message: 'Please enter the verification code'
                }])) {
                    valid = false;
                    firstInvalidInput = firstInvalidInput || captchaInput;
                }

                // 验证密码
                const pwdInput = document.getElementById('password');
                if (!validateField(pwdInput, validationRules.password)) {
                    valid = false;
                    firstInvalidInput = firstInvalidInput || pwdInput;
                }

                // 验证确认密码
                const confirmPwdInput = document.getElementById('password_confirm');
                const confirmPwdRules = [
                    {
                        validate: value => value.length > 0,
                        message: 'Please confirm your password'
                    },
                    {
                        validate: value => value === pwdInput.value,
                        message: 'The passwords entered twice are inconsistent'
                    }
                ];
                if (!validateField(confirmPwdInput, confirmPwdRules)) {
                    valid = false;
                    firstInvalidInput = firstInvalidInput || confirmPwdInput;
                }

                if (!valid) {
                    firstInvalidInput.focus();
                    return false
                }

                // 禁用提交按钮
                var $submitBtn = $('#ModifySubmitBtn');
                $submitBtn.prop('disabled', true).addClass('opacity-50 cursor-not-allowed');

                // 发送AJAX请求
                $.ajax({
                    url: '/user/editpassword',
                    type: 'POST',
                    data: $(this).serialize(),
                    dataType: 'json',
                    success: function(data) {
                        if (data.code === 1) {
                            //注册成功
                            layer.msg(data.msg, { icon: 1, time: 2000 }, () => {
                                location.href = data.url;
                            });
                        } else {
                            //注册失败
                            layer.msg('Error: ' + data.msg, { icon: 2 });
                        }
                    },
                    error: function(xhr, status, error) {
                        layer.msg('An error occurred: ' + error, { icon: 2 });
                    },
                    complete: function() {
                        // 无论成功失败，都重新启用按钮
                        $submitBtn.prop('disabled', false).removeClass('opacity-50 cursor-not-allowed');
                    }
                });
            });

            // 密码显示/隐藏处理
            function bindPasswordEye(eyeId) {
                const eye = document.getElementById(eyeId);
                if (eye) {
                    eye.addEventListener('click', function () {
                        const input = eye.parentElement.querySelector('input');
                        if (input) {
                            input.type = input.type === 'password' ? 'text' : 'password';
                            eye.style.backgroundImage = `url('/static/home/<USER>/icons/yanjing_yincang_${input.type === 'password' ? 'o' : '1'}.png')`;
                        }
                    });
                }
            }
            bindPasswordEye('passwordEye');
            bindPasswordEye('passwordEye2');
        });
    </script>

</body>

</html>