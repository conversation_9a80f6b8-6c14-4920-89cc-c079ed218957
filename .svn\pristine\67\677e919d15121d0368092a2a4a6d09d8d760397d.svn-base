<?php

namespace app\home\controller;

use app\home\controller\Common;

use think\facade\Db;

use app\validate\Users as UsersValidate;
use think\exception\ValidateException;

use think\facade\Filesystem;
use think\facade\Validate;

use think\facade\Cache;

use think\facade\View;

use Carbon\Carbon;
use think\Request;

class User extends Common
{
    public function index()
    {
        if(!session('userId')) {
            // 直接渲染错误页面
            return view('error/login_error');
        }

        $country = getCountry();

        $this->setLeft();

        return view("", [
            "country" => $country,
        ]);
    }

    //修改个人资料
    public function edit()
    {
       if ($this->request->isPost()) {
            if(!session('userId')) {
                $this->error('login first');
            }

            $user_id = session('userId');

            $data = $this->request->post();
            //验证数据
            try {
                validate(UsersValidate::class)->scene('front-edit')->check($data);
            } catch (ValidateException $e) {
                // 验证失败 输出错误信息
                $this->error($e->getError());
            }

            if($_FILES['avatar']['name']) $data['avatar'] = $this->upload(request()->file("avatar"), "avatar");

            $data['update_time'] = date("Y-m-d H:i:s");
            $s = Db::name("User")->where("id", $user_id)->update($data);
            if ($s) {
                $this->success('edit success', "/user");
            } else {
                $this->error("The system is busy, please try again later!");
            }
        }
    }

    //修改密码
    public function editpassword()
    {
       if ($this->request->isPost()) {
            if(!session('userId')) {
                $this->error('login first');
            }

            $user_id = session('userId');

            $data = $this->request->post();
            //验证数据
            try {
                validate(UsersValidate::class)->scene('front-editpassword')->check($data);
            } catch (ValidateException $e) {
                // 验证失败 输出错误信息
                $this->error($e->getError());
            }

            $email = Db::name("User")->where("id", $user_id)->value("email");
            //验证邮箱验证码
            $cacheCaptcha = Cache::get('mail_captcha_' . $email);
            if (!empty($cacheCaptcha) && $cacheCaptcha == $data['captcha']) {
                // 验证成功后删除验证码
                // Cache::set('mail_captcha_' . $email, null);
            } else {
                $this->error("Verification code error or expired!");
            }

            // 生成salt
            $data['salt'] = generateSalt();
            // 明码进行加盐hash解决
            $data['password'] = generateHashPassword($data['password'], $data['salt']);

            $data['update_time'] = date("Y-m-d H:i:s");
            $s = Db::name("User")->where("id", $user_id)->strict(false)->update($data);
            if ($s) {
                $this->success('edit success', "/user");
            } else {
                $this->error("The system is busy, please try again later!");
            }
        }
    }

    //试用券
    public function coupon(){
        if(!session('userId')) {
            return view('error/login_error');
        }

        $user_id = session('userId');

        //用户未使用的试用券
        $where = [
                "uc.user_id" => $user_id,
                "uc.is_used" => 0,  //未使用
            ];
        $currentTime = date('Y-m-d');
        $coupon_active = Db::name("User_coupon")->alias("uc")
                ->leftjoin("Coupon c", "uc.coupon_id=c.id")
                ->where($where)
                ->whereTime('uc.start_time', '<=', $currentTime) // 开始时间小于等于当前时间
                ->whereTime('uc.end_time', '>=', $currentTime)   // 结束时间大于等于当前时间
                ->column("uc.*, c.name, c.description");

        //已使用的试用券
        $where = [
                "uc.user_id" => $user_id,
                "uc.is_used" => 1,  //已使用
            ];
        $coupon_used = Db::name("User_coupon")->alias("uc")
                ->leftjoin("Coupon c", "uc.coupon_id=c.id")
                ->where($where)
                ->column("uc.*, c.name, c.description");

        //灰色试用券
        $where = [
            "coupon_type" => 1,  //灰色试用券
            "del_status" => 0,  //未删除
        ];
        $coupon_exchange = Db::name("Coupon")
                ->where($where)
                ->select()
                ->each(function ($item) use($user_id) {
                    //默认可兑换
                    $item['exchange_status'] = 0;

                    //未使用，并且未过期，则不能兑换
                    $where = [
                        "coupon_id" => $item['id'],
                        "user_id" => $user_id,
                        "is_used" => 0,  //未使用
                    ];
                    $currentTime = date('Y-m-d');
                    $user_coupon = Db::name("User_coupon")
                        ->where($where)
                        ->whereTime('start_time', '<=', $currentTime) // 开始时间小于等于当前时间
                        ->whereTime('end_time', '>=', $currentTime)   // 结束时间大于等于当前时间
                        ->find();
                    if(!empty($user_coupon)) {
                        $item['exchange_status'] = 1;  //不能兑换
                        $item['end_time'] = $user_coupon['end_time'];  //兑换券过期时间
                    }

                    return $item;
                });

        $this->setLeft();

        return view("", [
            "coupon_active" => $coupon_active,
            "coupon_used" => $coupon_used,
            "coupon_exchange" => $coupon_exchange,
        ]);
    }

    //兑换试用券
    public function coupon_exchange() {
        if ($this->request->isPost()) {
            if(!session('userId')) {
                $this->error('login first');
            }

            $user_id = session('userId');

            $id = $this->request->post("id");

            $where = [
                "id" => $id,
                "coupon_type" => 1,  //灰色试用券
                "del_status" => 0,  //未删除
            ];
            $coupon = Db::name("Coupon")->where($where)->find();
            if(empty($coupon)) {
                $this->error("coupon not exist!");
            }

            //未使用，并且未过期，则不能兑换
            $where = [
                "coupon_id" => $id,
                "user_id" => $user_id,
                "is_used" => 0,  //未使用
            ];
            $currentTime = date('Y-m-d');
            $user_coupon = Db::name("User_coupon")
                ->where($where)
                ->whereTime('start_time', '<=', $currentTime) // 开始时间小于等于当前时间
                ->whereTime('end_time', '>=', $currentTime)   // 结束时间大于等于当前时间
                ->find();
            if(!empty($user_coupon)) {
                $this->error("You have already redeemed this trial coupon!");
            }

            $where = [
                "id" => $user_id,
                "status" => 1,  //正常
            ];
            $user = Db::name("User")->field("points")->where($where)->find();
            if(empty($user)) {
                $this->error("The user does not exist, please log in again");
            }

            if($user['points'] < $coupon['points']) {
                $this->error("Insufficient points!");
            }

            //兑换试用券
            $coupon['points'] = (int)$coupon['points'];  //转换为数字
            $s = Db::name('User')
                ->where('id', $user_id)
                ->where('points', '>=', $coupon['points'])
                ->dec('points', $coupon['points'])
                ->update();
            if ($s) {
                //添加用户试用券
                $coupon_data = [
                    "coupon_id" => $id,
                    "user_id" => $user_id,
                    "start_time" => date("Y-m-d"),  //开始时间
                    "end_time" => Carbon::now()->addYear()->toDateString(),  //结束时间，1年后
                    "create_time" => date("Y-m-d H:i:s"),
                ];
                Db::name("User_coupon")->insertGetId($coupon_data);

                //增加积分记录
                Db::name("User_points")->insert([
                    "user_id" => $user_id,
                    "coupon_id" => $id,
                    "points" => -$coupon['points'],
                    "content" => "Exchange trial coupons",
                    "create_time" => date("Y-m-d H:i:s"),
                ]);

                $this->success("Exchange successful");
            }

        }
    }

    //用户积分
    public function points() {
        if(!session('userId')) {
            return view('error/login_error');
        }

        $points = Db::name("User_points")->where("user_id", session('userId'))->order("id desc")->select();

        $this->setLeft();

        return view("", [
            "points" => $points,
        ]);
    }

    //用户消息
    public function message(){
        if(!session('userId')) {
            return view('error/login_error');
        }

        //所有消息
        $user_all_message = Db::name("User_message")
            ->where("user_id", session('userId'))
            ->order("create_time desc")
            ->select()
            ->each(function ($item) {
                $item['link_url'] = "";
                //消息类型（0创建订单 1系统模板消息 2用户发送留言咨询 3帖子回复 4帖子提及 5回复提及 6私信 7审核结果通知 8留言咨询回复 9给产品经理发审核通知 10公共新闻通知）
                if($item['type'] == 0){
                    $order = Db::name("Order")->field("id, parent_id")->where("id", $item['main_id'])->find();
                    if($order['parent_id'] == 0){
                        $item['link_url'] = "/user/order-detail-".$item['main_id'];
                    } else {
                        $item['link_url'] = "/user/order-detail-".$order['parent_id'];
                    }
                } else if(in_array($item['type'], [3, 4, 5, 7])) {
                    $item['link_url'] = "/iCommunity/post/".$item['main_id'];
                } else if($item['type'] == 10) {
                    $seo_url = Db::name("News")->where("id", $item['main_id'])->value("seo_url");
                    $item['link_url'] = "/news/".$seo_url;
                }

                return $item;
            });

        $where = [
            "user_id" => session('userId'),
            "is_read" => 0,  //未读
        ];
        //更新为已读
        Db::name("User_message")->where($where)->update([
            "is_read" => 1
        ]);

        $this->setLeft();

        return view("", [
            "user_all_message" => $user_all_message
        ]);
    }

    //清除所有未读消息
    public function clearMessage(){
        if(!session('userId')) {
            return view('error/login_error');
        }

        $where = [
            "user_id" => session('userId'),
            "is_read" => 0,  //未读
        ];
        //更新为已读
        Db::name("User_message")->where($where)->update([
            "is_read" => 1
        ]);

        $this->success("clear success");
    }

    //订单列表
    public function order() {
        if(!session('userId')) {
            return view('error/login_error');
        }

        //进行中的订单
        $orders_progress = $this->getOrder(0);

        //已完成的订单
        $orders_completed = $this->getOrder(1);

        //已取消的订单
        $orders_cancelled = $this->getOrder(2);

        $this->setLeft();

        return view("", [
            "orders_progress" => $orders_progress,
            "orders_completed" => $orders_completed,
            "orders_cancelled" => $orders_cancelled,
        ]);
    }

    private function getOrder($order_status){
        $where = [
            "parent_id" => 0,
            "user_id" => session('userId'),
            "order_status" => $order_status,  //订单状态 0进行中 1已完成 2已取消
        ];
        $orders = Db::name("Order")
            ->where($where)
            ->order("create_time desc")
            ->select()
            ->each(function ($item){
                //产品信息
                $product = Db::name("Product")->where("id", $item['product_id'])->find();
                $product["product_progress"] = Db::name("Progress")->where("id", $item['product_progress_id'])->value("name");
                $item['product'] = $product;

                //服务信息
                $item['service_current'] = [];
                if($item['service_id']) {
                    $service_current = Db::name("Service")->where("id", $item['service_id'])->find();
                    $service_current['service_progress'] = Db::name("Progress")->where("id", $item['service_progress_id'])->value("name");
                    $item['service_current'] = $service_current;
                }

                //后续服务
                $item['service_son'] = Db::name("Order")->alias("o")
                    ->field("s.name,s.seo_url,s.id, p.name as service_progress")
                    ->where("o.parent_id", $item['id'])
                    ->leftjoin("Progress p", "o.service_progress_id=p.id")
                    ->leftjoin("Service s", "o.service_id=s.id")
                    ->select();

                return $item;
            });

        return $orders;
    }

    //订单详情
    public function order_detail() {
        if(!session('userId')) {
            return view('error/login_error');
        }

        $id = input("id");

        $where = [
            "id" => $id,
            "parent_id" => 0,  //父订单
            "user_id" => session("userId"),
        ];
        $order = Db::name("Order")->where($where)->find();

        if(empty($order)) {
            return view("error/404")->code(404);
            // $this->error("The order does not exist!", "/user/order");
        }
        if($order['order_status'] == 2){
            return view("error/404")->code(404);
            // $this->error("The order has been cancelled!", "/user/order");
        }

        //产品信息
        $product = Db::name("Product")->where("id", $order['product_id'])->find();

        //产品进度列表
        $where = [
            "main_id" => $order['product_id'],
            "type" => 0,  //产品
        ];
        $product_progress = Db::name("Progress")->where($where)->order("sort asc")->select()->toArray();

        //产品进度是否完成
        $last_progress_id = $product_progress?end($product_progress)['id']:0;
        $product['progress_status'] = $order['product_progress_id'] == $last_progress_id ? "completed":"proceed";

        //产品当前进度
        $product['current_progress'] = Db::name("Progress")->where("id", $order['product_progress_id'])->find();

        // 计算进度百分比
        $current_index = 0;
        if($product['current_progress']) {
            foreach($product_progress as $index => $progress) {
                if($progress['id'] == $product['current_progress']['id']) {
                    $current_index = $index + 1;
                    break;
                }
            }
        }
        $product['width_percentage'] = $current_index;

        //产品保密文件
        $where = [
            "order_id" => $id,
            "type" => 0,  //产品
            "file_type" => 0,  //保密文件
        ];
        $product_files = Db::name("Order_file")->where($where)->order("id desc")->select();

        //产品服务订单
        $order_services = [];
        if($order['service_id']) {
            //服务信息
            $order_services[$order['service_id']] = $this->getService($order['service_id'], $id);
        }

        //后续服务
        $services = Db::name("Order")->where("parent_id", $id)->select();
        foreach($services as $val){
            $order_services[$val['service_id']] = $this->getService($val['service_id'], $val['id']);
        }

        //产品所有服务
        $services = Db::name("Product_relation")->alias("pr")
            ->field("s.*")
            ->leftjoin("Service s", "pr.related_id=s.id")
            ->where(["product_id"=>$order['product_id'], "type"=>1])
            ->order("pr.sort asc")
            ->select()
            ->toArray();
        foreach($services as $key=>$val){
            $services[$key]['lists'] = isset($order_services[$val['id']]) ? $order_services[$val['id']]:[];
        }

        return view("", [
            "order" => $order,  //订单信息
            "product" => $product,  //产品信息
            "product_progress" => $product_progress,  //产品进度列表
            "product_files" => $product_files,  //产品保密文件
            "services" => $services,  //产品所有服务
        ]);
    }

    //获取服务资料
    private function getService($service_id, $order_id){
        //服务信息
        $service['order'] = Db::name("Order")->where("id", $order_id)->find();

        //服务进度列表
        $where = [
            "main_id" => $service_id,
            "type" => 1,  //服务
        ];
        $service['progress'] = Db::name("Progress")->where($where)->order("sort asc")->select()->toArray();

        //服务进度是否完成
        $last_progress_id = end($service['progress'])['id'];
        $service['progress_status'] = $service['order']["service_progress_id"] == $last_progress_id ? "completed":"proceed";

        //服务当前进度
        $service['current_progress'] = Db::name("Progress")->where("id", $service['order']['service_progress_id'])->find();

        // 计算进度百分比
        $current_index = 0;
        if($service['current_progress']){
            foreach($service['progress'] as $index => $progress) {
                if($progress['id'] == $service['current_progress']['id']) {
                    $current_index = $index + 1;
                    break;
                }
            }
        }
        $service['width_percentage'] = $current_index;

        //用户上传资料
        $where = [
            "order_id" => $order_id,
            "type" => 1,  //服务
            "file_type" => 1,  //用户资料
        ];
        $service['user_files'] = Db::name("Order_file")->where($where)->order("id desc")->select()->toArray();
        $service['last_userfile_time'] = $service['user_files']?end($service['user_files'])['create_time']:"";

        //保密文件
        $where = [
            "order_id" => $order_id,
            "type" => 1,  //服务
            "file_type" => 0,  //保密文件
        ];
        $service['service_files'] = Db::name("Order_file")->where($where)->order("id desc")->select()->toArray();
        $service['last_servicefile_time'] = $service['service_files']?end($service['service_files'])['create_time']:"";

        return $service;
    }

    //上传用户资料
    public function upload_userfile() {
        if ($this->request->isPost()) {
            // 验证登录状态
            if(!session('userId')) {
                return json(['code' => 0, 'msg' => 'Please log in', 'url' => '/login']);
            }

            $data = $this->request->post();
            if(empty($data['order_id'])) {
                return json(['code' => 0, 'msg' => 'Parameter error: Missing order ID']);
            }

            // 验证订单是否属于当前用户
            $parent_id = Db::name("Order")->where("id", $data['order_id'])->value("parent_id");
            $orderExists = Db::name("Order")->where([
                "id" => $parent_id,
                "user_id" => session("userId"),
                "order_status" => 0
            ])->find();
            if(!$orderExists) {
                return json(['code' => 0, 'msg' => 'Order information is incorrect or has been completed']);
            }

            // 获取上传文件
            $files = $this->request->file('files');
            if (empty($files)) {
                return json(['code' => 0, 'msg' => 'Please select the file to upload']);
            }

            // 验证规则
            $validate = Validate::rule([
                'files' => 'file|fileExt:jpg,jpeg,png,gif,pdf,doc,docx,xls,xlsx,ppt,pptx|fileSize:10240000' // 10MB
            ]);

            $data['create_time'] = date("Y-m-d H:i:s");
            $data['type'] = 1;  //服务
            $data['file_type'] = 1;  //用户资料

            $successFiles = [];
            $failedFiles = [];

            // 处理多文件上传
            foreach ($files as $file) {
                $originalName = $file->getOriginalName();

                if (!$validate->check(['files' => $file])) {
                    $failedFiles[] = [
                        'name' => $originalName,
                        'error' => $validate->getError()
                    ];
                    continue;
                }

                try {
                    $path = Filesystem::disk('public')->putFile('userfile', $file);
                    //拼接URL路径,结果是 $picPath = /storage/20200825/***.jpg
                    $filePath = Filesystem::getDiskConfig('public', 'url') . '/' . str_replace('\\', '/', $path);

                    // 保存到数据库
                    $fileData = $data;
                    $fileData['file'] = $filePath;
                    $fileData['file_name'] = $originalName;

                    Db::name("Order_file")->insertGetId($fileData);

                    $successFiles[] = [
                        'name' => $originalName,
                        'path' => $filePath
                    ];
                } catch (\Exception $e) {
                    $failedFiles[] = [
                        'name' => $originalName,
                        'error' => $e->getMessage()
                    ];
                }
            }

            // 如果有文件上传失败
            if (!empty($failedFiles)) {
                return json([
                    'code' => 2,  // 2表示部分成功
                    'msg' => 'Partial file upload failed',
                    'success' => $successFiles,
                    'failed' => $failedFiles,
                    'url' => '/user/order-detail-'.$parent_id
                ]);
            }

            // 全部成功
            return json([
                'code' => 1,
                'msg' => 'Upload successful',
                'data' => $successFiles,
                'url' => '/user/order-detail-'.$parent_id
            ]);

        } else {
            return json(['code' => 0, 'msg' => 'illegal request']);
        }
    }

    //用户帖子列表
    public function post() {
        if(!session('userId')) {
            return view('error/login_error');
        }

        $post = Db::name("Forum_posts")->where("user_id", session('userId'))->order("create_time desc")->select();

        $this->setLeft();

        return view("", [
            "post" => $post,
        ]);
    }

    //用户论坛评论
    public function comment() {
        if(!session('userId')) {
            return view('error/login_error');
        }

        //我的评论
        $reply = Db::name("Forum_reply")->where("user_id", session("userId"))->select();

        $this->setLeft();

        return view("", [
            "reply" => $reply,
        ]);
    }

    //用户会话列表
    public function private_message() {
        if(!session('userId')) {
            return view('error/login_error');
        }

        $user_id = session('userId');

        // 查询当前用户参与的所有会话
        $conversations = $this->getPrivateMessageList($user_id);

        $this->setLeft();

        return view("", [
            "conversations" => $conversations,
        ]);
    }

    // 查询当前用户参与的所有会话列表，每个会话的最后一条信息、时间
    private function getPrivateMessageList($user_id) {
        // 获取用户已删除的会话ID列表
        $deletedConversations = Db::name('Private_conversation_delete')
            ->where('user_id', $user_id)
            ->column('conversation_id');

        $conversations = Db::name('Private_conversation')
            ->where(function($query) use ($user_id) {
                $query->where('user1_id', $user_id)
                    ->whereOr('user2_id', $user_id);
            })
            ->whereNotIn('id', $deletedConversations) // 排除已删除的会话
            ->order('update_time', 'desc')
            ->select();

        $result = [];
        foreach ($conversations as $conversation) {
            // 获取对方用户ID
            $otherUserId = $conversation['user1_id'] == $user_id ? $conversation['user2_id'] : $conversation['user1_id'];
            $where = [
                "id" => $otherUserId,
                "status" => 1,  //正常
            ];
            $other_user = Db::name("User")->field("id, first_name, last_name, avatar")->where($where)->find();

            // 获取最后一条消息
            $lastMessage = Db::name('Private_message')
                ->where('conversation_id', $conversation['id'])
                ->order('create_time', 'desc')
                ->find();

            // 获取未读消息数
            $unreadCount = Db::name('Private_message')
                ->where('conversation_id', $conversation['id'])
                ->where('receiver_id', $user_id)
                ->where('is_read', 0)
                ->count();

            $result[] = [
                'conversation_id' => $conversation['id'],
                'other_user' => $other_user,
                'last_message' => $lastMessage,
                'unread_count' => $unreadCount,
                'update_time' => $conversation['update_time']
            ];
        }

        return $result;
    }

    //会话消息列表
    public function private() {
        if(!session('userId')) {
            return view('error/login_error');
        }

        $user_id = session('userId');

        $user_name = input("user");
        $parts = explode('_', $user_name); // 用空格分割字符串
        if(count($parts) < 2) {
            return view('error/404');
        }

        $user = Db::name("User")->where("id", $user_id)->find();

        //会话用户
        $first_name = $parts[0];
        $last_name = $parts[1];
        $where = [
            "first_name" => $first_name,
            "last_name" => $last_name,
            "status" => 1,
        ];
        $conversation_user = Db::name("User")->where($where)->find();
        if(empty($conversation_user) || $conversation_user['id'] == $user_id) {
            return view('error/404');
        }

        // 将对方用户添加到私信记录
        $user_conversations = Db::name('Private_conversation')
            ->where(function($query) use ($user_id, $conversation_user) {
                $query->where('user1_id', $user_id)
                    ->where('user2_id', $conversation_user['id']);
            })
            ->whereOr(function($query) use ($user_id, $conversation_user) {
                $query->where('user1_id', $conversation_user['id'])
                    ->where('user2_id', $user_id);
            })
            ->find();

        if(empty($user_conversations)) {
            $data = [
                "user1_id" => $user_id,
                "user2_id" => $conversation_user['id'],
                "create_time" => date("Y-m-d H:i:s"),
                "update_time" => date("Y-m-d H:i:s"),
            ];
            Db::name('Private_conversation')->insert($data);
        } else {
            // 检查并解除会话删除状态
            $this->restoreConversationIfDeleted($user_conversations['id'], $user_id);
        }

        // 查询当前用户参与的所有会话
        $conversations = $this->getPrivateMessageList($user_id);
        $chatData = [];
        foreach($conversations as $conversation) {
            // 获取会话下的所有未删除消息
            $conversations_messages = Db::name('Private_message')
                ->alias('m')
                ->leftJoin('Private_message_delete md', "m.id = md.message_id AND md.user_id = {$user_id}")
                ->where('m.conversation_id', $conversation['conversation_id'])
                ->where('md.message_id IS NULL') // 排除用户已删除的消息
                ->order('m.create_time', 'asc')
                ->select();

            // 获取对方用户信息
            $otherUser = $conversation['other_user'];
            // 组合用户名（假设first_name和last_name分别对应wu和xy）
            $fullName = trim($otherUser['first_name'] . ' ' . $otherUser['last_name']);
            $chatKey = str_replace(' ', '_', $fullName);

            // 处理消息列表
            $messages = [];
            if (!empty($conversations_messages)) {
                foreach ($conversations_messages as $message) {
                    $messageType = $message['sender_id'] == $user_id ? 'sent' : 'received';
                    $avatarUrl = $message['sender_id'] == $user_id ? $user['avatar'] : $otherUser['avatar'];
                    $userName = $message['sender_id'] == $user_id ? $user['first_name']." ".$user['last_name'] : $otherUser['first_name']." ".$otherUser['last_name'];

                    $msgItem = [
                        'id' => $message['id'],
                        'type' => $message['type'],
                        'content' => $message['content'],
                        'time' => date('M d, Y, H:i', strtotime($message['create_time'])),
                        'avatarUrl' => $avatarUrl,
                        "sender" => $messageType,
                        "userName" => $userName,
                    ];

                    // 处理文件或图片消息
                    if ($message['type'] === 'image') {
                        $msgItem['content'] = $message['content'] ?? '';
                    } else if ($message['type'] === 'file') {
                        $msgItem['content'] = [
                            'name' => $message['file_name'],
                            'size' => $message['file_size'],
                            'url' => $message['content'] ?? '',
                        ];
                    }

                    $messages[] = $msgItem;
                }
            }

            $chatData[$chatKey] = [
                'name' => $fullName,
                'lastTime' => date('M d, Y, H:i', strtotime($conversation['update_time'])),
                'unread' => $conversation['unread_count'],
                'messages' => $messages,
                "user_id" => $otherUser['id'],
                "avatarUrl" => $otherUser['avatar'],
                "conversation_id" => $conversation['conversation_id']
            ];
        }

        //当前会话的信息设置为已读
        if(!empty($user_conversations)) {
            $where = [
                "conversation_id" => $user_conversations['id'],
                "receiver_id" => $user_id,
                "is_read" => 0,  //未读
            ];
            Db::name("Private_message")->where($where)->update([
                "is_read" => 1, //已读
            ]);
        }

        return view("", [
            "conversation_user" => $conversation_user,  //当前会话用户
            "conversations" => $conversations,
            "chatData" => json_encode($chatData),
        ]);
    }

    //保存私信信息
    public function saveMessage() {
        if ($this->request->isPost()) {
            if(!session("userId")){
                $this->error("Login first");
            }

            $data = $this->request->post();

            $data['sender_id'] = session("userId");
            $data['is_read'] = 0;  //未读
            $data['create_time'] = date("Y-m-d H:i:s");

            $data['type'] = $data['type']=='sent' ? "text" : $data['type'];

            if($data['type'] == 'file') {
                //文件特殊处理
                $data['file_name'] = $data['content']['name'];
                $data['file_size'] = $data['content']['size'];
                $data['content'] = $data['content']['url'];
            }

            unset($data['id']);
            $id = Db::name("Private_message")->strict(false)->insertGetId($data);
            if ($id) {
                //更新会话最新时间
                Db::name("Private_conversation")->where("id", $data['conversation_id'])->save([
                    "update_time" => date("Y-m-d H:i:s")
                ]);

                // 检查并解除接收者的会话删除状态
                $this->restoreConversationIfDeleted($data['conversation_id'], $data['receiver_id']);

                $this->success('submit success', "/");
            } else {
                $this->error("The system is busy, please try again later!");
            }
        }
    }

    private function restoreConversationIfDeleted($conversation_id, $user_id) {
        // 检查会话是否被用户删除
        $deleted = Db::name('Private_conversation_delete')
            ->where('conversation_id', $conversation_id)
            ->where('user_id', $user_id)
            ->find();

        if ($deleted) {
            // 解除删除状态
            Db::name('Private_conversation_delete')
                ->where('conversation_id', $conversation_id)
                ->where('user_id', $user_id)
                ->delete();
        }
    }

    //上传私信文件
    public function uploadMessageFile(Request $request)
    {
       if ($this->request->isPost()) {
            if(!session('userId')) {
                $this->error('Login first');
            }

            $file = $request->file('file');
            $file_path = $this->uploadFile($file, "messages");
            if ($file_path) {
                $this->success($file_path);
            } else {
                $this->error("The system is busy, please try again later!");
            }
        }
    }


    private function setLeft() {
        //未读评论
        // $where = [
        //     "reply_to_user" => session("userId"),
        //     "status" => 1,  //审核通过
        //     "is_read" => 0,  //未读
        // ];
        // $reply_count = Db::name("Forum_reply")->where($where)->count();

        // View::assign("reply_count", $reply_count);

        //未读私信
        $where = [
            "receiver_id" => session("userId"),
            "is_read" => 0,  //未读
        ];
        $private_count = Db::name("Private_message")->where($where)->count();

        View::assign("private_count", $private_count);
    }

    //删除会话和消息
    public function deleteMessage() {
        if ($this->request->isPost()) {
            if(!session("userId")){
                $this->error("Login first");
            }

            $data = $this->request->post();

            if($data['type'] == 1) {
                //删除会话内的所有消息
                $conversation_messages_ids = Db::name("Private_message")->where("conversation_id", $data['conversation_id'])->column("id");
                foreach($conversation_messages_ids as $message_id) {
                    $where = [
                        "message_id" => $message_id,
                        "user_id" => session("userId")
                    ];
                    if(empty(Db::name("Private_message_delete")->where($where)->find())) {
                        Db::name("Private_message_delete")->insert([
                            "conversation_id" => $data['conversation_id'],
                            "message_id" => $message_id,
                            "user_id" => session("userId"),
                            "delete_time" => date("Y-m-d H:i:s")
                        ]);
                    }
                }
            } else {
                //删除整个会话
                $where = [
                    "conversation_id" => $data['conversation_id'],
                    "user_id" => session("userId"),
                ];
                if(empty(Db::name("Private_conversation_delete")->where($where)->find())) {
                    Db::name("Private_conversation_delete")->insert([
                        "conversation_id" => $data['conversation_id'],
                        "user_id" => session("userId"),
                        "delete_time" => date("Y-m-d H:i:s")
                    ]);
                }
            }

            $this->success("delete success");
        }
    }

    //提交下载日志
    public function downloadLog(Request $request) {
        if ($this->request->isPost()) {
            if(!session("userId")){
                $this->error("Login first");
            }

            $data = $this->request->post();

            if(empty($data['id']) || empty($data['main_id'])) {
                $this->error("parameter error");
            }

            $order_file = Db::name("Order_file")->where("id", $data['id'])->find();
            if(empty($order_file)) {
                $this->error("Data does not exist");
            }

            if($data['type'] == 0) {
                //产品
                $product = Db::name("Product")->where("id", $data['main_id'])->find();
            } else {
                //服务
                $product = Db::name("Service")->where("id", $data['main_id'])->find();
            }
            $data['product_name'] = $product['name'];
            if($product['manager_ids']) {
                $manager_ids = explode(",", $product['manager_ids']);
                $nameList = Db::name('User')
                    ->whereIn('id', $manager_ids)
                    ->column("CONCAT(first_name, ' ', last_name) as name");
                $nameList = array_column($nameList, 'name');
                $data['product_managers'] = implode(', ', $nameList);
            }

            $data['ip'] = $request->ip();
            $data['download_time'] = date("Y-m-d H:i:s");
            $data['order_file_id'] = $data['id'];
            $data['user_id'] = session("userId");

            $data['file'] = $order_file['file'];
            $data['file_name'] = $order_file['file_name'];
            $data['url'] = $order_file['url'];

            unset($data['id']);
            $s = Db::name("Download")->strict(false)->insertGetId($data);
            if ($s) {
                $this->success("Submitted successfully");
            } else {
                $this->error("The system is busy, please try again later!");
            }
        }
    }

}
