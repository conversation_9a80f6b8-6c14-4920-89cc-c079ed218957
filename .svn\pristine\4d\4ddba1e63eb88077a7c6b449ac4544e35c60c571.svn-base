/**
 * This configuration was generated using the CKEditor 5 Builder. You can modify it anytime using this link:
 * https://ckeditor.com/ckeditor-5/builder/#installation/NoJgNARCB0Bs0EYKQSEAGWsQHYCcALHiLOvjhQMzrogJEAclIDd+dTsBWByEAXgAsAtAGMAdsnRhgCMNOly56ALqQCo0bFENREFUA===
 */

const {
	ClassicEditor,
	Alignment,
	Autoformat,
	AutoImage,
	AutoLink,
	Autosave,
	BlockQuote,
	Bold,
	Bookmark,
	Code,
	CodeBlock,
	Emoji,
	Essentials,
	FindAndReplace,
	FontBackgroundColor,
	FontColor,
	FontFamily,
	FontSize,
	FullPage,
	Fullscreen,
	GeneralHtmlSupport,
	Heading,
	Highlight,
	HorizontalLine,
	HtmlComment,
	HtmlEmbed,
	ImageBlock,
	ImageCaption,
	ImageEditing,
	ImageInline,
	ImageInsert,
	ImageInsertViaUrl,
	ImageResize,
	ImageStyle,
	ImageTextAlternative,
	ImageToolbar,
	ImageUpload,
	ImageUtils,
	Indent,
	IndentBlock,
	Italic,
	Link,
	LinkImage,
	List,
	ListProperties,
	Markdown,
	MediaEmbed,
	Mention,
	PageBreak,
	Paragraph,
	PasteFromMarkdownExperimental,
	PasteFromOffice,
	PlainTableOutput,
	RemoveFormat,
	ShowBlocks,
	SimpleUploadAdapter,
	SourceEditing,
	SpecialCharacters,
	SpecialCharactersArrows,
	SpecialCharactersCurrency,
	SpecialCharactersEssentials,
	SpecialCharactersLatin,
	SpecialCharactersMathematical,
	SpecialCharactersText,
	Strikethrough,
	Style,
	Subscript,
	Superscript,
	Table,
	TableCaption,
	TableCellProperties,
	TableColumnResize,
	TableLayout,
	TableProperties,
	TableToolbar,
	TextPartLanguage,
	TextTransformation,
	Title,
	TodoList,
	Underline,
	WordCount
} = window.CKEDITOR;

const LICENSE_KEY =
	'eyJhbGciOiJFUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************.Hq7aA4rhNGDO_RqR5BLzE7I841QJkjUpjpRM1m3y1hRzIypsMAi40_YHMRnZBeHgNyB9-bf82mZ3uxpeYhFu5Q';

const editorConfig = {
	toolbar: {
		items: [
			'undo',
			'redo',
			'|',
			'sourceEditing',
			'|',
			'heading',
			'|',
			'fontSize',
			'fontFamily',
			'fontColor',
			'fontBackgroundColor',
			'|',
			'bold',
			'italic',
			'underline',
			'|',
			'emoji',         // 表情符号
			'specialCharacters', // 特殊字符
			'|',
			'link',
			'insertImage',
			'mediaEmbed',    // 媒体嵌入
			'insertTable',
			'insertTableLayout',
			'highlight',
			'blockQuote',
			'codeBlock',
			'|',
			'alignment',
			'|',
			'bulletedList',
			'numberedList',
			'todoList',
			'outdent',
			'indent'
		],
		shouldNotGroupWhenFull: false
	},
	plugins: [
		Alignment,
		// Autoformat,
		AutoImage,
		AutoLink,
		Autosave,
		BlockQuote,
		Bold,
		Bookmark,
		Code,
		CodeBlock,
		Emoji,
		Essentials,
		FindAndReplace,
		FontBackgroundColor,
		FontColor,
		FontFamily,
		FontSize,
		FullPage,
		Fullscreen,
		GeneralHtmlSupport,
		Heading,
		Highlight,
		HorizontalLine,
		HtmlComment,
		HtmlEmbed,
		ImageBlock,
		ImageCaption,
		ImageEditing,
		ImageInline,
		ImageInsert,
		ImageInsertViaUrl,
		ImageResize,
		ImageStyle,
		ImageTextAlternative,
		ImageToolbar,
		ImageUpload,
		ImageUtils,
		Indent,
		IndentBlock,
		Italic,
		Link,
		LinkImage,
		List,
		ListProperties,
		MediaEmbed,
		Mention,
		MentionCustomization,
		PageBreak,
		Paragraph,
		PasteFromMarkdownExperimental,
		PasteFromOffice,
		PlainTableOutput,
		RemoveFormat,
		ShowBlocks,
		SimpleUploadAdapter,
		SourceEditing,
		SpecialCharacters,
		SpecialCharactersArrows,
		SpecialCharactersCurrency,
		SpecialCharactersEssentials,
		SpecialCharactersLatin,
		SpecialCharactersMathematical,
		SpecialCharactersText,
		Strikethrough,
		Style,
		Subscript,
		Superscript,
		Table,
		TableCaption,
		TableCellProperties,
		TableColumnResize,
		TableLayout,
		TableProperties,
		TableToolbar,
		TextPartLanguage,
		TextTransformation,
		// Title,
		TodoList,
		Underline,
		WordCount
	],
	fontFamily: {
		supportAllValues: true
	},
	fontSize: {
		options: [10, 12, 14, 'default', 18, 20, 22],
		supportAllValues: true
	},
	fullscreen: {
		onEnterCallback: container =>
			container.classList.add(
				'editor-container',
				'editor-container_classic-editor',
				'editor-container_include-style',
				'editor-container_include-word-count',
				'editor-container_include-fullscreen',
				'main-container'
			)
	},
	heading: {
		options: [
			{
				model: 'paragraph',
				title: 'Paragraph',
				class: 'ck-heading_paragraph'
			},
			{
				model: 'heading1',
				view: 'h1',
				title: 'Heading 1',
				class: 'ck-heading_heading1'
			},
			{
				model: 'heading2',
				view: 'h2',
				title: 'Heading 2',
				class: 'ck-heading_heading2'
			},
			{
				model: 'heading3',
				view: 'h3',
				title: 'Heading 3',
				class: 'ck-heading_heading3'
			},
			{
				model: 'heading4',
				view: 'h4',
				title: 'Heading 4',
				class: 'ck-heading_heading4'
			},
			{
				model: 'heading5',
				view: 'h5',
				title: 'Heading 5',
				class: 'ck-heading_heading5'
			},
			{
				model: 'heading6',
				view: 'h6',
				title: 'Heading 6',
				class: 'ck-heading_heading6'
			}
		]
	},
	htmlSupport: {
		allow: [
			{
				name: 'a',
				classes: ['mention'],
				attributes: ['data-mention', 'data-user-id', 'href']
			},
			{
				name: /.*/,
				attributes: true,
				classes: true,
				styles: true
			}
		]
	},
	image: {
		toolbar: [
			'toggleImageCaption',
			'imageTextAlternative',
			'|',
			'imageStyle:inline',
			'imageStyle:block',
			'imageStyle:wrapText',
			'imageStyle:breakText',
			'imageStyle:side',
			'|',
			'resizeImage'
		],
		upload: {
            types: ['jpeg', 'jpg', 'png', 'gif', 'webp']
        }
	},
	// 添加简单上传适配器配置
    simpleUpload: {
        // 上传URL，指向你的ThinkPHP控制器
        uploadUrl: '/admin/editorImage',
        // 允许的文件类型
        allowedTypes: ['jpeg', 'jpg', 'png', 'gif', 'webp', 'mp4', 'mov', 'avi'],
        // 上传时发送的额外头信息（如CSRF令牌）
        // headers: {
        //     'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
        // }
    },
	mediaEmbed: {
        previewsInData: true,
        // 你可以添加更多视频网站的支持
        providers: [
            {
                name: 'myCustomVideo',
                url: /^.*$/,
                html: match => {
                    // 检查是否是本地视频
                    if (match[0].match(/\.(mp4|mov|avi)$/i)) {
                        return (
                            '<video controls>' +
                            `<source src="${match[0]}" type="video/mp4">` +
                            'Your browser does not support the video tag.' +
                            '</video>'
                        );
                    }
                    return null;
                }
            },
            // 支持YouTube等
            {
                name: 'youtube',
                url: /^(?:https?:\/\/)?(?:www\.)?(?:youtube\.com|youtu\.be)\/watch\?v=([^&\s]+)/,
                html: match => {
                    const id = match[1];
                    return (
                        '<div style="position: relative; padding-bottom: 56.25%; height: 0; overflow: hidden;">' +
                        `<iframe src="https://www.youtube.com/embed/${id}" ` +
                        'style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; border:0;" ' +
                        'allowfullscreen></iframe>' +
                        '</div>'
                    );
                }
            }
        ]
    },
	placeholder: ' ',
	language: 'zh-cn',
	licenseKey: LICENSE_KEY,
	link: {
		addTargetToExternalLinks: true,
		defaultProtocol: 'https://',
		decorators: {
			toggleDownloadable: {
				mode: 'manual',
				label: 'Downloadable',
				attributes: {
					download: 'file'
				}
			}
		}
	},
	list: {
		properties: {
			styles: true,
			startIndex: true,
			reversed: true
		}
	},
	mention: {
		dropdownLimit: 10,  //限制提及用户列表显示数
		feeds: [
			{
				//动态获取用户列表
				marker: '@',
				feed: fetchMentionUsers,
				itemRenderer: (item) => {
					const element = document.createElement('span');
					element.textContent = item.name;
					return element;
				}
			}
		]
	},
	menuBar: {
		isVisible: false  // 禁用菜单栏
	},
	style: {
		definitions: [
			{
				name: 'Article category',
				element: 'h3',
				classes: ['category']
			},
			{
				name: 'Title',
				element: 'h2',
				classes: ['document-title']
			},
			{
				name: 'Subtitle',
				element: 'h3',
				classes: ['document-subtitle']
			},
			{
				name: 'Info box',
				element: 'p',
				classes: ['info-box']
			},
			{
				name: 'CTA Link Primary',
				element: 'a',
				classes: ['button', 'button--green']
			},
			{
				name: 'CTA Link Secondary',
				element: 'a',
				classes: ['button', 'button--black']
			},
			{
				name: 'Marker',
				element: 'span',
				classes: ['marker']
			},
			{
				name: 'Spoiler',
				element: 'span',
				classes: ['spoiler']
			}
		]
	},
	table: {
		contentToolbar: ['tableColumn', 'tableRow', 'mergeTableCells', 'tableProperties', 'tableCellProperties']
	}
};



//自定义mention渲染
function MentionCustomization( editor ) {
	// 上转换：从HTML到模型
    editor.conversion.for('upcast').add(dispatcher => {
        dispatcher.on('element:a', (evt, data, conversionApi) => {
            // 1. 安全检查所有必要对象
            if (!data || !data.viewItem || !conversionApi || !conversionApi.writer) {
                return;
            }

            const viewItem = data.viewItem;

            // 2. 确认是mention元素
            if (!viewItem.hasClass || !viewItem.hasClass('mention')) {
                return;
            }

            // 3. 确保有modelRange
            if (!data.modelRange) {
                console.warn('Missing modelRange in mention conversion');
                return;
            }

            // 4. 安全获取属性
            const mentionId = viewItem.getAttribute('data-mention') || '';
            const href = viewItem.getAttribute('href') || '';
            const userId = viewItem.getAttribute('data-user-id') || '';

            // 5. 获取mention插件
            const mentionPlugin = editor.plugins.get('Mention');
            if (!mentionPlugin) {
                console.error('Mention plugin not found');
                return;
            }

            // 6. 创建mention属性
            const mentionAttribute = mentionPlugin.toMentionAttribute({
                id: mentionId,
                userId: userId,
                link: href
            });

            if (!mentionAttribute) {
                console.error('Failed to create mention attribute');
                return;
            }

            // 7. 检查是否可消费
            if (conversionApi.consumable.test(viewItem, { name: true, classes: 'mention' })) {
                conversionApi.consumable.consume(viewItem, { name: true, classes: 'mention' });

                // 8. 安全设置属性
                try {
                    conversionApi.writer.setAttribute('mention', mentionAttribute, data.modelRange);
                } catch (e) {
                    console.error('Failed to set mention attribute:', e);
                }
            }
        }, { priority: 'high' });
    });

    // 下转换：从模型到HTML
    editor.conversion.for('downcast').attributeToElement({
        model: 'mention',
        view: (modelAttributeValue, { writer }) => {
            if (!modelAttributeValue || !writer) return null;

            // 安全构建属性对象
            const attributes = {
                class: 'mention',
                'data-mention': modelAttributeValue.id || '',
                'href': modelAttributeValue.link || '#'
            };

            if (modelAttributeValue.userId) {
                attributes['data-user-id'] = modelAttributeValue.userId;
            }

            return writer.createAttributeElement('a', attributes, {
                priority: 20,
                id: modelAttributeValue.uid
            });
        },
        converterPriority: 'high'
    });
}

// 从 ThinkPHP8 后端获取 Mention 用户
async function fetchMentionUsers(queryText) {
  try {
    const response = await fetch(`/admin/Basic/searchUsers?q=${encodeURIComponent(queryText)}`);
    const users = await response.json();

    return users.map(user => ({
      id: `@${user.name}`,
      userId: user.id,
      name: user.name,
      link: `/user/profile/${user.id}`
    }));
  } catch (error) {
    console.error('获取 Mention 用户失败:', error);
    return [];
  }
}

class CKEditorManager {
    constructor() {
        this.editors = [];
    }

    async initAll(selector) {
        const elements = document.querySelectorAll(selector);

        for (const element of elements) {
            try {
                const editor = await ClassicEditor.create(element, editorConfig);

                // 更可靠的方式查找字数统计容器
                const container = element.closest('.editor-container');
                const wordCountContainer = container ?
                    container.querySelector('.editor_container__word-count') :
                    null;

                if (wordCountContainer) {
                    const wordCount = editor.plugins.get('WordCount');
                    wordCountContainer.appendChild(wordCount.wordCountContainer);
                }

                this.editors.push(editor);
            } catch (error) {
                console.error('Error initializing CKEditor:', error);
            }
        }
    }

    destroyAll() {
        this.editors.forEach(editor => {
            editor.destroy().catch(error => {
                console.error('Error destroying CKEditor:', error);
            });
        });
        this.editors = [];
    }
}


// 全局可用
window.CKEditorManager = CKEditorManager;