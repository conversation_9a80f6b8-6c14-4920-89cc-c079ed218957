body { height:100%; background-color: #f1f1f1; }
html { height:100%; }
.login_bg { background:url(../images/dl_bg.jpg) no-repeat; height: 100%; position: relative; background-size: cover;}
.login_con {
  width: 390px;
  height: 440px;
   border:1px solid #fff;
    background: #fff; border-radius: 10px 10px 10px 10px;
    position: absolute;
    top:50%;
    left:50%;
    margin-left: -195px;
    margin-top: -220px;
    box-shadow: 0 0 10px -2px #ddd;
  }
.login_con h1 { width: 168px; height: 100px; margin: 30px auto 0; }
.login_con img { display: block; width: 168px; }
.names { width: 267px; height:45px; border:1px solid #d4d2d2; border-radius: 5px 5px 5px 5px; color:#d4d2d2;
  padding-left: 15px; font-size: 15px; margin-left: 15%; margin-top: 25px; outline:none }
.yzheng { width: 139px; height:45px; border:1px solid #d4d2d2; border-radius: 5px 5px 5px 5px; color:#d4d2d2;
  padding-left: 15px; font-size: 15px; margin-left: 15%; margin-top: 25px; display: inline-block; outline:none }
img.yz_bg { display: inline-block; width: 120px; height: 47px; margin-top: 25px; margin-left: 1% }
.login { width: 278px; height:45px; border:1px solid #009EE0; border-radius: 5px 5px 5px 5px;
 color:#fff; padding-left: 4px; font-size: 15px; margin-left: 15%; margin-top: 30px; outline:none; background: #009EE0; cursor: pointer; }
.pows { font-size: 14px; color:#6f9188; text-align: center; position: absolute; bottom: 10px; width:100%; }
.backstage { width:100%; height: 60px; border:1px solid #262728; background: #262728; border-radius:5px 5px 0 0; margin: 0 auto; }
.webs { width: 321px; height: 60px; background:url(../images/logo2.png) 0 0 no-repeat; margin:6px 0 0 30px; padding-left:150px; line-height:45px; color:#fff; font-size:20px; font-weight:normal }
.webs_r { width:400px; height:30px; background:#fff; border-radius:15px 15px 15px 15px; background:rgba(255, 255, 255, 0.6)!important; filter:Alpha(opacity=60); margin:10px 10px 0 0; }
.webs_con { position:relative }
.webs_r ul { line-height: 20px; color:#999; height:20px; padding:5px 4px 3px 10px; }
.webs_r li { color:#fff; background: url(../images/icon.png) no-repeat scroll -55px -32px; display:inline; padding-left: 1px; margin-left: 6px; float: left; }
.webs_r li a { color:#fff; line-height:16px; height:16px; border:dotted 1 #000; padding-left: 3px; line-height:20px; }
.webs_r li a span { padding-right: 3px; }
.webs_r li.adminid { background: url(../images/icon.png) no-repeat scroll 0px -32px; width:14px; height:20px; margin-top:2px; overflow: hidden; }
.webs_r li.adminid span { display:inline-block; padding-left:15px; }
.menus { width:100%; margin: 0 auto; height:auto; background:#f1f1f1 }
.menus_l { width:100%; height:100%; background:#313541; float: left; overflow: hidden; }

/* .menus_l ul li{overflow: hidden;height: 64px;} */
.menus_l ul li.cur .list_ca { background: #009EE0; }
.menus_l ul li.cur .list_ca a { color: #fff; font-size: 16px; padding-left: 10px}
.menus_l ul li.active .list_back { background:rgba(255, 255, 255, 0.1); background:#fff 1; filter:alpha(opacity=10); }
.menus_l ul li.active .list_back a { color: #009EE0; }
.menus_l .menu-child { display: none; padding:12px 0 12px 0; }
.menus_l ul li.cur div em { display: none; }
.menus_l ul li.active div em { background: url(../images/tops.png) no-repeat; }
.menus_l ul li .list_ca, .menus_l ul li .list_cb, .menus_l ul li .list_cc, .menus_l ul li .list_cd, .menus_l ul li .list_ce, .menus_l ul li .list_cf, .menus_l ul li .list_cg, .menus_l ul li .list_ch { width: 205px; /*padding-left: 12px;*/ color:#fff; line-height: 54px; }
.list_ca a, .list_cb a, .list_cc a, .list_cd a, .list_ce a, .list_cf a, .list_cg a, .list_ch a { color:#707783; font-size: 16px; display: inline-block; /*padding-left: 8px;*/ position: relative; vertical-align: middle;}
.list_cg.list_back a{
	display: block;
	width: 100%;
}
.list_ca em, .list_cb em, .list_cc em, .list_cd em, .list_ce em, .list_cf em, .list_cg em, .list_ch em {
  display: inline-block; background: url(../images/icon.png) 0 -186px no-repeat; width: 9px; height: 9px;
  position: absolute;
  right: 30px;
  top: 18px;
}
.list_ca i { display: inline-block; background: url(../images/icon.png) 0 -199px no-repeat; width: 19px; height: 16px; vertical-align: middle; margin-left: 11px; position: relative; }
.xitong i{ background: url(../images/xitong.png) no-repeat !important;}
.list_cb i { display: inline-block; background: url(../images/icon.png) 0 -56px no-repeat; width: 19px; height: 16px; vertical-align: middle; margin-left: 11px }
.list_cc i { display: inline-block; background: url(../images/icon.png) 0 -76px no-repeat; width: 16px; height: 16px; vertical-align: middle; margin-left: 11px }
.list_cd i { display: inline-block; background: url(../images/icon.png) 0 -96px no-repeat; width: 20px; height: 18px; vertical-align: middle; margin-left: 11px }
.list_ce i { display: inline-block; background: url(../images/icon.png) 0 -115px no-repeat; width: 20px; height: 17px; vertical-align: middle; margin-left: 13px }
.list_cf i { display: inline-block; background: url(../images/icon.png) 0 -132px no-repeat; width: 20px; height: 15px; vertical-align: middle; margin-left: 14px }
/*.list_cg i { display: inline-block; background: url(../images/jiahao.png); width: 20px; height: 17px; vertical-align: middle; margin-left: 14px }*/
.list_ch i { display: inline-block; background: url(../images/icon.png) 0 -168px no-repeat; width: 20px; height: 16px; vertical-align: middle; margin-left: 14px }
/*.menus_l ul li.change .list_cb i { background: url(../images/icon.png) -1px -216px no-repeat; }
.menus_l ul li.change .list_cc i { background: url(../images/icon.png) -1px -240px no-repeat; }
.menus_l ul li.change .list_cd i { background: url(../images/icon.png) -1px -264px no-repeat; }
.menus_l ul li.change .list_ce i { background: url(../images/icon.png) -1px -288px no-repeat; }
.menus_l ul li.change .list_cf i { background: url(../images/icon.png) -1px -312px no-repeat; }
.menus_l ul li.change .list_cg i { background: url(../images/jianhao.png) no-repeat; }
.menus_l ul li.change .list_ch i { background: url(../images/icon.png) -1px -360px no-repeat; }*/
/*.menus_l ul li hr { border:none; border-top:1px solid #141a20; border-bottom:1px solid #1f2730; width: 148px; margin:0 0 0 12px; }*/
.menus_l ul li hr{
	display: none;
}
.menus_l ul li.cur hr { border:none; height:2px; }
.menu-child li { text-align: center; line-height: 35px; background: url(../images/li-icon.png) no-repeat 29px 17px; }
.menu-child li a {
  display:block;
  color: #767b80;
  overflow: hidden;
  width: 100px;
  text-align: left;
  margin-left: 45px;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.menus_r { width:100%; height: 100%; overflow-y:scroll; overflow-x:hidden; float: right; }
.maps { width: 100%; height: 47px; background: #fff; border:1px solid #fff; border-right:none; margin-top: 11px; overflow: hidden; margin-left: 10px }
.maps i { display: inline-block; float: left; background: url(../images/icon.png) -24px -57px no-repeat; width: 15px; height: 18px; margin: 15px 0 0 10px; }
.current { font-size: 16px; color:#5a5959; display: inline-block; margin-top: 12px; margin-left: 5px }
.current a { font-size: 14px; color:#5a5959; display: inline-block; }
.current a:hover { color:#009EE0; }
.maps_r { width: 245px; height: 25px; border:1px solid #dedddd; border-radius:5px 5px 5px 5px; float: right; margin: 10px 40px 0 0; background: url(../images/icon_05.png) 2px 6px no-repeat; }
.maps_r input.sou_l { outline: none; -webkit-appearance:none; border: 0 none; height: 25px; margin-left: 27px; float: left }
.maps_r input.sou_r { width: 49px; height: 25px; background: #dedddd; color:#959595; outline: none; -webkit-appearance:none; border: 0 none; color:#959595; cursor: pointer; }
.multi { width: 100%; background: #fff; border:1px solid #fff; margin-top: 11px; position: relative; }
.column { width: 100%; border-bottom:1px solid #ebebeb; padding:0px 0 15px 10px }
.gl { float:left; font-size: 14px; color:#52565a; line-height:50px; width: 75px; }
.my { height: auto; display: inline-block; line-height: 27px; margin-top:10px; }
.my .about { font-size: 14px; margin-right: 27px; display: inline-block; }
.my .about input { vertical-align: middle; outline: none; -webkit-appearance:none; background: url(../images/icon-01.png) no-repeat; width: 13px; height: 13px; }
.my .about input:checked { background: url(../images/icon_03.png) no-repeat; }
.additional { width: 1500px; height: 52px; }
.additional_l { height: auto; float: left; }
.gl_f { float:left; font-size: 14px; color:#52565a; line-height:50px; width: 75px; margin-left: 11px; }
.c_ture { width: 126px; height: 29px; position: absolute; bottom:10px; right:148px; }
a.c_ture_l { width: 60px; height: 29px; line-height: 29px; color:#fff; text-align: center; display: inline-block; float: left; background: #f09039 }
a.c_ture_r { width: 60px; height: 29px; line-height: 29px; color:#fff; text-align: center; display: inline-block; float: right; background: #28b265 }
/* 棣栭〉 */
.admin { width:100%; margin-left: 10px; height: auto; overflow: hidden; background: #fff; margin-top: 15px; }
.good { width: 1000px; height: 40px; line-height: 40px; }
.good span.sw { width: 70px; height: auto; display: inline-block; font-size: 15px; font-weight: bold; margin-left: 40px; }
.good b { font-size: 18px; color:#545454; font-weight: bold; display: inline-block; }
.please { display: inline-block; margin-left: 20px; }
.please a { display: inline-block; font-weight: bold; color:#000; }
.details { width: 567px; height: auto; overflow: hidden; background: #fafbfc; margin-top: 15px; margin-left: 26px; margin-bottom: 23px; }
.details_t { border-bottom:1px solid #ebeced; width: 503px; height: 37px; margin: 0 auto; }
.f_time { width: 258px; height: 37px; margin-left: 15px; line-height: 37px; }
.f_time span { width: 98px; height: 37px; text-align: right; display: inline-block; }
.f_times { width: 225px; height: 37px; margin-left: 5px; line-height: 37px; }
.f_times span { width: 177px; height: 37px; text-align: right; display: inline-block; }
.details_r { width: 578px; height: auto; margin-top:15px; margin-left: 30px; ; }
.details_r .details_rcon { width: 578px; height: 152px; margin-left: 30px; ; background: #fafbfc }
.details_r a { width: 62px; height: 91px; display: inline-block; margin-left:26px; margin-top: 30px; }
.details_r dl { width: 62px; height: 91px; text-align: center; }
.details_r dl dt { width: 61px; height: 61px; }
.details_r dl dd { width: 62px; height: 20px; line-height: 20px; margin-top: 5px; }
.details_ys { width: 175px; height: 63px; float: right; margin-top: 30px; }
.weather { width: 114px; height: 63px; float: left }
.weather h4 { font-size: 15px; font-weight: bold; text-align: right }
.weather span { display: inline-block; font-size: 15px; color:#999999; text-align: right; width: 114px; }
.weather_r { background: url(../images/cloudy.png) no-repeat; width: 59px; height: 44px; float: right; margin-top: 23px; }
/* 棣栭〉 end */

/* 浼氬憳绠＄悊 */
.Member { width: 100%; height: 65px; overflow: hidden; background: #fff; padding-left: 16px; margin-left: 10px }
.member_l { width: 660px; height: 30px; float: left; margin: 15px 0 0 0; }
.member_l label { height: 27px; line-height: 25px; text-align: right; display: inline-block; vertical-align: top; }
.member_l span { display: inline-block; margin-left: 10px; color:#bfbfbf; line-height: 25px }
a.selecs { width: 51px; height: 30px; background: #00b7ed; border:1px solid #00b7ed; font-size: 14px; color:#fff; text-align: center; display: inline-block; line-height: 30px; margin:0 10px 0 0; }
a.delete_s { width: 51px; height: 30px; background: #ea5a98; border:1px solid #ea5a98; font-size: 14px; color:#fff; text-align: center; display: inline-block; line-height: 30px; margin:0 10px 0 0; }
a.append_j { width: 75px; height: 30px; background: #1ac7c3; border:1px solid #1ac7c3; font-size: 14px; color:#fff; text-align: center; display: inline-block; line-height: 30px; margin:0 10px 0 0; }
.careful { display: inline-block; font-size: 12px; }
.paixu { /* width: 500px; */ float: right; margin: 15px 0 0 0; padding-right: 15px; }
.paixu label { display: inline-block; float: left; padding-top: 4px; }
.register { width: 90px; height: 30px; line-height: 30px; border:1px solid #e5e5e5; float: left; background: url(../images/select.png) 71px 11px #f6f6f6 no-repeat; outline: none; -webkit-appearance: none; margin-left: 15px; color:#bbb; }
.text_titles { width: 110px; height: 30px; outline: none; border:1px solid #e5e5e5; color:#bbb; line-height: 30px; margin-left: 15px; padding-left: 5px; background: #f6f6f6 }
.cast { width: 60px; height:30px; line-height: 30px; text-align: center; color:#fff; background: #f09039; margin-left: 10px; margin-right: 15px; cursor: pointer; outline: none }
.mid { width: 100%; height: auto; background:  #fff; overflow: hidden; margin-left: 10px }
.mid_con { width:98%; height: auto; overflow: hidden; margin-left: 15px; }
.mid_01 {
  width: 1177px;
  height: 48px;
  line-height: 48px;
  background: #fdfdfd;
  border:1px solid #e4e3e3;
  border-left:none;
  font-size: 16px;
  color: #363636;
}
.mid_01 td { text-align: center; }
.mid_01 .mid_one { width: 5%; }
.mid_t { width: 10%; }
.mid_th { width: 10%;}
.mid_thr { width:30%;}
.mid_f { width: 10%; }
.mid_s { width: 10%; }
.mid_02 { height: 104px; }
.mid_02 td { text-align: center; border-bottom:1px solid #e4e3e3; position: relative; /*padding:15px 0;*/ }
.mid_02 td input.mids_o { -webkit-appearance:none; background: url(../images/icon-01.png) no-repeat; width: 13px; height: 13px; outline: none }
.mid_02 td input.mids_o:checked { background: url(../images/icon_03.png) no-repeat; }
.mid_01 td input { -webkit-appearance:none; background: url(../images/icon-01.png) no-repeat; width: 13px; height: 13px; outline: none }
.mid_01 td input:checked { background: url(../images/icon_03.png) no-repeat; }
.mid_02 td input { -webkit-appearance:none; background: url(../images/icon-01.png) no-repeat; width: 13px; height: 13px; outline: none }
.mid_02 td input:checked { background: url(../images/icon_03.png) no-repeat; }
.fit { display: inline-block; margin:0 9px 5px 0; }
.fit input { -webkit-appearance:none; background: url(../images/icon-01.png) no-repeat; width: 13px; height: 13px; outline: none }
.fit input:checked { background: url(../images/icon_03.png) no-repeat; width: 13px; height: 13px }
.power-con { margin: 0 15px 0 15px; }
.reveal { width: 270px; height: 35px; line-height: 35px; margin: 20px auto 20px; }
.reveal select { outline: none }
i.top_fz { position: absolute; bottom:20px; right:30px; display: inline-block; font-style: normal; width: 13px; height: 13px; background:url(../images/select.png) no-repeat; }
i.top_xz { transform:rotate(180deg); -ms-transform:rotate(180deg); 	/* IE 9 */ -moz-transform:rotate(180deg); 	/* Firefox */ -webkit-transform:rotate(180deg); /* Safari 鍜� Chrome */ -o-transform:rotate(180deg); 	/* Opera */ }
.powers_hide { padding-left: 15px; }
/* 浼氬憳绠＄悊 end */

/* 鏂囩珷 */
.clas_box { margin-left: 10px; height:auto; overflow: hidden; background: #fff; margin-top: 15px;position: relative; }
.class_con { width:1500px; margin-bottom: 15px; }
.class_con label { width: 130px; height: 27px; display: block; vertical-align: middle; margin-bottom: 10px}
.class_con .l_xiang {
  width: 550px;
  outline: none;
   -webkit-appearance:none; background: url(../images/icon_04.png) 523px 8px no-repeat;
   border: 1px solid #d8d8d8;
   padding: 10px;
   border-radius: 4px;
 }
.class_con input { width: 528px; height: 25px; outline: none; border: 1px solid #d8d8d8; padding: 10px; color:#333; background: #fff; font: 14px/1.5 'Microsoft YaHei', '微软雅黑', '\5FAE\8F6F\96C5\9ED1'; border-radius: 4px;box-sizing: content-box;}


.class_con span { display: inline-block; margin-left: 10px; color:#bfbfbf; line-height: 25px }

.bor_cen{
  border-collapse:collapse;
}
.bor_cen .mid_01{
  border-left:3px solid #e4e3e3
}
.bor_cen .mid_02{
  border-left:3px solid #cfbba9
}


a.star { width: 54px; height: 25px; display: inline-block; margin-left: 6x }
a.star input { outline: none; -webkit-appearance:none; background: url(../images/no.png) no-repeat; vertical-align: middle; float: left; margin-top: 12px }
a.star i { font-style: normal; background: url(../images/star_01.png) no-repeat; width: 11px; height: 12px; float: left; margin-left: 5px; margin-top: 12px }
a.star:hover i { background: url(../images/star_02.png) no-repeat; }
a.star input:checked { background: url(../images/yes.png) no-repeat; }
.scipone { background: url(../images/sc_icon.png) no-repeat; text-align: center; color:#a19f9f; }
.class_con textarea {
  width:528px;
  min-height: 50px;
  /* resize:none; */
  border: 1px solid #d8d8d8;
  outline: none;
  color: #333;
  border-radius: 4px;
  padding: 10px; font: 14px/1.5 'Microsoft YaHei', '微软雅黑', '\5FAE\8F6F\96C5\9ED1';
}

.form_con { height: auto; margin-top: 15px; overflow: hidden; background: #fff; margin-left: 10px; padding: 25px; }
.form_xuan { width: 485px; height: 30px; margin-top: 15px; margin-left: 15px; }
.form_xuan a { margin-right: 5px; height: 30px; display: inline-block; height: 30px; line-height: 30px; text-align: center; color: #fff; }
a.form_qx { width: 51px; background: #ea5a98 }
a.sc { width: 51px; background: #1ac7c3 }
.tuijian { width: 159px; height: 30px; display: inline-block; background: #00b6ea; color:#fff; margin-right: 5px; }
a.tuijian_l { width: 70px; }
a.tuijian_r { width: 70px; }
a.tjwzhang { width: 70px; background: #9b8975 }
.bor_cen {
  overflow: hidden;
  background: #fff;
  border: 1px solid #dbd9e1;
}
.bor_cen tbody tr { height: 35px; border:1px solid #e4e3e3; }
.bor_cen .bor_bg { background: #f9f9f9 }
.bor_cen tr td {padding: 15px 10px; border-bottom:1px solid #e4e3e3; border-right: 1px solid #e4e3e3;}
.bor_01 { width:5%; border-right:1px solid #e4e3e3; }
.bor_01 input { outline: none; -webkit-appearance:none; background: url(../images/icon-01.png) no-repeat; width: 13px; height:13px; }
.bor_01 input:checked { background: url(../images/icon_03.png) no-repeat; }
.bor_02 { width: 5%; border-right:1px solid #e4e3e3; }
.bor_03 { width: 35%; border-right:1px solid #e4e3e3; }
.bor_04 { width: 10%; border-right:1px solid #e4e3e3; }
.bor_05 { width: 20%; border-right:1px solid #e4e3e3; }
.bor_06 { width: 20%; border-right:1px solid #e4e3e3; }
.bor_02 a { display: inline-block; background: url(../images/icon.png) -24px -136px; width: 11px; height: 12px; margin: 0 auto; }
.bor_03 a { display: inline-block; font-size: 14px; color:#545454; padding-left: 15px }
.bor_03 a:hover { color:#1093b9; }
.bor_03 a.bor_ls { color:#1093b9; }
.bor_04 i, .bor_05 i { font-style: normal; color:#545454; cursor: pointer; }
.bor_06 a { display: inline-block; color:#545454; }
/*.interpret { height: 35px; line-height: 35px; background: #f9f9f9; border:1px solid #e4e3e3; border-top: none; padding: 10px 0 10px 8px; }*/
.interpret_l { width: 185px; height: 30px; line-height: 30px; background: #fff; margin: 0px 0 10px 10px; float: left }
a.inter_t { width: 50px; height: 30px; line-height: 30px; border:1px solid #e9eaee; float: left }
a.inter_z { width: 26px; height: 30px; line-height: 30px; border:1px solid #e9eaee; }
.interpret_l a { display: inline-block; font-size: 12px; color:#545454; text-align: center; float: left; background: #fff; }
a.inters { background: #0ea4cc; color:#fff; }
a.inter_z:hover { background: #0ea4cc; color:#fff; }
.interpret_r { width: 200px; height: 30px; line-height: 30px; float: left; margin-left: 20px; }
.powes { display: inline-block; font-size: 14px; color:#545454; float: right; padding: 10px 15px 10px 0; }
/* 鍐呭绠＄悊 end*/

/* 娣诲姞浼氬憳*/
.add_con { width: 100%; height: auto; overflow: hidden; background: #fff; margin:15px 0 0 0; border-bottom:1px solid #ebebeb; }
.add_name { width: 582px; height: 35px; margin: 30px 0 0 40px; overflow: hidden; float: left }
.add_name label { float: left; margin-top: 4px; font-size: 14px; color:#52565a; }
.add_name .inp_key { width: 196px; height: 30px; border:1px solid #ebebeb; background: none; margin-left: 10px; float: left; color:#bbb; padding-left: 4px; font-size: 14px; outline: none; }
.add_name span { float: left; color:#bbb; margin-top: 4px; margin-left: 10px; }
.add_r { width: 294px; height: 35px; float: left; margin: 30px 0 0 40px; }
.add_r label { float: left; margin-top: 4px; font-size: 14px; color:#52565a; }
.add_r select { width: 196px; height: 30px; border:1px solid #ebebeb; line-height: 30px; float: left; margin-left: 10px; outline: none; color: #bbb; -webkit-appearance: none; padding-left: 5px; background: url(../images/select.png) 175px 10px no-repeat; }
.power_content { width: 98%; height: auto; overflow:hidden; margin:15px 0 0 30px }
.power_01 {/* width: 1185px; */ height: auto; padding-bottom: 15px }
.powers_t { /*width: 1170px;*/ height: 32px; border:1px solid #f1f1f1; border-left: none; border-left:3px solid #ed7faf; background: #f9f9f9; line-height: 30px; padding-left: 15px; }
.powers_xz { width: 98%; height: auto; padding-top: 15px; padding-left: 15px; }
.che_box { -webkit-appearance: none; background: url(../images/icon-01.png) no-repeat; width: 13px; height: 13px; outline: none; vertical-align: middle; }
.che_box:checked { -webkit-appearance: none; background: url(../images/icon_03.png) no-repeat; width: 13px; height: 13px; }
.in_xz { display: inline-block;/*  padding:0 45px 5px 0px */ width: 280px }
span.che_title { display: inline-block; padding-left: 10px; vertical-align: middle; }
span.admins_e { display: inline-block; padding-left: 10px; vertical-align: middle; color:#bbb; }
.reset { width: 210px; height: 29px; margin: 0 auto; }
.reset a { display: inline-block; margin: 0 8px 0 0; }
a.reset_01 { width: 57px; height: 27px; border:1px solid #e5e5e5; background: #f6f6f6; color:#535353; line-height: 27px; text-align: center; }
a.reset_02 { width: 57px; height: 27px; border:1px solid #f09039; background: #f09039; color:#fff; line-height: 27px; text-align: center; }
a.reset_03 { width: 57px; height: 27px; border:1px solid #28b265; background: #28b265; color:#fff; line-height: 27px; text-align: center; margin-top: 20px }
.reset_con { width: 100%; height: 80px; background: #fff; margin-bottom: 20px; }
/* 娣诲姞浼氬憳 end*/


/* 妯℃澘绠＄悊 */
.templet { width: 100%; height: auto; overflow: hidden; margin-top: 15px; background: #fff }
.tabbtn { width:98%; height:40px; margin-left: 15px; margin-top: 15px }
.tabbtn li { width: 96px; height: 40px; float: left; text-align: center; line-height: 40px; }
.tabbtn li a { color:#5a5959; }
.tabbtn li.cur { border-bottom: 2px solid #25add1; }
.tabbtn li.cur a { color:#25add1; }
.templet_con { width: 98%; height:auto; overflow: hidden; margin:30px 0 0 15px; border-collapse:collapse; }
.templet_con .news_cens { width: 95%; height: 35px; border:1px solid #e4e3e3; background: #f9f9f9 }
.lanmu { width: 40%; height: 35px; text-align: left; padding-left:90px; }
.tjia { width: 40%; height: 35px; text-align: center; }
.caozuo { width: 20%; height: 35px; text-align: center; }
.templet_con td input { width:13px; height:13px; -webkit-appearance: none; outline: none; background: url(../images/icon-01.png) no-repeat; padding-left: 38px; margin-left: 41px; }
.news_cen { width: 95%; height: 35px; border-bottom: 1px solid #e4e3e3 }
.news_01 { width: 40%; height: 35px; text-align: left; }
.news_02 { width: 40%; height: 35px; text-align: center; color:#bbb; }
.news_02 span { color:#545454; }
.news_03 { width: 20%; height: 35px; text-align: center; }
.edit { width: 120px; height: 35px; margin: 0 auto; line-height: 35px; color:#bbb; }
.edit i { display: inline-block; background: url(../images/icons_qian.png) no-repeat; width: 16px; height: 14px; }
.news_m { /* width: 322px;  */ height: 30px; margin: 25px 0 20px 15px; }
.news_m a { display: inline-block; color:#fff; line-height: 30px; text-align: center; margin: 0 5px 0 0; }
a.a1, a.a2, a.a3 { width: 50px; height: 30px; background: #25add1 }
a.a4 { width: 50px; height: 30px; background: #d34f30 }
a.a5 { width: 76px; height: 30px; background: #5e6368 }
.hide { display:none; }
/* 妯℃澘绠＄悊 end */

/* 缃戠珯鏍忕洰绠＄悊 */
.site { height: 65px; overflow: hidden; background: #fff; margin-top: 15px; padding-left: 16px; }
.site_l { width: 660px; height: 30px; float: left; margin: 15px 0 0 0; }
a.sites { width: 51px; height: 30px; background: #00b7ed; border:1px solid #00b7ed; font-size: 14px; color:#fff; text-align: center; display: inline-block; line-height: 30px; margin:0 10px 0 0; }
a.site_s { width: 51px; height: 30px; background: #ea5a98; border:1px solid #ea5a98; font-size: 14px; color:#fff; text-align: center; display: inline-block; line-height: 30px; margin:0 10px 0 0; }
a.open { display: inline-block; color:#9b8975; }
.site_r { padding-right: 20px; height: 30px; float: right; margin: 15px 0 0 0; }
a.sites_01, a.sites_02 { display: inline-block; width: 98px; height: 30px; color:#fff; line-height: 30px; text-align: center; margin-right: 8px; }
.sites_03 { display: inline-block; width: 250px; height: 30px; color:#fff; background: #55b538; line-height: 30px; text-align: center; }
a.sites_01 { background: #9b8975 }
a.sites_02 { background: #d34f30 }
.sites_03 a { display: inline-block; color:#fff; }
.wed_content { height: auto; overflow: hidden; background: #fff; padding:0 21px 50px 15px; }
.wed_content table { border-collapse: collapse; width: 100%; }
.wed_content table tr { border:1px solid #e4e3e3; height: 46px; text-align: center; }
td.wed_01 { width: 5%; border-right:1px solid #e4e3e3; }
td.wed_02 { width: 5%; border-right:1px solid #e4e3e3; }
td.wed_03 { width: 58%; }
td.wed_04 { width: 35%; }
.wed_03 a { display: inline-block; padding-left: 15px }
.wed_02 input { background: url(../images/icon-01.png) no-repeat; width: 13px; height: 13px; -webkit-appearance: none; outline: none }
.wed_01 a { background: url(../images/jia.png) no-repeat; width: 11px; height: 11px; display: inline-block; }
.wed_02 input:checked { background: url(../images/icon_03.png) no-repeat; }
.wed_r { width: 420px; height: 46px; overflow: hidden; line-height: 46px; margin-left: 150px; color:#e4e3e3; }
.wed_r input { width: 45px; height:30px; border:1px solid #e4e3e3; vertical-align: middle; background: none }
.wed_r a { display: inline-block; }
.news_l { float: right; margin: 25px 0 0 0; width:405px; height: 30px; line-height: 28px; text-align: center; color:#666; }
a.news_p { width: 72px; height: 28px; border:1px solid #e4e3e3; background: #666; display: inline-block; background: #f3f0f0; margin-right: 5px; }
a.news_r { width: 110px; height: 28px; border:1px solid #e4e3e3; background: #666; display: inline-block; background: #f3f0f0; margin-right: 5px; }
/* 缃戠珯鏍忕洰绠＄悊 end */

/* 淇敼鏍忕洰 */
.modified_column { width: 100%; height: auto; overflow: hidden; background: #fff; margin: 15px 0 0 0; padding-bottom: 80px; position: relative; }
.tab_menu { height: 35px; width: 100%; background-color: #fff; padding-bottom: 10px; padding-left: 15px; padding-top: 16px; margin-left: 10px }
.tab_menu li { width:95px; height: 35px; float: left; text-align: center; line-height: 35px; border:1px solid #ccc; }
.tab_menu .selected { height: 35px; cursor: pointer; border-bottom: 2px solid #0da4cc; border-top: none; border-left: none; border-right: none; color:#0da4cc; }
.tab_box { width: 1120px; height: auto; margin: 25px 0 100px 15px; }
.tab_box_O { width: 1120px; height: auto; }
.hide { display:none; }
.tab_cons { width: 1120px; border-bottom:1px solid #ebebeb; }
.tab_cons label { width: 120px; height: 49px; line-height: 49px; color:#545454; text-align: right; display: inline-block; }
.support { width: 600px; display: inline-block; }
.support input.ch_right { outline: none; -webkit-appearance: none; background: url(../images/no.png) no-repeat; width: 14px; height:14px; margin-left: 10px; vertical-align: middle; }
.support input.ch_right:checked { background: url(../images/yes.png) no-repeat; }
.support input.ch_rights { outline: none; -webkit-appearance: none; background: url(../images/no.png) no-repeat; width: 14px; height:14px; margin-left: 20px; vertical-align: middle; }
.support input.ch_rights:checked { background: url(../images/yes.png) no-repeat; }
.classify { width: 227px; height: 25px; line-height: 20px; outline: none; -webkit-appearance:none; color:#bbb; border-top:1px solid #d5d4d4; border-left:1px solid #d5d4d4; border-bottom: 1px solid #ebebeb; border-right: 1px solid #ebebeb; padding-left: 10px; background: url(../images/icon_04.png) 202px 3px no-repeat; }
.tests { width: 227px; height: 25px; line-height: 25px; border-top:1px solid #d5d4d4; border-left:1px solid #d5d4d4; border-bottom: 1px solid #ebebeb; border-right: 1px solid #ebebeb; padding-left: 10px; color:#bbb; outline: none }
.nums { width: 76px; height: 25px; line-height: 25px; border-top:1px solid #d5d4d4; border-left:1px solid #d5d4d4; border-bottom: 1px solid #ebebeb; border-right: 1px solid #ebebeb; padding-left: 10px; color:#bbb; outline: none }
.browse { width: 99px; height: 25px; line-height: 20px; outline: none; -webkit-appearance:none; color:#bbb; border:1px solid #bbb; padding-left: 10px; background: url(../images/icon_04.png) 74px 3px no-repeat; }
span.zhu { display: inline-block; margin-left:15px; }
.catalog { width: 286px; height: 25px; line-height: 25px; padding-left: 10px; color:#bbb; outline: none; border-top:1px solid #d5d4d4; border-left:1px solid #d5d4d4; border-bottom: 1px solid #ebebeb; border-right: 1px solid #ebebeb; }
.options { outline: none; background: url(../images/no.png) no-repeat; width: 14px; height: 14px; vertical-align: middle; -webkit-appearance:none; }
.options:checked { background: url(../images/yes.png) no-repeat; }
.default { width: 227px; height: 25px; line-height: 25px; border-top:1px solid #d5d4d4; border-left:1px solid #d5d4d4; border-bottom: 1px solid #ebebeb; border-right: 1px solid #ebebeb; padding-left: 10px; color:#bbb; outline: none }
.de_con { width: 550px; height:85px; display: inline-block; vertical-align: top; }
.defaults { margin-bottom: 5px; }
.defaults input { outline: none; background: url(../images/no.png) no-repeat; width: 14px; height: 14px; vertical-align: middle; -webkit-appearance:none; }
.defaults input:checked { background: url(../images/yes.png) no-repeat; }
.defaults span { margin-left: 5px; display: inline-block; }
.de_y { width: 155px; height: 30px; /*margin-left: 10px;  margin-top: 25px; */ }
.de_y button,a.de_y_r{
  display: inline-block;
  padding: 5px 15px;
  color:#fff;
  border-radius: 4px;
}
button.de_y_l { background: #009EE0; float: left;border: 1px solid #009EE0; }
button.de_y_l:hover{
  cursor: pointer;
}
a.de_y_r { background: #fff; float: left; margin-left: 15px; border: 1px solid #ccc; color: #999}

.by { position: absolute; right:20px; bottom:5px; color:#515151; }
.class_con_alink_01 { margin-left: 10px }
/* 淇敼鏍忕洰 end */

  .page { margin-left: 10px; }
.pre a {
  display: inline-block;
  font-size: 12px;
  color: #545454;
  text-align: center;
  float: left;
  background: #fff;
  width: 50px;
  height: 30px;
  line-height: 30px;
  border: 1px solid #e9eaee;
}

.page-one a,.page-one .current{
  margin:0;
  display: inline-block;
  font-size: 12px;
  border-radius: 4px;
  text-align: center;
  float: left;
  width: 30px;
  height: 30px;
  line-height: 30px;
}
.page-one a {
  color: #545454;
   background: #fff;
  border: 1px solid #e9eaee;
  margin-right: 10px;
}

.page-one .current {
  border: 1px solid #e9eaee;
  background: #009EE0;
  color: #fff;
  margin-right: 10px;
}
.totle { margin-left: 10px; }

/*liuqingyan add begin*/
.showErrorMessage{ color:#F00;}

#dialogBg{width:100%;height:100%;background-color:rgba(0,0,0,.5);filter:alpha(opacity=60);position:fixed;top:0;left:0;z-index:9999;display:none;}
/*#dialogBg{width:100%;height:100%;background-color:#f1f1f1 ;position:fixed;top:0;left:0;z-index:9999;display:none;}*/
#dialog{width:500px;height:400px;margin:20px auto;display:block;background-color:#CCC;z-index:10000; overflow-y:auto;/*  padding:10px; */ opacity:1;filter:alpha(opacity=100);}

.class_con_inputfile{ outline:none; -webkit-appearance:none; background-color:#fff; border:none; color:#bfbfbf; width:60px; height:24px;}
.class_con_alink_01{ display:inline-block; color:#F60;}

.div_height_15{ height:15px; width:100%;}
/*liuqingyan add end*/


.class_con{
  position: relative;
}
.class_con input.img_new_now{
  width: 300px;
  height: 25px;
  border: 0;
  position: absolute;
  margin-left: 5px;
}
.class_con span.News{
  position: absolute;
  width: 80px;
  text-align: center;
  height: 25px;
  z-index: 9;
  background: #00b6ea;
  color: #fff;
  border-radius: 5px;
  left: 0px;
  margin-left: 0;
}

/*mozi add begin*/
.menus_l  ul li,.menus_l  ul li a{
	-moz-transition: all 0.5s ease-in;
    -webkit-transition: all 0.5s ease-in;
    -o-transition: all 0.5s ease-in;
    transition: all 0.5s ease-in;
}

.menus_l  ul li.tab{
  padding-left: 10px;
}

.menus_l ul li.tab:hover,.menus_l ul li.tab.active{
	background: #272a34;
	color: #f6f5fa;
}
.menus_l ul li.tab.active a{
  color: #fff !important;
}

.menus_l ul li.tab:hover a{
	color: #fff;
}

.menus_l ul li.tab .list_cg.list_back{
	position: relative;
}

.menus_l ul li.tab a{
	padding-left: 45px;
}
.icon-new,.img-7-13{
	position: absolute;
	display: block;
	left: 10px;
	top: 50%;
	margin-top: -10px;
	height:20px;
	width:20px;
}

.icon-new{
  background:url(../images/icon-new.png) no-repeat;
}

.img-7-13{
  background:url(../images/img-7-13.png)  no-repeat;
}

.icon-4{background-position:0 0;}
.icon-5{background-position:0 -20px;}
.icon-6{background-position:0 -40px;}
.icon-3{background-position:0 -60px;}
.icon-0{background-position:0 -80px;}
.icon-1{background-position:0 -100px;}
.icon-2{background-position:0 -120px;}
.icon-11{background-position:0 -140px;}
.icon-12{background-position:0 -160px;}
.icon-13{background-position:0 -180px;}
.icon-10{background-position:0 -200px;}
.icon-7{background-position:0 -220px;}
.icon-8{background-position:0 -240px;}
.icon-9{background-position:0 -260px;}

.type{background-position:0 0;}
.why{background-position:0 -80px;}
.history{background-position:0 -120px;}
.position{background-position:0 -140px;}
.subscribe{background-position:0 -160px;}

.menus_l ul li.tab:hover i.img-7-13.subscribe,
.menus_l ul li.tab.active i.img-7-13.subscribe{
  background-position:0 -20px;
}

.menus_l ul li.tab:hover i.img-7-13.type,
.menus_l ul li.tab.active i.img-7-13.type{
  background-position:0 -40px;
}

.menus_l ul li.tab:hover i.img-7-13.why,
.menus_l ul li.tab.active i.img-7-13.why{
  background-position:0 -60px;
}

.menus_l ul li.tab:hover i.img-7-13.history,
.menus_l ul li.tab.active i.img-7-13.history{
  background-position:0 -100px;
}

.menus_l ul li.tab:hover i.img-7-13.position,
.menus_l ul li.tab.active i.img-7-13.position{
  background-position:0 -181px;
}

.menus_l ul li.tab:hover i.icon-new.icon-0,
.menus_l ul li.tab.active i.icon-new.icon-0{
	background-position:0 -360px;
}
.menus_l ul li.tab:hover i.icon-new.icon-1,
.menus_l ul li.tab.active i.icon-new.icon-1{
	background-position:0 -380px;
}
.menus_l ul li.tab:hover i.icon-new.icon-2,
.menus_l ul li.tab.active i.icon-new.icon-2{
	background-position:0 -400px;
}
.menus_l ul li.tab:hover i.icon-new.icon-3,
.menus_l ul li.tab.active i.icon-new.icon-3{
	background-position:0 -340px;
}
.menus_l ul li.tab:hover i.icon-new.icon-4,
.menus_l ul li.tab.active i.icon-new.icon-4{
	background-position:0 -280px;
}
.menus_l ul li.tab:hover i.icon-new.icon-5,
.menus_l ul li.tab.active i.icon-new.icon-5{
	background-position:0 -300px;
}
.menus_l ul li.tab:hover i.icon-new.icon-6,
.menus_l ul li.tab.active i.icon-new.icon-6{
	background-position:0 -320px;
}
.menus_l ul li.tab:hover i.icon-new.icon-7,
.menus_l ul li.tab.active i.icon-new.icon-7{
	background-position:0 -500px;
}
.menus_l ul li.tab:hover i.icon-new.icon-8,
.menus_l ul li.tab.active i.icon-new.icon-8{
	background-position:0 -520px;
}
.menus_l ul li.tab:hover i.icon-new.icon-9,
.menus_l ul li.tab.active i.icon-new.icon-9{
	background-position:0 -540px;
}
.menus_l ul li.tab:hover i.icon-new.icon-10,
.menus_l ul li.tab.active i.icon-new.icon-10{
	background-position:0 -480px;
}
.menus_l ul li.tab:hover i.icon-new.icon-11,
.menus_l ul li.tab.active i.icon-new.icon-11{
	background-position:0 -420px;
}
.menus_l ul li.tab:hover i.icon-new.icon-12,
.menus_l ul li.tab.active i.icon-new.icon-12{
	background-position:0 -440px;
}
.menus_l ul li.tab:hover i.icon-new.icon-13,
.menus_l ul li.tab.active i.icon-new.icon-13{
	background-position:0 -460px;
}
.menus_l ul li.tab .menu-child li a{
  padding-left: 0
}

/*首页样式*/
#edit-div {
  flex-direction: column;
  margin-left: 133px;
  width:960px;
  vertical-align: top;
  margin-top: -26px;
}

.clas_box{
  padding: 25px;
}

.mo-tabList{
  margin-bottom: 30px;
}

.mo-tabList a{
  margin-bottom: 10px
}

a.a-button{
  display: inline-block;
  margin-right: 15px;
  font-size: 14px;
  color: #fff;
  padding: 7px 20px;
  background: #313541;
  text-align: center;
  border-radius: 4px;
}
.a-button:hover{
  background: #009EE0;
  color: #fff;
  cursor: pointer;
}

.mo-tab{
  overflow: hidden;
  margin-bottom: 20px;
}

.box-conter{
  margin-bottom:15px;
  padding:20px;
  border:1px solid #ccc;
}

.mo-tab li{
  float: left;
  padding: 7px 20px;
  margin-right: 15px;
}

.mo-tab li.active{
  background: #1DB0B8;
  color: #fff;
}

.span-button{
  display: inline-block;
  margin-right: 15px;
  font-size: 14px;
  color: #fff;
  padding: 7px 20px;
  background: #313541;
  text-align: center;
  border-radius: 4px;
}

.span-button:hover{
  background: #1DB0B8;
  color: #fff;
  cursor: pointer;
}

.conter-box1{
  display: none;
  width: 500px;
}

.conter-box1.active{
  display: block;
}

.defaultTab-T {
  background: #fdfdfd;
  border-bottom: 1px solid #f1eff6;
}

.compile,.delete-c,.main-content,.basic,.preview{
  padding: 3px 10px;
  border-radius: 2px;
  -moz-border-radius: 2px;
  -webkit-border-radius: 2px;
  display: inline-block;
  margin: 0 2px 5px 2px;
}

.compile{
  color: #fe990b;
  border: 1px solid #fe990b;
}

.delete-c{
  color: #f23e47;
  border: 1px solid #f23e47;
}

.main-content{
  color: #1DB0B8;
  border: 1px solid #1DB0B8;
}

.basic{
  color: #009EE0;
  border: 1px solid #009EE0;
}

.preview{
  color: #37C6C0;
  border: 1px solid #37C6C0;
}

.compile:hover{
  color: #fe990b;
}

.delete-c:hover{
  color: #f23e47;
}

.basic:hover{
  color: #009EE0;
}

.main-content:hover{
  color: #1DB0B8;
}

.preview:hover{
  color: #37C6C0;
}

span.colse{
  position: absolute;
  background: url(../images/delete.png) no-repeat center;
  width: 20px;
  height: 20px;
  background-size: 20px;
  left: 533px;
  z-index: 99;
  margin-left: 0;
}

.colse:hover,#lunbo2:hover,#lunbo3:hover,span#lunbo,span#lunbo1,span#lunboTab{
  cursor: pointer;
}

span#lunbo3,span#lunbo2,span#lunbo,span#lunbo1,span#lunboTab{
  margin-left: 0;
  background: #1DB0B8;
  color: #fff;
  padding: 2px 10px;
  border-radius: 4px;
  border: 0;
}


.class_con span.add-lunbo-button{
  cursor: pointer;
  margin-left: 20px;
  background: #1DB0B8;
  color: #fff;
  padding: 2px 10px;
  border-radius: 4px;
  border: 0;
}


.label_input{
  margin-bottom: 15px;
}

tbody{
  font-size: 13px;
}

tbody>tr:hover {
    background: #f5f5f5;
}
.class_con.layout_tip_css, .class_con span.pic_size_tip {
	color:red;
}
.current .de_y_r {float: none;}


/*定义滚动条高宽及背景 高宽分别对应横竖滚动条的尺寸*/
::-webkit-scrollbar
{
    width: 5px;
    height: 5px;
    background-color: #F5F5F5;
}

/*定义滚动条轨道 内阴影+圆角*/
::-webkit-scrollbar-track
{
    -webkit-box-shadow: inset 0 0 6px #313541;
    border-radius: 10px;
    background-color: #F5F5F5;
}

/*定义滑块 内阴影+圆角*/
::-webkit-scrollbar-thumb
{
    border-radius: 10px;
    -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,.3);
    background-color: #272a34;
}


/*mozi add end*/

.class_con_ueditor { width:1500px; margin-bottom: 15px; }
.class_con_ueditor{
  position: relative;
}
.class_con_ueditor label { width: 120px; height: 27px; display: block; vertical-align: middle; margin-bottom: 10px}

.class_con.radio input[type="radio"] {
	width:30px;
	height:20px;
	margin:5px 5px 0 0;
}
.class_con.radio img {
	vertical-align: middle;
}

.tabcontent{
  margin: 10px;
}
.tabcontent input{
  width: 30px;
  height: 10px;
  margin: 0 10px;
}
.cursor-point{
  cursor: pointer;
}
.tabcontent .tab-title{
  color: #333;
}
.class_con span.add-lunbo-button.left{
  margin-left:0;
}

a.add-button{
  float: right;
  margin-right: 50px;
  margin-top: 6px;
  background-color: #009EE0;
  display: inline-block;
  font-size: 14px;
  color: #fff;
  padding: 7px 20px;
  text-align: center;
  border-radius: 4px;
}

.interpret {
  height: 35px;
  line-height: 35px;
  background: #f9f9f9;
  border-top: none;
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
  padding: 10px 30px;
  border: 1px solid #e4e3e3;
}

.table-paging-l {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-right: 15px;
}
.table-paging-l li {
  list-style: none;
}
.table-paging-l a, .table-paging-l span, .table-paging-r input, .table-paging-r button {
  width: 30px;
  height: 30px;
  line-height: 30px;
  background: #fff;
  border: 1px solid #e9eaee;
  border-radius: 3px;
  color: #666666;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  cursor: pointer;
  transition: all .25s ease;
  margin-right: 5px;
}
.table-paging-l a:hover, .table-paging-l span:hover, .table-paging-l li.active span {
  border: 1px solid #e9eaee;
  background: #009EE0;
  color: #fff;
}

.table-paging-r {
  display: flex;
  flex-direction: row;
  align-items: center;
}
.table-paging-r input {
  cursor: inherit;
}

.table-paging-r input, .table-paging-r button {
  display: inline-block;
}
.table-paging-r input {
  width: 70px;
  padding: 0 5px;
}
.pagego-input {
  text-align: center;
}


.editor-container {
  width: 900px;
}
.class_con img{
  max-width: 230px;
}



.list-item {
  width: 600px;
  margin: 10px 0;
  border: 1px solid #ccc;
  padding: 10px;
  border-radius: 5px;
}
.list-item input, .list-item textarea{
  margin-bottom: 5px;
}
.list-item button {
  display: block;
  margin-top: 5px;
  background-color: #ff4d4d;
  color: white;
  border: none;
  padding: 5px 10px;
  border-radius: 3px;
  cursor: pointer;
}
.list-item button:hover {
  background-color: #cc0000;
}
.add-item {
  background-color: #4CAF50;
  color: white;
  border: none;
  padding: 2px 8px;
  border-radius: 3px;
  cursor: pointer;
}
.add-item:hover {
  background-color: #45a049;
}

.tab-buttons {
  display: flex;
  border-bottom: 1px solid #ccc;
}

.tab-button {
  padding: 10px 20px;
  cursor: pointer;
  border: none;
  background-color: #f1f1f1;
  margin-right: 5px;
}

.tab-button.active {
  background-color: #ddd;
  font-weight: bold;
}

.tab-content {
  padding: 20px;
  border: 1px solid #ccc;
  border-top: none;
}

.tab-pane {
  display: none;
}

.tab-pane.active {
  display: block;
}

.class_con input[type="checkbox"] {
  width: 20px;
  height: 20px;
}

.list-img img{
  max-height: 100px;
}

/*输入提示*/
.class_con span.input-tips{
  color: #f50000
}

/*必填项*/
.class_con span.must-input{
  color: #ff0101;
  font-size: 20px;
  vertical-align: middle;
}


/*开关切换*/
.class_con label.switch {
  position: relative;
  display: inline-block;
  width: 70px;
  height: 34px;
}

.class_con label.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: .4s;
  border-radius: 34px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 26px;
  width: 26px;
  left: 4px;
  bottom: 4px;
  background-color: white;
  transition: .4s;
  border-radius: 50%;
}

input:checked + .toggle-slider {
  background-color: #2196F3;
}

input:checked + .toggle-slider:before {
  transform: translateX(26px);
}

/*列表全选、单选*/
.multi-select-container {
  display: inline-block;
  vertical-align: middle;
  width: 300px;
}

.user-select-header {
  margin-bottom: 5px;
}

.select-all-btn, .deselect-all-btn {
  padding: 2px 8px;
  margin-right: 5px;
  background-color: #f0f0f0;
  border: 1px solid #ccc;
  border-radius: 3px;
  cursor: pointer;
  font-size: 12px;
}

.select-all-btn:hover, .deselect-all-btn:hover {
  background-color: #e0e0e0;
}

.class_con span.selected-count, .class_con span.selected-count .count {
  font-size: 12px;
  color: #666;
  margin-left: 10px;
}
.class_con .l_xiang.user-multi-select{
  background: inherit;
}
.user-multi-select {
  width: 100%;
  padding: 5px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: #fff;
}

.user-multi-select option {
  padding: 3px 5px;
  margin: 1px 0;
  border-bottom: 1px solid #e9e9e9;
}

.user-multi-select option:hover {
  background-color: #f0f0f0;
}
.user-multi-select option:checked {
  background-color: #4CAF50; /* 选中时的背景色 */
  color: white; /* 选中时的文字颜色 */
  font-weight: bold; /* 加粗 */
  border-bottom-color: transparent; /* 选中时隐藏边框 */
}

/*左侧菜单*/
.menus_l ul li .list_ca, .menus_l ul li .list_cb, .menus_l ul li .list_cc, .menus_l ul li .list_cd, .menus_l ul li .list_ce, .menus_l ul li .list_cf, .menus_l ul li .list_cg, .menus_l ul li .list_ch {
  line-height: 45px;
}
.menu-child li a {
  color: #fff;
}
.menu-child li.active {
  background-color: #393939; /* 或其他你喜欢的样式 */
}

/*多选排序*/
.handle {
    cursor: move;
    margin-right: 10px;
}
.list-group {
    width: 550px;
}
.list-group .list-group-item .btn{
    margin-left: 10px;
}
.list-group-item {
    user-select: none;
}

/*订单*/
.order-info .class_con{
  width: 570px;
}
.order-info .class_con label{
  width: 136px;
  text-align: right;
  margin-bottom: 0;
}
.order-info .class_con span{
  margin-left: 0;
}
.order-info .class_con .l_xiang{
  width: 398px;
  height: 25px;
  box-sizing: content-box;
  background: url(../images/icon_04.png) 393px 12px no-repeat;
}
.order-info .class_con input, .order-info .class_con textarea{
  width: 400px;
}
.order-info .class_con .input-tips{
  width: 400px;
  display: block;
  margin-left: 136px;
  height: 45px;
}
.order-info .class_con span.must-input{
  margin-left: 5px;
}
.order-info .de_y{
  width: 250px;
  margin: auto;
}

.cnt-information {
  margin-bottom: 20px;
  border-bottom: 1px solid #E1E7F1;
  padding-bottom: 15px;
}
.cnt-title {
  font-size: 14px;
  color: #333;
  margin-bottom: 20px;
}
.cnt-title::before {
  content: '';
  display: inline-block;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: #0f2950;
  vertical-align: middle;
  margin-right: 10px;
}
.cnt-basic-list {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  margin-bottom: 10px;
}
.cnt-basic-list .cnt-basic-item {
  margin-right: 10px;
}
.cnt-basic-item .cnt-basic-i {
  display: flex;
  flex-direction: row;
  align-items: center;
  position: relative;
  margin-bottom: 10px;
}
.order-info .cnt-basic-item .cnt-basic-i{
    margin-bottom: 15px;
}
.order-info .cnt-basic-item .cnt-basic-i .url-address{
    margin-left: 136px;
    margin-top: -30px
}
.input-readonly {
    background-color: #CCCCCC !important;
}
.cnt-basic-l {
    max-width: 65%;
}
.cnt-basic-f {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
}
.cnt-label {
    padding: 0px 8px;
    height: 30px;
    border: 0;
    border-radius: 5px;
    cursor: pointer;
    margin-right: 10px;
    font-size: 14px;
    font-weight: 500;
    background-color: #0f2950;
    color: #fff;
}
.cnt-label.add-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 130px;
}
.class-table {
    table-layout: fixed;
    width: 100%;
    border-left: 1px solid #EFEFEF;
}
.class-table>thead {
    background-color: #E6E9F0;
}
.class-table>thead>tr>th {
    font-size: 1rem;
    font-weight: 500;
    height: 40px;
    border-right: 1px solid #EFEFEF;
}
.class-table>tbody>tr>td {
    white-space: normal;
    font-size: 14px;
}
.class-table>tbody>tr>td, .class-table>tfoot>tr>td {
    border-right: 1px solid #EFEFEF;
    border-bottom: 1px solid #EFEFEF;
    text-align: center;
    padding: 10px;
    color: #666;
    word-wrap: break-word;
}
.add-layer{
  font-size: 24px;
  font-weight: bold;
  margin-right: 5px;
}
.cnt-basic-layer{
  display: none;
  margin: 20px 30px;
}
.cnt-label.layer-close-btn{
    background-color: #f4f8fa;
    color: #474545;
    border: 1px solid #c3c0c0;
}
.cnt-basic-layer .cnt-label{
  width: 90px;
  height: 36px;
}
.cnt-basic-layer.cnt-basic-list .cnt-basic-item{
  margin-right:0;
}
.cnt-basic-layer.order-info .de_y{
    width: 205px;
}
.class-table .button{
  cursor: pointer;
}

.change-files .class_con span{
    margin-left: 0;
}

.change-files .class_con span.selecttype {
    padding: 5px 20px;
    margin-right: 10px;
    cursor: pointer;
    border: 1px solid #ddd;
    border-radius: 3px;
    display: inline-block;
    color: #3a3a3a;
}

.change-files .class_con span.selecttype.active {
    background-color: #337ab7;
    color: white;
    border-color: #2e6da4;
}

.services-row .class-table>thead{
  background-color: #d2e4f5;
}
.services-row .class-table>tbody{
  background-color: #f3f9ff;
}

.manag-radio-list{
  margin-left: 20px;
}
.manag-radio-item.child{
  margin-left: 20px;
}
.manag-radio-item.child2{
  margin-left: 40px;
}

/* 顶部搜索 */
.maps .search-container {
    display: flex;
    max-width: 500px;
    float: right;
    margin-top: 6px;
    margin-right: 50px;
}
.maps .search-input {
    flex: 1;
    padding: 8px 10px;
    border: 1px solid #ddd;
    border-radius: 4px 0 0 4px;
    font-size: 14px;
    outline: none;
}
.maps .search-input:focus {
    border-color: #409EFF;
}
.maps .search-button {
    padding: 7px 20px;
    background-color: #409EFF;
    color: white;
    border: none;
    border-radius: 0 4px 4px 0;
    cursor: pointer;
    font-size: 14px;
}
.maps .search-button:hover {
    background-color: #66B1FF;
}

/* 标签多选 */
.multi-select-display {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    cursor: pointer;
    position: relative;
    width: 530px;
    background: url(../images/icon_04.png) 523px 8px no-repeat;
    outline: none;
    -webkit-appearance: none;
    border: 1px solid #d8d8d8;
    padding: 10px;
    border-radius: 4px;
}
.class_con input.multi-select-input {
    border: none;
    outline: none;
    flex: 1;
    background: transparent;
    cursor: pointer;
    padding: 0;
    height: inherit;
    width: inherit;
}
.class_con span.multi-select-tag {
    background: #f0f0f0;
    color: #1890ff;
    border-radius: 0.125rem;
    padding: 0 0.375rem;
    display: flex;
    align-items: center;
}
.multi-select-tag .close {
    margin-left: 0.125rem;
    cursor: pointer;
    color: #999;
}
.multi-select-dropdown {
    left: 0;
    width: 550px;
    background: #fff;
    border: 1px solid #e6e6e6;
    border-radius: 0.25rem;
    box-shadow: 0 0.125rem 0.5rem rgba(0, 0, 0, 0.08);
    z-index: 10;
    max-height: 12.5rem;
    overflow-y: auto;
}
.multi-select-option {
    padding: 0.5rem 1rem;
    cursor: pointer;
}
.multi-select-option.selected {
    color: #1890ff;
    background: #e6f7ff;
}