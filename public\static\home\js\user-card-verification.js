/**
 * 用户信息卡片功能脚本
 * 确保所有用户头像都具有用户信息卡片功能
 */

window.UserCardVerification = (function() {
    'use strict';

    /**
     * 自动修复缺失的用户信息卡片配置
     */
    function autoFixUserAvatars() {
        let fixedCount = 0;
        
        $('.iCommunity-left').each(function(index) {
            const $container = $(this);
            let needsFix = false;
            
            // 添加缺失的CSS类
            if (!$container.hasClass('user-avatar-container')) {
                $container.addClass('user-avatar-container');
                needsFix = true;
            }
            
            // 确定是主评论还是子评论
            const isSubComment = $container.closest('.comment-reply-item').length > 0;
            const avatarType = isSubComment ? 'sub-comment-avatar' : 'main-comment-avatar';
            
            if (!$container.hasClass(avatarType)) {
                $container.addClass(avatarType);
                needsFix = true;
            }
            
            // 添加data-user-id属性
            if (!$container.attr('data-user-id')) {
                $container.attr('data-user-id', index + 1); // 临时ID
                needsFix = true;
            }
            
            // 修复触发器类
            const $trigger = $container.find('.w-full.h-full');
            if ($trigger.length && !$trigger.hasClass('user-avatar-trigger')) {
                $trigger.addClass('user-avatar-trigger');
                needsFix = true;
            }
            
            // 添加用户信息卡片
            let $userCard = $container.find('.user-info-card');
            if ($userCard.length === 0) {
                // 查找现有的信息卡片容器
                $userCard = $container.find('.iCommunity-left-info');
                if ($userCard.length) {
                    $userCard.addClass('user-info-card');
                    // 更新z-index和样式
                    $userCard.css({
                        'z-index': '50',
                        'min-width': '300px'
                    });
                    if (window.innerWidth >= 768) {
                        $userCard.css('min-width', '400px');
                    }
                    needsFix = true;
                }
            }
            
            if (needsFix) {
                fixedCount++;
            }
        });

        // 重新初始化事件处理器
        if (typeof EventHandler !== 'undefined') {
            EventHandler.init();
        }
        
        return fixedCount;
    }



    /**
     * 生成用户信息卡片内容（用于缺失的卡片）
     */
    function generateUserCardContent(userData = {}) {
        const defaultUser = {
            username: userData.username || '用户',
            userLevel: userData.userLevel || '普通用户',
            avatar: userData.avatar || '/images/user.jpg',
            stats: userData.stats || {
                questions: 0,
                posts: 0,
                replies: 0
            }
        };
        
        return `
            <div class="bg-[#fafbff] rounded-xl border border-[#dae9ff]" style="box-shadow: 0 0 1.25rem -0.125rem rgba(84, 111, 138, 0.3);">
                <div class="flex items-start gap-x-2 mb-3 md:mb-0 md:gap-x-8 p-4 md:p-10">
                    <div class="w-[2.5rem] h-[2.5rem] md:w-[8.125rem] md:h-[8.125rem] flex-shrink-0">
                        <img src="${defaultUser.avatar}" alt="${defaultUser.username}" class="w-full h-full rounded-md object-cover">
                    </div>
                    <div class="flex flex-col gap-y-2 md:flex-1">
                        <div class="flex w-full gap-x-2.5 md:gap-x-8 items-end">
                            <div class="name-info">
                                <a href="#" class="text-base md:text-3xl font-bold">${defaultUser.username}</a>
                                <p class="text-xs text-nowrap md:text-xl text-gray-500">${defaultUser.userLevel}</p>
                            </div>
                            <div class="message-btn">
                                <a href="#" class="text-sm px-2 py-1 md:text-xl bg-blue-600 text-white text-nowrap rounded-md md:px-4 md:py-2">
                                    Private message
                                </a>
                            </div>
                        </div>
                        <div class="mt-3 px-2 md:px-0 md:mt-5">
                            <ul class="text-sm justify-between md:text-2xl flex items-center gap-x-5 md:gap-x-10 text-center md:justify-start">
                                <li><span>${defaultUser.stats.questions}</span><p class="text-blue-600">Questions</p></li>
                                <li><span>${defaultUser.stats.posts}</span><p class="text-blue-600">Posts</p></li>
                                <li><span>${defaultUser.stats.replies}</span><p class="text-blue-600">Reply</p></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 完整的初始化和验证流程
     */
    function initializeAndVerify() {
        // 自动修复用户头像配置
        autoFixUserAvatars();
    }

    // 公开API
    return {
        autoFixUserAvatars,
        generateUserCardContent,
        initializeAndVerify
    };
})();

// 页面加载完成后自动运行验证
$(document).ready(function() {
    // 延迟执行，确保所有模块都已加载
    setTimeout(function() {
        // 自动运行完整验证
        UserCardVerification.initializeAndVerify();
    }, 1000);
});
