<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0,maximum-scale=1.0,minimum-scale=1.0,user-scalable=no">
    <title>Points - HitGen OpenDEL™</title>
    <link rel="shortcut icon" href="/favicon.ico" />
    <link href="__CSS__/vendors/aos.css" rel="stylesheet">
    <link href="__CSS__/style.css" rel="stylesheet" />
</head>

<body>
    <section
        class="pb-5 md:pb-20 relative bg-[url(__IMG__/bg.jpg)] min-h-lvh">
        <span class="absolute top-0 left-0 w-full h-full bg-[url(__IMG__/tm-left.png)] bg-no-repeat bg-[top_left] z-0 bg-size-[10rem] md:bg-size-[26.9375rem]"></span>
        <span class="absolute top-0 right-0 w-full h-full bg-[url(__IMG__/tm-right.png)] bg-no-repeat bg-[top_right] z-0 bg-size-[10rem] md:bg-size-[25.0625rem]"></span>

        {include file="public:header"}

        <div class="w-11/12 mx-auto md:w-10/12">
            <div class="mt-5 Navigation mb-3.5 md:mb-14">
                <ul class="text-[#999] flex items-center text-md md:text-xl">
                    <li>
                        <a href="/" class="text-[#155797]">
                            Home
                        </a>
                    </li>
                    <li class="max-w-[12.5rem] line-clamp-1 md:max-w-2xs">
                        Personal Center
                    </li>
                </ul>
            </div>

            <!-- data-aos="fade-up" -->
            <div class="md:flex md:flex-row md:gap-x-4">

                {include file="user:left"}

                <div class="bg-white border border-[var(--border-color)] rounded-lg shadow-2xs
                md:w-2/3 md:rounded-2xl
                ">
                    <div
                        class="grid grid-cols-2 text-sm md:text-[1.5625rem] md:px-[1.875rem] md:pt-4 md:flex Roboto_Bold md:text-2xl md:gap-x-[4.375rem] user-tab-link">
                        <span class="user-tab-item active">
                            HITGEN Points
                        </span>
                    </div>
                    <div class="user-profile">
                        <div class="profile-item">
                            <div class="p-4 md:p-[1.875rem]">
                                <div class="bg-[#dae9ff] rounded-tl-xl rounded-tr-xl min-h-[5.625rem] bg-[url(__IMG__/backgrounds/points-bg.png)] bg-no-repeat bg-[90%_bottom] bg-size-[40%] flex flex-col items-start justify-center gap-y-2 pl-5
                                md:h-[11.25rem] md:bg-[90%_bottom] md:bg-size-[15.6875rem] Roboto_Bold md:pl-[3.125rem]
                                ">
                                    <span class="bg-[rgba(255,255,255,.5)] rounded-full py-1 px-3 text-xs
                                    md:w-[7.5rem] md:h-[2.5rem] md:text-xl md:text-center md:leading-[2.5rem] md:py-0
                                    ">Points</span>
                                    <div class="text-[#155797] text-3xl md:text-[3.75rem]">
                                        {$user.points ?? 0}
                                    </div>
                                </div>
                                <div class="my-4 Roboto_Bold md:mt-[3.75rem] md:mb-[1.875rem] md:text-3xl">
                                    Points Details
                                </div>
                                <div class="Points-list">
                                    <ul>
                                        {volist name="points" id="vo"}
                                        <li>
                                            <span>
                                                {:date('F j, Y, g:i A', strtotime($vo.create_time))}
                                            </span>
                                            <p>
                                                {$vo.content}
                                                {if $vo.points>0}
                                                <span class="text-[#f08411]">+{$vo.points}</span>
                                                {else }
                                                <span class="text-[#155797]">{$vo.points}</span>
                                                {/if}
                                                 points
                                            </p>
                                        </li>
                                        {/volist}
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>

        </div>
    </section>

    {include file="public:footer"}

    {include file="public:foot"}

</body>

</html>