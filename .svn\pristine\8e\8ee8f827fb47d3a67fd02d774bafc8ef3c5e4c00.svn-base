<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
    <title>添加用户</title>

    {include file="common:head"}
</head>

<body>
    <div class="menus_r">
        <div class="maps">
            <i></i>
            <p class="current">您当前的位置：后台管理&nbsp;>&nbsp;添加用户
                <a href="{:url('index')}" class="de_y_r" >返回</a>
            </p>
        </div>

        <div class="clas_box">
            <form action="{:url('add')}" method="post" enctype="multipart/form-data" id="formId" autocomplete="off">
                <div class="class_con">
                    <label>Email：</label>
                    <input type="text" name="email" />
                    <span class="must-input">*</span>
                </div>

                <div class="class_con">
                    <label>Password：</label>
                    <input name="password" type="password" minlength="8" maxlength="20" /> <span class="input-tips">密码长度8-20位，必须包含大小写字母、数字和特殊字符</span>
                    <span class="must-input">*</span>
                </div>

                <div class="class_con">
                    <label>Country/Region：</label>
                    <select name="country" class="l_xiang">
                        <option value="">
                            Please select your country/region
                        </option>
                        {volist name="country" id="vo"}
                        <option value="{$vo.en_name}">{$vo.en_name} {$vo.cn_name}</option>
                        {/volist}
                    </select>
                    <span class="must-input">*</span>
                </div>

                <div class="class_con">
                    <label>First Name：</label>
                    <input type="text" name="first_name" />
                    <span class="must-input">*</span>
                </div>

                <div class="class_con">
                    <label>Last Name：</label>
                    <input type="text" name="last_name" />
                    <span class="must-input">*</span>
                </div>

                <!-- <div class="class_con">
                    <label>昵称：</label>
                    <input type="text" name="nickname" />
                </div> -->

                <div class="class_con">
                    <label>Organization/Institution/Corporation：</label>
                    <input type="text" name="organization" />
                    <span class="must-input">*</span>
                </div>

                <div class="class_con">
                    <label>Title：</label>
                    <input type="text" name="title" />
                </div>

                <div class="class_con">
                    <label>角色：</label>
                    <select name="role_id" class="l_xiang">
                        {volist name="roles" id="vo"}
                        <option value="{$vo.id}">{$vo.name}</option>
                        {/volist}
                    </select>
                    <span class="must-input">*</span>
                </div>

                <div class="class_con">
                    <label>状态：</label>
                    <label class="switch">
                        <input type="checkbox" checked class="toggle-switch" data-target="status">
                        <span class="toggle-slider"></span>
                        <input type="hidden" name="status" value="1">
                    </label>
                </div>

                <div class="de_y">
                    <button class="de_y_l" type="submit" id="submitBtn">确定</button>
                    <a href="{:url('index')}" class="de_y_r" >返回</a>
                </div>
            </form>
        </div>
    </div>

    {include file="common:foot"}
</body>
</html>