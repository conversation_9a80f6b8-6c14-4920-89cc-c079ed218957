<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
    <title>创建订单</title>

    {include file="common:head"}
</head>

<body>
    <div class="cnt-basic-list cnt-basic-layer order-info" style="display: block;">
        <form action="{:url('edit_service')}" method="post" enctype="multipart/form-data" class="layer-form" autocomplete="off">
            <input type="hidden" name="id" value="{$getone.id}" />

            <div class="cnt-basic-item">
	        	<div class="cnt-basic-i class_con">
	                <label>选择服务：</label>
                    <select name="service_id" id="service_id" class="l_xiang">
                        <option value="">请选择</option>
                        {volist name="service" id="vo"}
                            <option value="{$vo.id}" {if $getone.service_id==$vo.id}selected{/if}>{$vo.name}</option>
                        {/volist}
                    </select>
	            </div>

                <div class="cnt-basic-i class_con">
                    <label>订单金额：</label>
                    <input type="text" name="money" value="{$getone.money}" />
                    <span class="must-input">*</span>
                </div>

                <div class="cnt-basic-i class_con">
                    <label>服务进度：</label>
                    <select name="service_progress_id" id="service_progress_id" class="l_xiang">
                        <option value="">请选择</option>
                        {volist name="service_progress" id="vo"}
                            <option value="{$vo.id}" {if $getone.service_progress_id==$vo.id}selected{/if}>{$vo.name}</option>
                        {/volist}
                    </select>
                    <span class="must-input">*</span>
                </div>

                <div class="de_y">
                    <button type="button" class="layer-close-btn cnt-label">取消</button>
                    <button type="button" class="layer-submit-btn cnt-label">提交</button>
                </div>
	        </div>
        </form>
    </div>

    {include file="common:foot_layer"}

    <script>
        //服务变化，服务进度列表变化
        $('#service_id').on('change', function() {
            var serviceId = $(this).val();
            if (!serviceId) return; // 如果没有选择 product，直接返回

            // 发送 AJAX 请求获取 services
            $.ajax({
                url: "/admin/Order/getServiceProgress",
                type: 'GET',
                data: {service_id: serviceId},
                dataType: 'json',
                success: function(response) {
                    if (response.code === 1) {
                        //更新服务进度列表
                        var $serviceSelect = $('#service_progress_id');
                        $serviceSelect.empty();
                        $serviceSelect.append('<option value="" disabled selected>请选择</option>');
                        $.each(response.data, function(index, service) {
                            $serviceSelect.append(
                                '<option value="' + service.id + '">' + service.name + '</option>'
                            );
                        });
                        $serviceSelect.css('color', '#333');
                    }
                },
                error: function(xhr, status, error) {
                    console.error("AJAX 请求失败:", error);
                    $('#service').html('<option value="">加载失败，请重试</option>');
                }
            });
        });
    </script>
</body>
</html>